# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Test Commands
- **Build**: `./gradlew build`
- **Run unit tests**: `./gradlew test`
- **Run instrumented tests**: `./gradlew connectedAndroidTest`
- **<PERSON><PERSON> and check Kotlin code**: `./gradlew lint ktlintCheck`

## Architecture Overview
- **Compose UI**: The project uses Jetpack Compose for UI components, with Material 3 styling.
- **Room Database**: For local persistence, Room is used with KSP for compile-time code generation.
- **Ktor for Networking**: <PERSON><PERSON> is integrated for HTTP networking with <PERSON><PERSON> as the engine.
- **DexKit**: Used for dynamic code analysis and manipulation.
- **Xposed Integration**: The app includes Xposed framework hooks for runtime modifications.

## Key Dependencies
- **AndroidX**: Core libraries for modern Android development.
- **KSP**: Kotlin Symbol Processing for compile-time code generation.
- **Coil**: Image loading library for Compose.
- **OkHttp**: HTTP client for networking.
- **Gson**: JSON parsing library.