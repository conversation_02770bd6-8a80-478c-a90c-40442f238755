package dev.pigmomo.yhkit2025

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.lifecycle.ViewModelProvider
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.repository.LogRepositoryImpl
import dev.pigmomo.yhkit2025.data.repository.PhoneWithPasswordRepositoryImpl
import dev.pigmomo.yhkit2025.data.repository.TokenRepositoryImpl
import dev.pigmomo.yhkit2025.service.BackgroundService
import dev.pigmomo.yhkit2025.ui.screens.LoginScreen
import dev.pigmomo.yhkit2025.ui.theme.Yhkit2025Theme
import dev.pigmomo.yhkit2025.viewmodel.LoginViewModel

/**
 * 登录账号管理Activity
 * 用于管理永辉生活APP的登录令牌
 */
class LoginActivity : ComponentActivity() {
    
    // 登录视图模型
    private lateinit var viewModel: LoginViewModel
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化数据库、仓库和ViewModel
        val database = AppDatabase.getDatabase(applicationContext)
        val loginTokenDao = database.loginTokenDao()
        val logDao = database.logDao()
        val phoneWithPasswordDao = database.phoneWithPasswordDao()
        
        // 创建仓库实例
        val tokenRepository = TokenRepositoryImpl(loginTokenDao = loginTokenDao)
        val logRepository = LogRepositoryImpl(logDao = logDao)
        val phoneWithPasswordRepository = PhoneWithPasswordRepositoryImpl(phoneWithPasswordDao = phoneWithPasswordDao)
        
        // 创建ViewModel实例
        viewModel = ViewModelProvider(
            this,
            LoginViewModel.Factory(tokenRepository, phoneWithPasswordRepository, application)
        )[LoginViewModel::class.java]
        
        // 注入日志仓库
        viewModel.setLogRepository(logRepository)
        
        // 设置UI内容
        setContent {
            Yhkit2025Theme(dynamicColor = false) {
                LoginScreen(viewModel)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        
        // 重置后台服务的注册信息
        BackgroundService.resetRegistrationInfo()
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        super.onBackPressed()
        viewModel.setFloatingWindowShown(false)
        BackgroundService.toggleFloatingWindow(this, false)
    }
}
