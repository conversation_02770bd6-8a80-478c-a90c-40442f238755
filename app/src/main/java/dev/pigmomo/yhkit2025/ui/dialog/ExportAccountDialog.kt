package dev.pigmomo.yhkit2025.ui.dialog

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import dev.pigmomo.yhkit2025.data.model.LoginTokenEntity
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor

/**
 * 导出筛选选项 LoginTokenEntity
 * @property displayName 显示名称
 */
enum class ExportFilterOptionForLoginToken(val displayName: String) {
    ALL("全部"),
    NON_NEWCOMER("非新人"),
    NEWCOMER_PRIVILEGE("新人特权")
}


/**
 * 导出筛选选项 OrderTokenEntity
 * @property displayName 显示名称
 */
enum class ExportFilterOptionForOrderToken(val displayName: String) {
    ALL("全部"),
    NEWCOMER("新人"),
    NON_NEWCOMER("非新人"),
    ACCOUNT_VALID("账号有效"),
    ACCOUNT_INVALID("账号无效"),
    YH_CARD_NOT_LIMITED("永辉卡非限制"),
    YH_CARD_LIMITED("永辉卡受限"),
    ACTIVITY_NOT_LIMITED("活动非限制"),
    ACTIVITY_LIMITED("活动受限"),
    CREDIT_LESS_THAN_2500("积分<2500"),
    CREDIT_GREATER_THAN_EQUAL_2500("积分>=2500"),
    CARD_BALANCE_GREATER_THAN_0("余额>0"),
    CARD_BALANCE_EQUAL_0("余额=0"),
}

/**
 * 导出账号筛选对话框 LoginTokenEntity
 * @param onDismiss 对话框关闭回调
 * @param onConfirm 确认导出回调，携带筛选选项
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExportLoginAccountDialog(
    onDismiss: () -> Unit,
    onConfirm: (ExportFilterOptionForLoginToken) -> Unit,
    tokens: List<LoginTokenEntity>
) {
    var selectedOption by remember { mutableStateOf(ExportFilterOptionForLoginToken.ALL) }

    val tokensToExportCount by remember(selectedOption) {
        mutableStateOf(
            when (selectedOption) {
                ExportFilterOptionForLoginToken.ALL -> tokens.size
                ExportFilterOptionForLoginToken.NON_NEWCOMER -> tokens.filter { "新人特权" != it.extraNote }.size
                ExportFilterOptionForLoginToken.NEWCOMER_PRIVILEGE -> tokens.filter { "新人特权" == it.extraNote }.size
            }
        )
    }

    AlertDialog(
        containerColor = dialogContainerColor(),
        onDismissRequest = onDismiss,
        title = { Text("选择导出类型") },
        text = {
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    ExportFilterOptionForLoginToken.entries.forEach { option ->
                        FilterChip(
                            selected = selectedOption == option,
                            onClick = {
                                selectedOption = option
                            },
                            label = { Text(option.displayName) },
                            colors = FilterChipDefaults.filterChipColors(
                                selectedContainerColor = CardContainerColor
                            )
                        )
                    }
                }
            }
        },
        confirmButton = {
            Button(onClick = { onConfirm(selectedOption) }, enabled = tokensToExportCount > 0) {
                Text("确认($tokensToExportCount)")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 多选筛选条件
 * 用于存储多个筛选条件的数据类
 */
data class MultiFilterOptions(
    val isNewcomer: Boolean? = null, // true=新人，false=非新人，null=不筛选新人状态
    val accountValid: Boolean? = null, // true=账号有效，false=账号无效，null=不筛选账号状态
    val yhCardLimitedStatus: Boolean? = null, // true=永辉卡受限，false=永辉卡非限制，null=不筛选永辉卡状态
    val activityLimitedStatus: Boolean? = null, // true=活动受限，false=活动非限制，null=不筛选活动状态
    val creditLessThan2500: Boolean? = null,
    val creditGreaterThanEqual2500: Boolean? = null,
    val cardBalanceStatus: Boolean? = null // true=余额=0，false=余额>0，null=不筛选余额状态
)

/**
 * 导出账号筛选对话框 OrderTokenEntity
 * 支持多选筛选条件
 * @param onDismiss 对话框关闭回调
 * @param onConfirm 确认导出回调，携带多选筛选条件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExportOrderAccountDialog(
    onDismiss: () -> Unit,
    onConfirm: (MultiFilterOptions, Boolean) -> Unit,
    addParamsToNote: Boolean,
    tokens: List<OrderTokenEntity>
) {
    // 使用MultiFilterOptions来跟踪选择状态
    var filterOptions by remember { mutableStateOf(MultiFilterOptions()) }

    /**
     * 是否添加参数到备注
     */
    var tempAddParamsToNote by remember { mutableStateOf(addParamsToNote) }

    // 判断是否选择了"全部"选项
    val isAllSelected by remember(filterOptions) {
        derivedStateOf {
            filterOptions.isNewcomer == null &&
                    filterOptions.accountValid == null &&
                    filterOptions.yhCardLimitedStatus == null &&
                    filterOptions.activityLimitedStatus == null &&
                    filterOptions.creditLessThan2500 == null &&
                    filterOptions.creditGreaterThanEqual2500 == null &&
                    filterOptions.cardBalanceStatus == null
        }
    }

    // 计算符合条件的令牌数量
    val tokensToExportCount by remember(filterOptions) {
        derivedStateOf {
            if (isAllSelected) {
                tokens.size
            } else {
                tokens.filter { token ->
                    // 新人/非新人筛选
                    val passesNewcomerFilter = when (filterOptions.isNewcomer) {
                        true -> token.isNew
                        false -> !token.isNew
                        null -> true // 不筛选新人状态
                    }

                    // 积分筛选
                    val passesCreditFilter = when {
                        filterOptions.creditLessThan2500 == true -> token.credit < 2500
                        filterOptions.creditGreaterThanEqual2500 == true -> token.credit >= 2500
                        else -> true // 不筛选积分状态
                    }

                    // 账号状态筛选
                    val passesAccountFilter = when (filterOptions.accountValid) {
                        true -> token.isLogin // 账号有效
                        false -> !token.isLogin // 账号无效
                        null -> true // 不筛选账号状态
                    }
                    
                    // 永辉卡限制状态筛选
                    val passesYhCardFilter = when (filterOptions.yhCardLimitedStatus) {
                        true -> token.yhCardLimited // 永辉卡受限
                        false -> !token.yhCardLimited // 永辉卡非限制
                        null -> true // 不筛选永辉卡状态
                    }
                    
                    // 活动限制状态筛选
                    val passesActivityFilter = when (filterOptions.activityLimitedStatus) {
                        true -> token.activityLimited // 活动受限
                        false -> !token.activityLimited // 活动非限制
                        null -> true // 不筛选活动状态
                    }
                    
                    // 余额状态筛选
                    val passesCardBalanceFilter = when (filterOptions.cardBalanceStatus) {
                        true -> token.cardBalance == 0f // 余额=0
                        false -> token.cardBalance > 0 // 余额>0
                        null -> true // 不筛选余额状态
                    }

                    // 所有条件都必须满足
                    passesNewcomerFilter && 
                    passesAccountFilter && 
                    passesYhCardFilter && 
                    passesActivityFilter && 
                    passesCreditFilter && 
                    passesCardBalanceFilter
                }.size
            }
        }
    }

    AlertDialog(
        containerColor = dialogContainerColor(),
        onDismissRequest = onDismiss,
        title = { Text("选择导出类型") },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row {
                        TokenActionButton(
                            imageVector = Icons.Filled.Edit,
                            text = "添加备注",
                            onClick = {
                                tempAddParamsToNote = !tempAddParamsToNote
                            },
                            tint = if (!tempAddParamsToNote) Color(0xFF48454E).copy(alpha = 0.5f) else Color(
                                0xFF48454E
                            )
                        )
                    }
                }

                // 第一行：ALL、NEWCOMER、NON_NEWCOMER（互斥选择）
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .horizontalScroll(rememberScrollState()),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 全部选项
                    FilterChip(
                        selected = isAllSelected,
                        onClick = {
                            // 选择"全部"时，清除其他所有筛选条件
                            filterOptions = MultiFilterOptions()
                        },
                        label = { Text(ExportFilterOptionForOrderToken.ALL.displayName) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = CardContainerColor
                        )
                    )

                    // 新人选项
                    FilterChip(
                        selected = filterOptions.isNewcomer == true,
                        onClick = {
                            filterOptions = filterOptions.copy(
                                isNewcomer = if (filterOptions.isNewcomer == true) null else true
                            )
                        },
                        label = { Text(ExportFilterOptionForOrderToken.NEWCOMER.displayName) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = CardContainerColor
                        )
                    )

                    // 非新人选项
                    FilterChip(
                        selected = filterOptions.isNewcomer == false,
                        onClick = {
                            filterOptions = filterOptions.copy(
                                isNewcomer = if (filterOptions.isNewcomer == false) null else false
                            )
                        },
                        label = { Text(ExportFilterOptionForOrderToken.NON_NEWCOMER.displayName) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = CardContainerColor
                        )
                    )
                }

                // 第二行：账号状态（互斥选择）
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .horizontalScroll(rememberScrollState()),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 账号有效选项
                    FilterChip(
                        selected = filterOptions.accountValid == true,
                        onClick = {
                            filterOptions = filterOptions.copy(
                                accountValid = if (filterOptions.accountValid == true) null else true
                            )
                        },
                        label = { Text(ExportFilterOptionForOrderToken.ACCOUNT_VALID.displayName) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = CardContainerColor
                        )
                    )

                    // 账号无效选项
                    FilterChip(
                        selected = filterOptions.accountValid == false,
                        onClick = {
                            filterOptions = filterOptions.copy(
                                accountValid = if (filterOptions.accountValid == false) null else false
                            )
                        },
                        label = { Text(ExportFilterOptionForOrderToken.ACCOUNT_INVALID.displayName) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = CardContainerColor
                        )
                    )
                }
                
                // 第三行：永辉卡状态（互斥选择）
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .horizontalScroll(rememberScrollState()),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 永辉卡非限制选项
                    FilterChip(
                        selected = filterOptions.yhCardLimitedStatus == false,
                        onClick = {
                            filterOptions = filterOptions.copy(
                                yhCardLimitedStatus = if (filterOptions.yhCardLimitedStatus == false) null else false
                            )
                        },
                        label = { Text(ExportFilterOptionForOrderToken.YH_CARD_NOT_LIMITED.displayName) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = CardContainerColor
                        )
                    )

                    // 永辉卡受限选项
                    FilterChip(
                        selected = filterOptions.yhCardLimitedStatus == true,
                        onClick = {
                            filterOptions = filterOptions.copy(
                                yhCardLimitedStatus = if (filterOptions.yhCardLimitedStatus == true) null else true
                            )
                        },
                        label = { Text(ExportFilterOptionForOrderToken.YH_CARD_LIMITED.displayName) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = CardContainerColor
                        )
                    )
                }
                
                // 第四行：活动状态（互斥选择）
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .horizontalScroll(rememberScrollState()),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 活动非限制选项
                    FilterChip(
                        selected = filterOptions.activityLimitedStatus == false,
                        onClick = {
                            filterOptions = filterOptions.copy(
                                activityLimitedStatus = if (filterOptions.activityLimitedStatus == false) null else false
                            )
                        },
                        label = { Text(ExportFilterOptionForOrderToken.ACTIVITY_NOT_LIMITED.displayName) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = CardContainerColor
                        )
                    )

                    // 活动受限选项
                    FilterChip(
                        selected = filterOptions.activityLimitedStatus == true,
                        onClick = {
                            filterOptions = filterOptions.copy(
                                activityLimitedStatus = if (filterOptions.activityLimitedStatus == true) null else true
                            )
                        },
                        label = { Text(ExportFilterOptionForOrderToken.ACTIVITY_LIMITED.displayName) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = CardContainerColor
                        )
                    )
                }

                // 第五行：积分筛选（互斥选择）
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .horizontalScroll(rememberScrollState()),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 积分<2500选项
                    FilterChip(
                        selected = filterOptions.creditLessThan2500 == true,
                        onClick = {
                            filterOptions = filterOptions.copy(
                                creditLessThan2500 = if (filterOptions.creditLessThan2500 == true) null else true,
                                creditGreaterThanEqual2500 = null
                            )
                        },
                        label = { Text(ExportFilterOptionForOrderToken.CREDIT_LESS_THAN_2500.displayName) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = CardContainerColor
                        )
                    )

                    // 积分>=2500选项
                    FilterChip(
                        selected = filterOptions.creditGreaterThanEqual2500 == true,
                        onClick = {
                            filterOptions = filterOptions.copy(
                                creditGreaterThanEqual2500 = if (filterOptions.creditGreaterThanEqual2500 == true) null else true,
                                creditLessThan2500 = null
                            )
                        },
                        label = { Text(ExportFilterOptionForOrderToken.CREDIT_GREATER_THAN_EQUAL_2500.displayName) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = CardContainerColor
                        )
                    )
                }
                
                // 第六行：余额状态（互斥选择）
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .horizontalScroll(rememberScrollState()),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 余额>0选项
                    FilterChip(
                        selected = filterOptions.cardBalanceStatus == false,
                        onClick = {
                            filterOptions = filterOptions.copy(
                                cardBalanceStatus = if (filterOptions.cardBalanceStatus == false) null else false
                            )
                        },
                        label = { Text(ExportFilterOptionForOrderToken.CARD_BALANCE_GREATER_THAN_0.displayName) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = CardContainerColor
                        )
                    )

                    // 余额=0选项
                    FilterChip(
                        selected = filterOptions.cardBalanceStatus == true,
                        onClick = {
                            filterOptions = filterOptions.copy(
                                cardBalanceStatus = if (filterOptions.cardBalanceStatus == true) null else true
                            )
                        },
                        label = { Text(ExportFilterOptionForOrderToken.CARD_BALANCE_EQUAL_0.displayName) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = CardContainerColor
                        )
                    )
                }
            }
        },
        confirmButton = {
            Button(
                onClick = { onConfirm(filterOptions, tempAddParamsToNote) },
                enabled = tokensToExportCount > 0
            ) {
                Text("确认($tokensToExportCount)")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}