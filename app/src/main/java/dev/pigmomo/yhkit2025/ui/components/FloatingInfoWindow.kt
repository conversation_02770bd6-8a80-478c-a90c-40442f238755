package dev.pigmomo.yhkit2025.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandHorizontally
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkHorizontally
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor
import kotlin.math.tan
import kotlinx.coroutines.delay

/**
 * 悬浮信息窗口，用于显示动态信息。
 * 样式与自动注册状态指示器一致。
 *
 * @param info 要在窗口中显示的文本信息。
 */
@Composable
fun FloatingInfoWindow(
    info: String,
) {
    // 文本显示状态
    val isTextVisible = remember { mutableStateOf(true) }

    // 监听info变化，重置显示状态并启动15秒倒计时
    LaunchedEffect(info) {
        isTextVisible.value = true
        delay(5000) // 5秒后收起文本
        isTextVisible.value = false
    }

    // 无限循环过渡动画
    val infiniteTransition = rememberInfiniteTransition(label = "floating_info_animation")

    // 旋转动画
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )

    // 滑动动画进度
    val slideProgress by infiniteTransition.animateFloat(
        initialValue = -0.3f,
        targetValue = 1.3f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "slide_progress"
    )

    // 主容器，应用背景、边框和内边距
    Box(
        modifier = Modifier
            .background(
                color = CardContainerColor,
                shape = RoundedCornerShape(12.dp)
            )
            .border(
                width = 1.dp,
                color = Color(0xFFFFD700).copy(alpha = 0.5f),
                shape = RoundedCornerShape(12.dp)
            )
            .drawBehind {
                val gradientWidth = size.width * 0.5f
                val startX = size.width * slideProgress
                val cornerRadius = 12.dp.toPx()

                // 倾斜角度（度数）
                val angleDegrees = 15f
                // 转换为弧度并计算斜率
                val slope = tan(Math.toRadians(angleDegrees.toDouble())).toFloat()

                // 计算倾斜的起点和终点
                val heightOffset = size.height * slope
                val startY = size.height / 2 - heightOffset / 2
                val endY = size.height / 2 + heightOffset / 2

                drawRoundRect(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            Color.Transparent,
                            Color(0xFFFFD700).copy(alpha = 0.3f),
                            Color(0xFF00BCD4).copy(alpha = 0.3f),
                            Color(0xFF9C27B0).copy(alpha = 0.3f),
                            Color.Transparent
                        ),
                        start = Offset(startX - gradientWidth, startY),
                        end = Offset(startX, endY)
                    ),
                    cornerRadius = CornerRadius(cornerRadius, cornerRadius)
                )
            }
            .padding(horizontal = 12.dp, vertical = 6.dp)
    ) {
        // 水平排列图标和文本
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 旋转的同步图标 - 始终显示
            Icon(
                painter = painterResource(id = R.drawable.outline_sync_24),
                contentDescription = null,
                tint = Color(0xFFFFD700),
                modifier = Modifier
                    .size(20.dp)
                    .graphicsLayer { rotationZ = rotation }
            )

            // 动画容器，用于文本的展开和收起
            AnimatedVisibility(
                visible = isTextVisible.value,
                enter = fadeIn() + slideInHorizontally(
                    initialOffsetX = { it },
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessLow
                    )
                ),
                exit = fadeOut() + shrinkHorizontally(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioNoBouncy,
                        stiffness = Spring.StiffnessMedium
                    )
                )
            ) {
                // 文本显示
                Text(
                    text = info.replace(",", "/"),
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    modifier = Modifier.padding(start = 12.dp),
                    fontSize = 14.sp,
                )
            }
        }
    }
}
