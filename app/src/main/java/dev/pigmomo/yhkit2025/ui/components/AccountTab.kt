package dev.pigmomo.yhkit2025.ui.components

import android.widget.Toast
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.api.model.boostcoupon.BoostCouponRecord
import dev.pigmomo.yhkit2025.api.model.card.CardItem
import dev.pigmomo.yhkit2025.api.model.coupon.CouponListCoupon
import dev.pigmomo.yhkit2025.api.model.credit.CreditData
import dev.pigmomo.yhkit2025.api.model.credit.CreditDetail
import dev.pigmomo.yhkit2025.api.model.invitatinv2.InvitationReward
import dev.pigmomo.yhkit2025.api.model.invitatinv2.SuccessInvite
import dev.pigmomo.yhkit2025.api.model.order.Order
import dev.pigmomo.yhkit2025.utils.ClipboardUtils
import dev.pigmomo.yhkit2025.utils.DateUtils
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.Locale

/**
 * 处理优惠券过期时间和显示文本
 *
 * @param coupon 优惠券对象
 * @param discountAmount 优惠券金额文本
 * @param nowDate 当前日期
 * @return Pair<Color, String> 包含文本颜色和标题文本的Pair
 */
private fun processCouponExpiration(
    coupon: CouponListCoupon,
    discountAmount: String,
    nowDate: LocalDate
): Pair<Color, String> {
    return try {
        val expirationParts = coupon.expirationDesc.split("-")
        if (expirationParts.size == 2) {
            val endDateStr = expirationParts[1].trim()
            val formatter = DateTimeFormatter.ofPattern("yyyy.MM.dd")
            val endDate = LocalDate.parse(endDateStr, formatter)

            // 计算过期状态和显示文本
            val isExpiringSoon = endDate.minusDays(3).isBefore(nowDate) || endDate.isEqual(nowDate)
            val expirationSuffix = when {
                endDate.isEqual(nowDate) -> " 今天过期"
                endDate.isEqual(nowDate.plusDays(1)) -> " 明天过期"
                endDate.isEqual(nowDate.plusDays(2)) -> " 后天过期"
                else -> ""
            }

            Pair(
                if (isExpiringSoon) Color.Red else Color.Unspecified,
                "${coupon.conditiondesc} $discountAmount$expirationSuffix"
            )
        } else {
            Pair(Color.Unspecified, "${coupon.conditiondesc} $discountAmount")
        }
    } catch (e: Exception) {
        Pair(Color.Unspecified, "${coupon.conditiondesc} $discountAmount")
    }
}

/**
 * 卡片项
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ItemCard(
    title: String,
    subtitle: String,
    backgroundColor: Color = Color(0xCBDEE6F7),
    textColor: Color = Color.Unspecified,
    onClick: () -> Unit = {},
    onLongClick: () -> Unit = {}
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor,
        ),
        modifier = Modifier.padding(top = 2.dp, bottom = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .combinedClickable(
                    onClick = onClick,
                    onLongClick = onLongClick
                )
        ) {
            Column(
                verticalArrangement = Arrangement.Center,
                modifier = Modifier
                    .padding(
                        start = 8.dp,
                        top = 12.dp,
                        end = 6.dp,
                        bottom = 12.dp
                    )
            ) {
                Text(
                    text = title,
                    fontSize = 14.sp,
                    lineHeight = 15.sp,
                    color = textColor,
                    maxLines = 1,
                    modifier = Modifier.horizontalScroll(rememberScrollState())
                )
                Text(
                    text = subtitle,
                    fontSize = 11.sp,
                    lineHeight = 12.sp,
                    color = textColor,
                    modifier = Modifier.horizontalScroll(rememberScrollState())
                )
            }
        }
    }
}

/**
 * 永辉卡标签内容
 */
@Composable
fun YhCardContent(
    cardList: List<CardItem>,
    onRefresh: () -> Unit,
    onBindCard: () -> Unit = {},
    onShowSendCardDialog: (CardItem) -> Unit = {},
    onSimpleSendCard: (CardItem) -> Unit = {},
    onCancelSendCard: (CardItem) -> Unit = {},
    onShowCancelSendCardDialog: (CardItem) -> Unit = {}
) {
    val context = LocalContext.current
    Column {
        // 操作按钮
        val actions = listOf(
            ActionItem(
                icon = Icons.Filled.Refresh,
                text = "刷新",
                onClick = onRefresh
            ),
            ActionItem(
                icon = Icons.Filled.Add,
                text = "绑定",
                onClick = onBindCard
            )
        )

        AccountActionBar(actions = actions)

        // 永辉卡列表
        LazyColumn(modifier = Modifier.heightIn(max = 400.dp)) {
            items(cardList.size) { index ->
                val card = cardList[index]

                val textTitle = when (card.state) {
                    2 -> "${card.cardTitle} 面额: ${card.cardAmount} 已转赠"
                    1 -> "${card.cardTitle} 面额: ${card.cardAmount} 赠送中"
                    else -> "${card.cardTitle} 余额: ${card.cardBalance} 面额: ${card.cardAmount}"
                }

                val textSubtitle = when (card.state) {
                    2 -> "${card.receiverPhone} 已领取 更新时间: ${card.lastUpdatedAt}"
                    1 -> "发起时间: ${card.sendTime}"
                    else -> "更新时间: ${card.lastUpdatedAt}" + (if (card.resources != "0") " 来源: ${card.resources}" else "")
                }

                ItemCard(
                    title = textTitle,
                    subtitle = textSubtitle,
                    onLongClick = {
                        if (card.state == 1) {
                            // 转赠中逻辑
                            onShowCancelSendCardDialog(card)
                            return@ItemCard
                        }
                    },
                    onClick = {
                        if (card.state == 2) return@ItemCard

                        if (card.state == 1) {
                            // 转赠中逻辑
                            onSimpleSendCard(card)
                            return@ItemCard
                        }

                        if (card.cardBalance == "0" && card.state == 3) {
                            // 异常情况，进行取消赠送
                            onCancelSendCard(card)
                            return@ItemCard
                        }

                        // 判断时间
                        val currentTime = System.currentTimeMillis()
                        val lastUpdatedTime = DateUtils.parseDate(card.lastUpdatedAt,"yyyy-MM-dd HH:mm")?.time ?: 0
                        val diffTime = (currentTime - lastUpdatedTime) / 1000

                        if (diffTime < 10) {
                            Toast.makeText(context, "请${diffTime}秒后重试", Toast.LENGTH_SHORT)
                                .show()
                            return@ItemCard
                        }

                        // 可以赠送卡处理
                        onShowSendCardDialog(card)
                    }
                )
            }

            item {
                if (cardList.size > 10) {
                    Text(
                        text = "更多请在APP内查看",
                        fontSize = 10.sp,
                        lineHeight = 12.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }
}

/**
 * 优惠券标签内容
 */
@Composable
fun CouponContent(
    availableCoupons: List<CouponListCoupon>,
    unavailableCoupons: List<CouponListCoupon>,
    onRefresh: () -> Unit,
    onGetNewPersonCoupon: () -> Unit = {},
    onReceiveCoupon: () -> Unit = {},
    onHandLuck: () -> Unit = {},
    onShowCouponDetailDialog: (CouponListCoupon) -> Unit = {}
) {
    // 当前日期
    val nowDate = LocalDate.now()

    Column {
        // 操作按钮
        val actions = listOf(
            ActionItem(
                icon = Icons.Filled.Refresh,
                text = "刷新",
                onClick = onRefresh
            ),
            ActionItem(
                icon = Icons.Filled.Person,
                text = "新人优惠券",
                onClick = onGetNewPersonCoupon
            ),
            ActionItem(
                icon = Icons.Filled.Add,
                text = "领取",
                onClick = onReceiveCoupon
            ),
            ActionItem(
                icon = Icons.Filled.Share,
                text = "拼手气",
                onClick = onHandLuck
            )
        )

        AccountActionBar(actions = actions)

        LazyColumn(
            modifier = Modifier
                .heightIn(max = 400.dp)
                .fillMaxWidth()
        ) {
            // 可用优惠券列表
            if (availableCoupons.isNotEmpty()) {
                items(availableCoupons.size) { index ->
                    val coupon = availableCoupons[index]

                    val discountAmount = if (coupon.amount == -100) {
                        coupon.desc
                    } else {
                        "￥${coupon.amount / 100.00}"
                    }

                    val couponType = when (coupon.couponlatitude) {
                        "coupon.general" -> "优惠券"
                        "redEnvelope.coupon" -> "红包"
                        else -> "优惠券"
                    }

                    val tagListText = coupon.taglist.joinToString(" ") { it.text }

                    // 处理优惠券过期时间和显示文本
                    val (textColor, title) = processCouponExpiration(
                        coupon,
                        discountAmount,
                        nowDate
                    )

                    ItemCard(
                        title = title,
                        subtitle = "${coupon.expirationDesc} $couponType$tagListText",
                        onClick = { },
                        onLongClick = { onShowCouponDetailDialog(coupon) },
                        textColor = textColor
                    )
                }
            }

            // 不可用优惠券列表
            if (unavailableCoupons.isNotEmpty()) {
                items(unavailableCoupons.size) { index ->
                    val coupon = unavailableCoupons[index]

                    val discountAmount = if (coupon.amount == -100) {
                        coupon.desc
                    } else {
                        "￥${coupon.amount / 100.00}"
                    }

                    val couponType = when (coupon.couponlatitude) {
                        "coupon.general" -> "优惠券"
                        "redEnvelope.coupon" -> "红包"
                        else -> "优惠券"
                    }

                    val tagListText = coupon.taglist.joinToString(" ") { it.text }

                    // 处理优惠券过期时间和显示文本
                    val (textColor, title) = processCouponExpiration(
                        coupon,
                        discountAmount,
                        nowDate
                    )

                    ItemCard(
                        title = title,
                        subtitle = "${coupon.expirationDesc} $couponType$tagListText",
                        textColor = Color.Gray,
                        onClick = { },
                        onLongClick = { onShowCouponDetailDialog(coupon) }
                    )
                }
            }
        }
    }
}

/**
 * 助力券标签内容
 */
@Composable
fun BoostCouponContent(
    boostCouponList: List<BoostCouponRecord>,
    onRefresh: () -> Unit,
    onGetGameCode: (String) -> Unit,
    onEditBoostConfig: () -> Unit = {},
    onSaveBoostConfig: () -> Unit = {},
    onShowBoostCouponDetailDialog: (BoostCouponRecord) -> Unit = {}
) {
    val context = LocalContext.current
    Column {
        // 操作按钮
        val actions = listOf(
            ActionItem(
                icon = Icons.Filled.Refresh,
                text = "刷新",
                onClick = onRefresh
            ),
            ActionItem(
                icon = Icons.Filled.Edit,
                text = "编辑配置",
                onClick = onEditBoostConfig
            ),
            ActionItem(
                painterIcon = R.drawable.baseline_save_24,
                text = "保存配置",
                onClick = onSaveBoostConfig
            )
        )

        AccountActionBar(actions = actions)

        // 助力券列表
        LazyColumn(modifier = Modifier.heightIn(max = 400.dp)) {
            items(boostCouponList.size) { index ->
                val boostCoupon = boostCouponList[index]
                val prizeGameDTO = boostCoupon.prizeGameDTO ?: return@items
                val boostCouponVO = boostCoupon.boostCouponVO ?: return@items

                val prizeId = prizeGameDTO.prizeId.toString()
                val gameCode = prizeGameDTO.gameCode.toString()
                val prizeGameStatus = prizeGameDTO.prizeGameStatus.toString()
                val needBoostNum = prizeGameDTO.needBoostNum
                val boostNum = prizeGameDTO.remainBoostNum // 还差助力人数
                val activityTitle = prizeGameDTO.activityTitle

                val remainBoostNumStr = boostCouponVO.availableCount / needBoostNum

                val boosterTypeStr = when (prizeGameDTO.boosterType) {
                    1 -> ""
                    2 -> " 限新用户助力"
                    else -> ""
                }

                val boostExpireAt = prizeGameDTO.boostExpireAt?.let { timestamp ->
                    if (prizeGameStatus == "2") {
                        DateUtils.formatDate(timestamp,"HH:mm:ss")
                    } else ""
                } ?: ""

                val statusText = when (prizeGameStatus) {
                    "1" -> "未发起"
                    "2" -> "进行中"
                    "3" -> "已结束"
                    "4" -> "已超时"
                    else -> "未知"
                }

                val backgroundColor = when (prizeGameStatus) {
                    "2" -> Color(0xCBF7DEF4)
                    else -> Color(0xCBDEE6F7)
                }

                val title = "${boostCouponVO.name} ${boostCouponVO.amount}${boostCouponVO.unit}" +
                        (if (boostCouponVO.couponDescription.isNotEmpty() && boostCouponVO.name != boostCouponVO.couponDescription) " ${boostCouponVO.couponDescription}" else "") +
                        (if (boostCouponVO.isRedEnvelope) " 红包" else "") + boosterTypeStr

                val subtitle = when (prizeGameStatus) {
                    "1" -> "$prizeId $activityTitle 库存:${remainBoostNumStr}张"
                    "2" -> "$prizeId,$gameCode $boostExpireAt,$needBoostNum,$boostNum 库存:${remainBoostNumStr}张"
                    "4" -> "$statusText $prizeId $activityTitle 库存:${remainBoostNumStr}张"
                    else -> statusText
                }

                ItemCard(
                    title = title,
                    subtitle = subtitle,
                    backgroundColor = backgroundColor,
                    onClick = {
                        when (prizeGameStatus) {
                            "1", "4" -> {
                                // 获取助力链接或重新发起助力
                                onGetGameCode(prizeId)
                            }

                            "2" -> {
                                // 复制链接
                                ClipboardUtils.copyTextToClipboard(
                                    context,
                                    "prizeId=$prizeId&gameCode=$gameCode"
                                )
                                Toast.makeText(
                                    context,
                                    "助力参数已复制到剪贴板",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        }
                    },
                    onLongClick = { onShowBoostCouponDetailDialog(boostCoupon) }
                )
            }
        }
    }
}

/**
 * 订单标签内容
 */
@Composable
fun OrderContent(
    orderList: List<Order>,
    hasNextPage: Boolean,
    lastOrderId: String,
    onLoadMore: () -> Unit,
    onRefresh: () -> Unit,
    onGetOrderDetail: (String) -> Unit,
    onQueryOrder: () -> Unit = {},
    onClearOrder: () -> Unit = {}
) {
    Column {
        // 操作按钮
        val actions = listOf(
            ActionItem(
                icon = Icons.Filled.Refresh,
                text = "刷新",
                onClick = onRefresh
            ),
            ActionItem(
                icon = Icons.Filled.Search,
                text = "查询",
                onClick = onQueryOrder
            ),
            ActionItem(
                icon = Icons.Filled.Delete,
                text = "清空",
                onClick = onClearOrder
            )
        )

        AccountActionBar(actions = actions)

        // 订单列表
        LazyColumn(modifier = if (hasNextPage) Modifier.height(400.dp) else Modifier.heightIn(max = 400.dp)) {
            items(orderList.size) { index ->
                val order = orderList[index]

                // 如果到达列表底部且还有更多数据，加载更多
                if (index == orderList.size - 1 && hasNextPage) {
                    onLoadMore()
                }

                val ispickselfStr = if (order.ispickself == 1) "自提" else "配送"
                val formattedAmount = "¥${order.totalpayment / 100.00}"

                // 处理配送时间
                val texpecttime = order.texpecttime
                val formattedDate = if (texpecttime != null && texpecttime.date > 0) {
                    DateUtils.formatDate(texpecttime.date,"yy-MM-dd")
                } else {
                    ""
                }

                val timeSlot = texpecttime?.timeslots?.firstOrNull()
                val timeSlotStr = if (timeSlot != null && timeSlot.slottype != "immediate") {
                    "${timeSlot.from}-${timeSlot.to}"
                } else {
                    "尽快送达（预计${timeSlot?.to}前）"
                }

                val textTitle =
                    "${order.statusmsg} $ispickselfStr ${order.title} ${order.seller.title}"
                val textSubtitle = "$formattedDate $timeSlotStr ${order.id} $formattedAmount"

                ItemCard(
                    title = textTitle,
                    subtitle = textSubtitle,
                    backgroundColor = if (order.status != 5) Color(0xCBF7DEF4) else Color(0xCBDEE6F7),
                    onClick = {
                        // 根据订单状态处理点击事件
                        if (order.status in listOf(1, 2, 3, 5, 6, 12, 16)) {
                            onGetOrderDetail(order.id)
                        }
                    }
                )
            }

            // 显示三个空ItemCard占位，防止列表空着
            if (hasNextPage && orderList.size == 5) {
                items(3) {
                    ItemCard(
                        title = "",
                        subtitle = "",
                        backgroundColor = Color(0xCBDEE6F7),
                        textColor = Color.Gray
                    )
                }
            }

            if (!hasNextPage && orderList.size > 5) {
                item {
                    Text(
                        text = "到底啦！！！",
                        fontSize = 10.sp,
                        lineHeight = 12.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }
}

/**
 * 邀请有礼标签内容
 */
@Composable
fun InvitationContent(
    invitationRewards: List<InvitationReward>,
    successInvites: List<SuccessInvite>,
    onRefreshRewards: () -> Unit,
    onCopyInvitationCode: () -> Unit = {},
    onGetInvitationRules: () -> Unit = {},
    onBindInvitationCode: () -> Unit = {}
) {
    Column {
        // 操作按钮
        val actions = listOf(
            ActionItem(
                icon = Icons.Filled.Refresh,
                text = "刷新",
                onClick = onRefreshRewards
            ),
            ActionItem(
                painterIcon = R.drawable.baseline_copy_all_24,
                text = "复制邀请码",
                onClick = onCopyInvitationCode
            ),
            ActionItem(
                icon = Icons.Filled.Info,
                text = "规则",
                onClick = onGetInvitationRules
            ),
            ActionItem(
                icon = Icons.Filled.Add,
                text = "绑定",
                onClick = onBindInvitationCode
            )
        )

        AccountActionBar(actions = actions)

        // 成功邀请和奖励列表
        LazyColumn(modifier = Modifier.heightIn(max = 400.dp)) {
            // 成功获得奖励的记录
            if (invitationRewards.isNotEmpty()) {
                item {
                    Text(
                        text = "奖励记录",
                        modifier = Modifier.padding(6.dp)
                    )
                }

                items(invitationRewards.size) { index ->
                    val reward = invitationRewards[index]

                    val inviteeType = if (reward.inviteeType == "new") "新用户" else "老用户"
                    val textTitle =
                        "${reward.mobile} $inviteeType ${reward.rewardAmount}${reward.rewardType} 已成功"

                    val formattedDate = DateUtils.formatDate(reward.completeTime,"yyyy-MM-dd HH:mm")

                    ItemCard(
                        title = textTitle,
                        subtitle = formattedDate
                    )
                }

                if (invitationRewards.size == 10) {
                    item {
                        Text(
                            text = "更多请在APP内查看",
                            fontSize = 10.sp,
                            lineHeight = 12.sp,
                            color = Color.Gray,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }

            // 已邀请但未获得奖励的记录
            if (successInvites.isNotEmpty()) {
                item {
                    Text(
                        text = "邀请记录",
                        modifier = Modifier.padding(
                            top = 8.dp,
                            start = 6.dp,
                            end = 8.dp,
                            bottom = 8.dp
                        )
                    )
                }

                items(successInvites.size) { index ->
                    val invite = successInvites[index]

                    val inviteeType = if (invite.inviteeType == "new") "新用户" else "老用户"
                    val statusStr = when (invite.status) {
                        1 -> "奖励已到账"
                        0 -> "未下单"
                        -1 -> "已失效"
                        else -> invite.statusDesc ?: "未知"
                    }

                    val textTitle = "${invite.mobile} $inviteeType $statusStr"

                    val formattedDate = DateUtils.formatDate(invite.inviteTime,"yyyy-MM-dd HH:mm")

                    ItemCard(
                        title = textTitle,
                        subtitle = formattedDate
                    )
                }

                if (successInvites.size == 10) {
                    item {
                        Text(
                            text = "更多请在APP内查看",
                            fontSize = 10.sp,
                            lineHeight = 12.sp,
                            color = Color.Gray,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }
        }
    }
}

/**
 * 积分标签内容
 */
@Composable
fun CreditContent(
    creditData: CreditData?,
    creditDetails: List<CreditDetail>,
    creditCount: Int,
    onLoadMore: (Int) -> Unit,
    onRefresh: () -> Unit,
    onPointTeam: () -> Unit,
    onSignRewardRule: () -> Unit,
    onShowJoinPointTeamDialog: () -> Unit
) {
    Column {
        // 操作按钮
        val actions = listOf(
            ActionItem(
                icon = Icons.Filled.Refresh,
                text = "刷新",
                onClick = onRefresh
            ),
            ActionItem(
                painterIcon = R.drawable.baseline_copy_all_24,
                text = "积分组队",
                onClick = onPointTeam
            ),
            ActionItem(
                icon = Icons.Filled.Info,
                text = "规则",
                onClick = onSignRewardRule
            ),
            ActionItem(
                icon = Icons.Filled.Add,
                text = "参加组队",
                onClick = onShowJoinPointTeamDialog
            )
        )

        AccountActionBar(actions = actions)

        // 积分信息
        if (creditData != null && creditData.expiringcredit > 0) {
            Card(
                modifier = Modifier.padding(top = 2.dp, bottom = 2.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xCBDEE6F7),
                ),
            ) {
                Column(
                    modifier = Modifier
                        .padding(
                            start = 8.dp,
                            top = 12.dp,
                            end = 6.dp,
                            bottom = 12.dp
                        )
                        .fillMaxWidth()
                ) {
                    Text(
                        text = "即将过期: ${creditData.expiringcredit}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.Red
                    )
                }
            }
        }

        // 积分记录列表
        LazyColumn(modifier = Modifier.heightIn(max = 400.dp)) {
            items(creditDetails.size) { index ->
                val detail = creditDetails[index]

                // 如果到达列表底部且还有更多数据，加载更多
                if (index == creditDetails.size - 1 && creditDetails.size < creditCount) {
                    val nextPage = (creditDetails.size / 20) // 假设每页20条
                    onLoadMore(nextPage)
                }

                val formattedDate = DateUtils.formatDate(detail.date,"yyyy-MM-dd HH:mm")

                val additionSign = if (detail.addition > 0) "+" else ""

                ItemCard(
                    title = "${detail.desc} $additionSign${detail.addition.toInt()}",
                    subtitle = formattedDate
                )
            }

            if (creditDetails.size >= 5) {
                item {
                    Text(
                        text = "到底啦！！！",
                        fontSize = 10.sp,
                        lineHeight = 12.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }
}

/**
 * 标签栏组件
 * 用于显示多个标签页选项，支持水平滚动
 *
 * @param tabs 标签页列表，包含标签名称和可选的附加信息
 * @param selectedTabIndex 当前选中的标签索引
 * @param onTabSelected 标签选择回调
 * @param fontSize 标签文字大小，默认为12sp
 * @param spacing 标签之间的间距，默认为5dp
 */
@Composable
fun TabBar(
    tabs: List<TabItem>,
    selectedTabIndex: Int,
    onTabSelected: (Int) -> Unit,
    fontSize: Int = 14,
    spacing: Int = 5
) {
    Row(modifier = Modifier.horizontalScroll(rememberScrollState())) {
        tabs.forEachIndexed { index, tab ->
            TextButton(
                onClick = { onTabSelected(index) },
                colors = ButtonDefaults.textButtonColors(
                    containerColor = if (selectedTabIndex == index) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        Color(0xFFDEE6F7)
                    },
                    contentColor = if (selectedTabIndex == index) {
                        MaterialTheme.colorScheme.onPrimary
                    } else {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    },
                ),
                modifier = Modifier.padding(
                    start = if (index == 0) 0.dp else spacing.dp
                )
            ) {
                Text(
                    text = tab.displayText,
                    fontSize = fontSize.sp
                )
            }
        }
    }
}

/**
 * 标签项数据类
 *
 * @param name 标签名称
 * @param badge 附加信息，如数量、金额等
 * @param displayText 显示文本，如果为空则自动拼接name和badge
 */
data class TabItem(
    val name: String,
    val badge: String = "",
    val displayText: String = if (badge.isEmpty() || badge == "0") name else "$name$badge"
)

/**
 * AccountDialog标签页常量
 */
object AccountTabs {
    const val YH_CARD = "永辉卡"
    const val COUPON = "优惠券"
    const val BOOST_COUPON = "助力券"
    const val ORDER = "订单"
    const val INVITATION = "邀请有礼"
    const val CREDIT = "积分"

    /**
     * 获取所有标签页
     */
    fun getAllTabs(): List<String> {
        return listOf(YH_CARD, COUPON, BOOST_COUPON, ORDER, INVITATION, CREDIT)
    }
}

/**
 * 账户操作栏
 * 显示一组操作按钮
 *
 * @param actions 操作按钮列表
 */
@Composable
fun AccountActionBar(
    actions: List<ActionItem>
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color(0xCBDEE6F7),
        ),
        modifier = Modifier
            .padding(top = 2.dp, bottom = 2.dp)
            .fillMaxWidth()
    ) {
        Row {
            actions.forEach { action ->
                if (action.icon != null) {
                    TokenActionButton(
                        imageVector = action.icon,
                        text = action.text,
                        onClick = action.onClick
                    )
                } else {
                    TokenActionButton(
                        icon = action.painterIcon!!,
                        text = action.text,
                        onClick = action.onClick
                    )
                }
            }
        }
    }
}

/**
 * 操作按钮项
 *
 * @param icon 图标
 * @param text 文本
 * @param onClick 点击回调
 * @param iconSize 图标大小
 */
data class ActionItem(
    val icon: ImageVector? = null,
    val painterIcon: Int? = null,
    val text: String,
    val onClick: () -> Unit,
    val iconSize: Dp = 24.dp
)