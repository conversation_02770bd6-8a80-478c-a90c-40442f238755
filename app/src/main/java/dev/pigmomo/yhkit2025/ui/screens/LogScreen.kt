package dev.pigmomo.yhkit2025.ui.screens

import android.widget.Toast
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.data.model.LogEntity
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor
import dev.pigmomo.yhkit2025.viewmodel.LogViewModel
import dev.pigmomo.yhkit2025.utils.ClipboardUtils
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Locale
import dev.pigmomo.yhkit2025.ui.components.LogFilterDropdown
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults

/**
 * 日志查看界面
 * 用于显示ProcessRecorder记录的日志
 * @param viewModel 日志视图模型
 * @param onNavigateBack 返回上一页的回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LogScreen(
    viewModel: LogViewModel,
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current

    val logs by viewModel.logs.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val hasMoreData by viewModel.hasMoreData.collectAsState()
    var searchQuery by remember { mutableStateOf("") }
    var selectedFilter by remember { mutableStateOf("全部") }
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()

    // 根据滚动状态判断是否显示回到顶部按钮
    val showScrollToTopButton by remember {
        derivedStateOf {
            listState.firstVisibleItemIndex > 5
        }
    }

    // 检测滚动到底部并加载更多数据
    val endOfListReached by remember {
        derivedStateOf {
            val lastVisibleItem = listState.layoutInfo.visibleItemsInfo.lastOrNull()

            // 当列表为空或加载中时不触发加载
            if (logs.isEmpty() || isLoading) {
                false
            } else {
                // 检查是否到达列表底部
                lastVisibleItem != null && lastVisibleItem.index >= logs.size - 3 && hasMoreData
            }
        }
    }

    // 当到达列表底部时加载更多数据
    LaunchedEffect(endOfListReached) {
        if (endOfListReached && hasMoreData && !isLoading) {
            viewModel.loadNextPage()
        }
    }

    // 初始加载
    LaunchedEffect(Unit) {
        viewModel.loadAllLogs()
    }

    // 处理搜索和筛选的协同工作
    fun applyFiltersAndSearch() {
        when (selectedFilter) {
            "全部" -> {
                if (searchQuery.isNotEmpty()) {
                    viewModel.searchLogs(searchQuery)
                } else {
                    viewModel.loadAllLogs()
                }
            }

            "按手机号" -> {
                val phoneNumber = searchQuery.takeIf { it.isNotEmpty() } ?: ""
                if (phoneNumber.isNotEmpty()) {
                    viewModel.loadLogsByPhoneNumber(phoneNumber)
                } else {
                    Toast.makeText(
                        context,
                        "请先在搜索框中输入手机号",
                        Toast.LENGTH_SHORT
                    ).show()
                    viewModel.loadAllLogs()
                }
            }

            else -> {
                if (searchQuery.isNotEmpty()) {
                    // 如果有搜索关键词且选择了特定级别，先按级别筛选再搜索
                    viewModel.loadLogsByLevelAndSearch(selectedFilter, searchQuery)
                } else {
                    viewModel.loadLogsByLevel(selectedFilter)
                }
            }
        }
    }

    // 监听搜索查询变化
    LaunchedEffect(searchQuery) {
        if (searchQuery.isNotEmpty() || selectedFilter != "全部") {
            applyFiltersAndSearch()
        } else {
            viewModel.loadAllLogs()
        }
    }

    // 监听筛选器变化
    LaunchedEffect(selectedFilter) {
        applyFiltersAndSearch()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("LOG") },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    actionIconContentColor = Color.White,
                    navigationIconContentColor = Color.White
                ),
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    var showClearAllLogsConfirmDialog by remember { mutableStateOf(false) }

                    // 只有当日志不为空时才显示清空按钮
                    if (logs.isNotEmpty()) {
                        IconButton(onClick = { showClearAllLogsConfirmDialog = true }) {
                            Icon(Icons.Filled.Clear, contentDescription = "清空日志")
                        }
                    }

                    // 清空日志确认对话框
                    if (showClearAllLogsConfirmDialog) {
                        ConfirmationDialog(
                            title = "确认清空日志",
                            message = "确定要清空所有日志记录吗？",
                            onConfirm = {
                                viewModel.clearAllLogs()
                                showClearAllLogsConfirmDialog = false
                            },
                            onDismiss = { showClearAllLogsConfirmDialog = false }
                        )
                    }
                }
            )
        },
        floatingActionButton = {
            AnimatedVisibility(
                visible = showScrollToTopButton,
                enter = scaleIn(animationSpec = tween(300)) + fadeIn(animationSpec = tween(300)),
                exit = scaleOut(animationSpec = tween(300)) + fadeOut(animationSpec = tween(300))
            ) {
                FloatingActionButton(
                    onClick = {
                        coroutineScope.launch {
                            listState.animateScrollToItem(0)
                        }
                    },
                    containerColor = MaterialTheme.colorScheme.primary,
                    contentColor = Color.White,
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Icon(
                        imageVector = Icons.Filled.KeyboardArrowUp,
                        contentDescription = "回到顶部",
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }
    ) { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {

            // 搜索和筛选区域
            Column(
                modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 16.dp, bottom = 0.dp)
            ) {
                // 搜索框
                OutlinedTextField(
                    value = searchQuery,
                    onValueChange = { searchQuery = it },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = {
                        Text(
                            "搜索日志内容、手机号或UID...",
                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                        )
                    },
                    leadingIcon = {
                        Icon(
                            Icons.Filled.Search,
                            contentDescription = "搜索",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    },
                    singleLine = true,
                    shape = RoundedCornerShape(12.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MaterialTheme.colorScheme.primary,
                        unfocusedBorderColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f),
                        cursorColor = MaterialTheme.colorScheme.primary,
                        focusedContainerColor = MaterialTheme.colorScheme.surface,
                        unfocusedContainerColor = MaterialTheme.colorScheme.surface
                    )
                )

                Spacer(modifier = Modifier.height(12.dp))

                // 筛选器按钮行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "筛选(${logs.size}):",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontWeight = FontWeight.Medium
                    )

                    // 筛选按钮
                    val filterOptions = listOf(
                        "全部" to Icons.Filled.Home,
                        "INFO" to Icons.Filled.Info,
                        "WARNING" to Icons.Filled.Warning,
                        "ERROR" to Icons.Filled.Close,
                        //"按手机号" to Icons.Filled.Search
                    )

                    LogFilterDropdown(
                        selectedFilter = selectedFilter,
                        onFilterSelected = { selectedFilter = it },
                        filterOptions = filterOptions
                    )
                }
            }

            // 日志列表
            if (logs.isEmpty() && !isLoading) {
                // 空状态页面
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        // 空状态图标
                        Surface(
                            modifier = Modifier.size(80.dp),
                            shape = CircleShape,
                            color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                        ) {
                            Box(
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    Icons.Filled.Search,
                                    contentDescription = null,
                                    modifier = Modifier.size(40.dp),
                                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                                )
                            }
                        }

                        Text(
                            text = "暂无日志记录",
                            style = MaterialTheme.typography.headlineSmall,
                            color = MaterialTheme.colorScheme.onSurface,
                            fontWeight = FontWeight.Medium
                        )

                        Text(
                            text = if (selectedFilter != "全部" || searchQuery.isNotEmpty()) {
                                "没有找到符合条件的日志\n尝试调整搜索条件或筛选器"
                            } else {
                                "系统还没有记录任何日志"
                            },
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            textAlign = TextAlign.Center,
                            lineHeight = 20.sp
                        )
                    }
                }
            } else {
                Box(modifier = Modifier.fillMaxSize()) {
                    LazyColumn(
                        state = listState,
                        modifier = Modifier.fillMaxSize(),
                        verticalArrangement = Arrangement.spacedBy(2.dp),
                    ) {
                        items(logs) { log ->
                            LogItem(
                                log = log,
                                onUidClick = { uid ->
                                    ClipboardUtils.copyTextToClipboard(context, uid)
                                    searchQuery = uid
                                },
                                onPhoneNumberClick = { phoneNumber ->
                                    ClipboardUtils.copyTextToClipboard(context, phoneNumber)
                                    searchQuery = phoneNumber
                                }
                            )
                        }

                        // 底部状态提示
                        item {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                when {
                                    isLoading && logs.isNotEmpty() -> {
                                        // 加载中状态
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            CircularProgressIndicator(
                                                modifier = Modifier.size(20.dp),
                                                strokeWidth = 2.dp,
                                                color = MaterialTheme.colorScheme.primary
                                            )
                                            Text(
                                                text = "正在加载更多日志...",
                                                style = MaterialTheme.typography.bodyMedium,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        }
                                    }

                                    !hasMoreData && logs.isNotEmpty() -> {
                                        // 没有更多数据
                                        Surface(
                                            color = MaterialTheme.colorScheme.surfaceVariant.copy(
                                                alpha = 0.5f
                                            ),
                                            shape = RoundedCornerShape(20.dp)
                                        ) {
                                            Text(
                                                text = "✨ 已显示全部日志",
                                                style = MaterialTheme.typography.bodyMedium,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(
                                                    alpha = 0.8f
                                                ),
                                                modifier = Modifier.padding(
                                                    horizontal = 16.dp,
                                                    vertical = 8.dp
                                                )
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 单条日志项组件
 * @param log 日志实体
 * @param onUidClick 点击UID时的回调，传入UID值
 * @param onPhoneNumberClick 点击手机号时的回调，传入手机号
 */
@Composable
fun LogItem(
    log: LogEntity,
    onUidClick: (String) -> Unit = {},
    onPhoneNumberClick: (String) -> Unit = {}
) {
    val dateFormat = remember { SimpleDateFormat("MM-dd HH:mm:ss", Locale.getDefault()) }
    val formattedDate = remember(log) { dateFormat.format(log.timestamp) }
    val context = LocalContext.current

    // 根据日志级别选择颜色和图标
    val (logLevelColor, logLevelIcon) = when (log.logLevel) {
        "INFO" -> MaterialTheme.colorScheme.primary to Icons.Filled.Info
        "WARNING" -> MaterialTheme.colorScheme.tertiary to Icons.Filled.Warning
        "ERROR" -> MaterialTheme.colorScheme.error to Icons.Filled.Close
        else -> MaterialTheme.colorScheme.primary to Icons.Filled.Info
    }

    Card(
        modifier = Modifier
            .padding(horizontal = 10.dp, vertical = 4.dp)
            .fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth()
        ) {

            // 主要内容区域
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(16.dp)
            ) {
                // 头部信息行：日志级别图标、标签、时间
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 左侧：日志级别图标和标签
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = logLevelIcon,
                            contentDescription = log.logLevel,
                            modifier = Modifier.size(18.dp),
                            tint = logLevelColor
                        )

                        Surface(
                            color = logLevelColor.copy(alpha = 0.1f),
                            contentColor = logLevelColor,
                            shape = RoundedCornerShape(6.dp)
                        ) {
                            Text(
                                text = log.tag,
                                style = MaterialTheme.typography.labelSmall,
                                fontWeight = FontWeight.Medium,
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 2.dp)
                            )
                        }
                    }

                    // 右侧：时间戳
                    Text(
                        text = formattedDate,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                        fontWeight = FontWeight.Normal
                    )
                }

                // 用户信息行：手机号和UID
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 手机号
                    Surface(
                        onClick = { onPhoneNumberClick(log.phoneNumber) },
                        modifier = Modifier.clip(RoundedCornerShape(8.dp)),
                        color = MaterialTheme.colorScheme.surfaceContainer.copy(alpha = 0.3f),
                        contentColor = MaterialTheme.colorScheme.onSurface
                    ) {
                        Text(
                            text = log.phoneNumber,
                            style = MaterialTheme.typography.bodySmall,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }

                    // UID
                    Surface(
                        onClick = { onUidClick(log.tokenUid) },
                        modifier = Modifier.clip(RoundedCornerShape(8.dp)),
                        color = MaterialTheme.colorScheme.surfaceContainer.copy(alpha = 0.3f),
                        contentColor = MaterialTheme.colorScheme.onSurface
                    ) {
                        Text(
                            text = log.tokenUid,
                            style = MaterialTheme.typography.bodySmall,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }

                // 消息内容
                Surface(
                    onClick = {
                        ClipboardUtils.copyTextToClipboard(context, log.message)
                        Toast.makeText(
                            context,
                            "消息内容已复制到剪贴板",
                            Toast.LENGTH_SHORT
                        ).show()
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(8.dp)),
                    color = CardContainerColor,
                    contentColor = MaterialTheme.colorScheme.onSurface
                ) {
                    Text(
                        text = log.message,
                        style = MaterialTheme.typography.bodyMedium,
                        maxLines = 1,
                        modifier = Modifier
                            .horizontalScroll(rememberScrollState())
                            .padding(12.dp)
                            .fillMaxWidth(),
                        lineHeight = 20.sp
                    )
                }
            }
        }
    }
}
