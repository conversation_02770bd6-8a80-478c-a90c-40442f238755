package dev.pigmomo.yhkit2025.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val RoyalBlue = Color(0xFF3669CB)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// 对话框背景颜色
val DialogContainerColor = Color(0xFFF0F2FF)

// 卡片背景颜色
val CardContainerColor = Color(0xCBDEE6F7)

// TAB相关颜色
val TabSelectedGradientStart = Color(0xFF3669CB)
val TabSelectedGradientEnd = Color(0xFF4A7BD8)
val TabUnselectedColor = Color(0xFF8A8A8A)
val TabIndicatorColor = Color(0xFF3669CB)
val TabBadgeSelectedColor = Color(0xFF3669CB)
val TabBadgeUnselectedColor = Color(0xFF9E9E9E)