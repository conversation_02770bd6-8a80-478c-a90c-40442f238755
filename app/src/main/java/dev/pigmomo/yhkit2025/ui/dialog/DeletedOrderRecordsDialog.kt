package dev.pigmomo.yhkit2025.ui.dialog

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.data.model.DeletedOrderEntity
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import java.text.SimpleDateFormat
import java.util.*

/**
 * 删除订单记录对话框
 * @param onDismiss 关闭对话框
 * @param deletedOrders 删除订单记录
 * @param onClearAllRecords 清空所有记录
 * @param onOrderSelect 选择订单
 */
@Composable
fun DeletedOrderRecordsDialog(
    onDismiss: () -> Unit,
    deletedOrders: List<DeletedOrderEntity>,
    onClearAllRecords: () -> Unit = {},
    onOrderSelect: (String) -> Unit = {}
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "请选择删除订单记录 (${deletedOrders.size})",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                Row {
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Filled.Close,
                            contentDescription = "关闭"
                        )
                    }
                }
            }
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 300.dp)
            ) {
                LazyColumn(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(deletedOrders) { deletedOrder ->
                        DeletedOrderRecordItem(
                            deletedOrder = deletedOrder,
                            onOrderSelect = onOrderSelect
                        )
                    }
                }

                /* Spacer(modifier = Modifier.height(16.dp))

                // 清空所有记录按钮
                if (deletedOrders.isNotEmpty()) {
                    Button(
                        onClick = onClearAllRecords,
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error,
                            contentColor = Color.White
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Delete,
                            contentDescription = "清空记录",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("清空所有记录")
                    }
                } */
            }
        },
        confirmButton = { },
        dismissButton = { },
        containerColor = dialogContainerColor()
    )
}

/**
 * 删除订单记录项
 */
@Composable
fun DeletedOrderRecordItem(
    deletedOrder: DeletedOrderEntity,
    onOrderSelect: (String) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 2.dp, bottom = 2.dp),
        shape = RoundedCornerShape(8.dp),
        colors = cardThemeOverlay()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onOrderSelect(deletedOrder.orderId) }
                .padding(12.dp)
        ) {
            // 订单ID和状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "订单ID: ${deletedOrder.orderId}",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )

                // 订单状态标签
                Surface(
                    shape = RoundedCornerShape(4.dp),
                    color = getOrderStatusColor(deletedOrder.orderStatus)
                ) {
                    Text(
                        text = getOrderStatusText(deletedOrder.orderStatus),
                        fontSize = 10.sp,
                        color = Color.White,
                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(4.dp))

            // 订单标题
            if (deletedOrder.orderTitle.isNotEmpty()) {
                Text(
                    text = "订单: ${deletedOrder.orderTitle}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                Spacer(modifier = Modifier.height(4.dp))
            }

            // 账号信息
            Text(
                text = "账号: ${deletedOrder.phoneNumber} (${deletedOrder.uid})",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(4.dp))

            // 删除时间
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "删除时间: ${formatDeleteTime(deletedOrder.deleteTimestamp)}",
                    fontSize = 11.sp,
                    color = MaterialTheme.colorScheme.outline
                )

                if (deletedOrder.extraNote.isNotEmpty()) {
                    Text(
                        text = deletedOrder.extraNote,
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.outline,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }
    }
}

/**
 * 格式化删除时间
 */
private fun formatDeleteTime(timestamp: Long): String {
    return try {
        val date = Date(timestamp)
        val formatter = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
        formatter.format(date)
    } catch (e: Exception) {
        "时间错误"
    }
}

/**
 * 获取订单状态颜色
 */
private fun getOrderStatusColor(status: Int): Color {
    return when (status) {
        5 -> Color(0xFF4CAF50) // 已完成 - 绿色
        6 -> Color(0xFF9E9E9E) // 已取消 - 灰色
        16 -> Color(0xFF607D8B) // 已关闭 - 蓝灰色
        else -> Color(0xFF2196F3) // 其他 - 蓝色
    }
}

/**
 * 获取订单状态文本
 */
private fun getOrderStatusText(status: Int): String {
    return when (status) {
        1 -> "待付款"
        2 -> "待配送"
        3 -> "配送中"
        5 -> "已完成"
        6 -> "已取消"
        12 -> "待自提"
        16 -> "已关闭"
        else -> "未知($status)"
    }
}
