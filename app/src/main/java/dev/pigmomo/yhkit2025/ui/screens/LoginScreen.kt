package dev.pigmomo.yhkit2025.ui.screens

import android.annotation.SuppressLint
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.LoginTokenEntity
import dev.pigmomo.yhkit2025.data.model.PhoneWithPasswordEntity
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor
import dev.pigmomo.yhkit2025.ui.theme.TabBadgeSelectedColor
import dev.pigmomo.yhkit2025.ui.theme.TabBadgeUnselectedColor
import dev.pigmomo.yhkit2025.ui.theme.TabSelectedGradientEnd
import dev.pigmomo.yhkit2025.ui.theme.TabSelectedGradientStart
import dev.pigmomo.yhkit2025.ui.theme.TabUnselectedColor
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.viewmodel.LoginViewModel
import java.text.SimpleDateFormat
import java.util.Date
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.dialog.ExportLoginAccountDialog
import kotlinx.coroutines.launch
import kotlin.math.tan

/**
 * 登录界面
 * @param viewModel 登录视图模型
 */
@SuppressLint("RememberReturnType")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoginScreen(viewModel: LoginViewModel) {
    val context = LocalContext.current
    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current

    // 从ViewModel获取状态
    val loginTokens by viewModel.loginTokens.collectAsState()
    val phoneWithPasswords by viewModel.phoneWithPasswords.collectAsState()
    val clickedTokenUid by viewModel.clickedTokenUid
    val longClickedTokenUid by viewModel.longClickedTokenUid
    val isTokenDetailDialogVisible by viewModel.isTokenDetailDialogVisible
    val isAccountExporting by viewModel.accountExporting
    val isExportAccountDialogVisible by viewModel.isExportAccountDialogVisible
    val isAccountImporting by viewModel.accountImporting
    val loggingInPhoneNumbersForPassword by viewModel.loggingInPhoneNumbersForPassword.collectAsState()
    val loggingInPhoneNumbersForSms by viewModel.loggingInPhoneNumbersForSms.collectAsState()
    val inputStr by viewModel.inputStr
    val isAutoRegistering by viewModel.isAutoRegistering
    val remainingMessages by viewModel.remainingMessages

    val isFloatingWindowShown by viewModel.isFloatingWindowShown

    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_START -> {
                    if (isFloatingWindowShown) {
                        viewModel.toggleFloatingWindow(false)
                    }
                }

                Lifecycle.Event.ON_STOP -> {
                    if (isFloatingWindowShown) {
                        viewModel.toggleFloatingWindow(true)
                    }
                }

                Lifecycle.Event.ON_DESTROY -> {
                    viewModel.toggleFloatingWindow(false)
                }

                else -> {}
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    // Tab选择状态
    var selectedTabIndex by remember { mutableStateOf(0) }
    val tabs = listOf(
        TabItem(
            "账号列表",
            painterResource(id = R.drawable.outline_list_24),
            loginTokens.size
        ),
        TabItem(
            "密码列表",
            painterResource(id = R.drawable.outline_password_24),
            phoneWithPasswords.size
        )
    )

    // 判断是否显示Tab
    val showTabs = remember(loginTokens, phoneWithPasswords) {
        loginTokens.isNotEmpty() && phoneWithPasswords.isNotEmpty()
    }

    // 根据数据状态确定默认选择的Tab
    // 只在初始化或列表状态变化时更新selectedTabIndex
    remember(loginTokens.isEmpty(), phoneWithPasswords.isEmpty()) {
        // 只有当两个列表都为空或者只有一个列表不为空时才重置selectedTabIndex
        if (loginTokens.isEmpty() && phoneWithPasswords.isEmpty()) {
            // 两个列表都为空，默认选择账号列表Tab
            selectedTabIndex = 0
        } else if (loginTokens.isEmpty()) {
            // 只有密码列表有数据
            selectedTabIndex = 1
        } else if (phoneWithPasswords.isEmpty()) {
            // 只有账号列表有数据
            selectedTabIndex = 0
        }
        // 如果两个列表都有数据，保持当前选中的Tab不变
    }

    // 懒加载列表状态
    val lazyListState = rememberLazyListState()

    // 长按选中的令牌
    val longSelectedToken = remember(longClickedTokenUid) {
        loginTokens.find { it.uid == longClickedTokenUid }
    }

    // 账号列表标题
    val displayStr = remember(loginTokens, viewModel.loggingInPhoneNumbersForSms) {
        "账号列表(${viewModel.getLoginTokensInfo()})"
    }

    // 密码列表标题
    val passwordDisplayStr = remember(phoneWithPasswords) {
        "密码列表(${phoneWithPasswords.size})"
    }

    // 是否显示清空账号确认对话框
    var showClearAllTokensConfirmDialog by remember { mutableStateOf(false) }

    // 是否显示清空密码确认对话框
    var showClearAllPasswordsConfirmDialog by remember { mutableStateOf(false) }

    // 账号导出文件选择器
    val accountExportFileLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.CreateDocument("text/plain")
    ) { uri ->
        uri?.let {
            viewModel.exportAccountsToUri(it)
        }
    }

    // 账号导入文件选择器
    val accountImportFileLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.OpenDocument()
    ) { uri ->
        uri?.let {
            viewModel.importAccountsFromUri(it)
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Row(verticalAlignment = Alignment.Bottom) {
                        Text(
                            text = "YH SIGNIN",
                            color = MaterialTheme.colorScheme.onPrimary
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary
                ),
                actions = {
                    // 导入账户按钮
                    IconButton(
                        onClick = {
                            accountImportFileLauncher.launch(arrayOf("text/plain"))
                        },
                        enabled = !isAccountExporting && !isAccountImporting && !isAutoRegistering
                    ) {
                        if (isAccountImporting) {
                            CircularProgressIndicator(
                                modifier = Modifier.padding(8.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                        } else {
                            Icon(
                                painter = painterResource(id = R.drawable.baseline_move_to_inbox_24),
                                contentDescription = null,
                                tint = if (isAccountExporting || isAutoRegistering)
                                    MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.5f)
                                else MaterialTheme.colorScheme.onPrimary
                            )
                        }
                    }

                    // 导出账户按钮
                    IconButton(
                        onClick = {
                            if (loginTokens.isNotEmpty()) {
                                viewModel.setExportAccountDialogVisible(true)
                            }
                        },
                        enabled = !isAccountExporting && !isAccountImporting && !isAutoRegistering && loginTokens.isNotEmpty()
                    ) {
                        if (isAccountExporting) {
                            CircularProgressIndicator(
                                modifier = Modifier.padding(8.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                        } else {
                            Icon(
                                painter = painterResource(id = R.drawable.baseline_outbound_24),
                                contentDescription = null,
                                tint = if (loginTokens.isEmpty() || isAccountImporting || isAutoRegistering)
                                    MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.5f)
                                else MaterialTheme.colorScheme.onPrimary
                            )
                        }
                    }

                    // 获取验证码按钮
                    IconButton(
                        onClick = {
                            viewModel.getSmsCodeFromServer()
                        },
                        enabled = !isAccountExporting && !isAccountImporting && !isAutoRegistering && phoneWithPasswords.isEmpty()
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.baseline_perm_phone_msg_24),
                            contentDescription = null,
                            tint = if (isAccountExporting || isAccountImporting || isAutoRegistering || phoneWithPasswords.isNotEmpty()) MaterialTheme.colorScheme.onPrimary.copy(
                                alpha = 0.5f
                            ) else MaterialTheme.colorScheme.onPrimary
                        )
                    }

                    // 自动注册按钮
                    IconButton(
                        onClick = {
                            if (isAutoRegistering) {
                                viewModel.stopAutoRegistration()
                            } else {
                                viewModel.startAutoRegistration()
                            }

                            viewModel.setFloatingWindowShown(!isFloatingWindowShown)
                        },
                        enabled = !isAccountExporting && !isAccountImporting && phoneWithPasswords.isEmpty()
                    ) {
                        Icon(
                            painter = painterResource(id = if (isAutoRegistering) R.drawable.baseline_pause_circle_24 else R.drawable.baseline_auto_fix_high_24),
                            contentDescription = if (isAutoRegistering) "停止自动注册" else "开始自动注册",
                            tint = if (isAccountExporting || isAccountImporting || !isAutoRegistering)
                                MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.5f)
                            else
                                MaterialTheme.colorScheme.onPrimary
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .padding(paddingValues)
                .fillMaxSize()
                .imePadding()
        ) {
            Column {
                // 手机号、验证码输入框
                if (phoneWithPasswords.isEmpty()) {
                    OutlinedTextField(
                        value = inputStr,
                        onValueChange = { viewModel.setInputStr(it) },
                        label = { Text("手机号----验证码") },
                        shape = RoundedCornerShape(16),
                        singleLine = true,
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = MaterialTheme.colorScheme.primary,
                            unfocusedBorderColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f),
                            cursorColor = MaterialTheme.colorScheme.primary,
                        ),
                        modifier = Modifier
                            .padding(start = 12.dp, end = 12.dp, top = 8.dp)
                            .fillMaxWidth()
                    )
                }

                // 只有当两个列表都不为空时才显示Tab
                if (showTabs) {
                    // 美化的TAB设计
                    EnhancedTabRow(
                        tabs = tabs,
                        selectedTabIndex = selectedTabIndex,
                        onTabSelected = { selectedTabIndex = it }
                    )
                }

                when (selectedTabIndex) {
                    0 -> { // 账号列表Tab
                        // 账号列表标题和搜索
                        if (loginTokens.isNotEmpty()) {
                            AccountListHeader(
                                title = displayStr,
                                onClear = { showClearAllTokensConfirmDialog = true },
                                isQuerying = viewModel.isQuerying.value,
                                setIsQuerying = {
                                    if (it) {
                                        viewModel.setQueryConfirmDialogVisible(true)
                                    } else {
                                        viewModel.setIsQuerying(false)
                                    }
                                }
                            )
                        }

                        // 账号列表
                        ScrollableListWithButtons(
                            lazyListState = lazyListState,
                            itemCount = loginTokens.size,
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f)
                        ) {
                            LazyColumn(
                                state = lazyListState,
                                modifier = Modifier.fillMaxSize()
                            ) {
                                itemsIndexed(loginTokens) { index, loginToken ->
                                    LoginTokenItem(
                                        loginToken = loginToken,
                                        isClicked = clickedTokenUid == loginToken.uid,
                                        isLongClicked = longClickedTokenUid == loginToken.uid,
                                        onClick = {
                                            viewModel.setClickedTokenUid(loginToken.uid)
                                        },
                                        onLongClick = {
                                            viewModel.setLongClickedTokenUid(loginToken.uid)
                                            viewModel.setTokenDetailDialogVisible(true)
                                        },
                                        index = index,
                                        onLogin = {
                                            viewModel.loginWithSmsCode(
                                                loginToken
                                            )
                                        },
                                        isLoading = loggingInPhoneNumbersForSms.contains(loginToken.phoneNumber),
                                    )
                                }
                            }
                        }
                    }

                    1 -> {
                        // 密码列表Tab
                        // 密码列表标题和搜索
                        if (phoneWithPasswords.isNotEmpty()) {
                            PasswordListHeader(
                                title = passwordDisplayStr,
                                isMultiThreadLogin = viewModel.isMultiThreadLogin.value,
                                setIsMultiThreadLogin = { viewModel.setIsMultiThreadLogin(it) },
                                setMultiThreadLoginDialogVisible = {
                                    viewModel.setMultiThreadLoginDialogVisible(
                                        it
                                    )
                                },
                                onClear = { showClearAllPasswordsConfirmDialog = true },
                            )
                        }

                        // 密码列表
                        val passwordListState = rememberLazyListState()
                        ScrollableListWithButtons(
                            lazyListState = passwordListState,
                            itemCount = phoneWithPasswords.size,
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f)
                        ) {
                            LazyColumn(
                                state = passwordListState,
                                modifier = Modifier.fillMaxSize()
                            ) {
                                itemsIndexed(phoneWithPasswords) { index, phoneWithPassword ->
                                    PhoneWithPasswordItem(
                                        phoneWithPassword = phoneWithPassword,
                                        onLogin = {
                                            viewModel.loginWithPassword(
                                                phoneWithPassword.phoneNumber,
                                                phoneWithPassword.password,
                                                index
                                            )
                                        },
                                        isLoading = loggingInPhoneNumbersForPassword.contains(
                                            phoneWithPassword.phoneNumber
                                        ),
                                        index = index
                                    )
                                }
                            }
                        }
                    }
                }
            }

            if (phoneWithPasswords.isEmpty() && !isAutoRegistering) {
                FloatingActionButton(
                    onClick = {
                        viewModel.loginWithSmsCode()
                    },
                    containerColor = MaterialTheme.colorScheme.primary,
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(16.dp),
                    elevation = FloatingActionButtonDefaults.bottomAppBarFabElevation()
                ) {
                    Icon(Icons.AutoMirrored.Filled.ArrowForward, contentDescription = "执行")
                }
            }

            // 自动注册状态指示器
            if (isAutoRegistering) {
                // 旋转的进度指示器
                val infiniteTransition =
                    rememberInfiniteTransition(label = "auto_register_animation")
                val rotation by infiniteTransition.animateFloat(
                    initialValue = 0f,
                    targetValue = 360f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(1500, easing = LinearEasing),
                        repeatMode = RepeatMode.Restart
                    ),
                    label = "rotation"
                )

                // 滑动动画进度
                val slideProgress by infiniteTransition.animateFloat(
                    initialValue = -0.3f,
                    targetValue = 1.3f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(2000, easing = LinearEasing),
                        repeatMode = RepeatMode.Restart
                    ),
                    label = "slide_progress"
                )

                Box(
                    modifier = Modifier
                        .align(Alignment.BottomStart)
                        .padding(16.dp)
                        .background(
                            color = CardContainerColor,
                            shape = RoundedCornerShape(12.dp)
                        )
                        .border(
                            width = 1.dp,
                            color = Color(0xFFFFD700).copy(alpha = 0.5f),
                            shape = RoundedCornerShape(12.dp)
                        )
                        .drawBehind {
                            val gradientWidth = size.width * 0.5f
                            val startX = size.width * slideProgress
                            val cornerRadius = 12.dp.toPx()

                            // 倾斜角度（度数）
                            val angleDegrees = 15f
                            // 转换为弧度并计算斜率
                            val slope = tan(Math.toRadians(angleDegrees.toDouble())).toFloat()

                            // 计算倾斜的起点和终点
                            val heightOffset = size.height * slope
                            val startY = size.height / 2 - heightOffset / 2
                            val endY = size.height / 2 + heightOffset / 2

                            drawRoundRect(
                                brush = Brush.linearGradient(
                                    colors = listOf(
                                        Color.Transparent,
                                        Color(0xFFFFD700).copy(alpha = 0.3f),
                                        Color(0xFF00BCD4).copy(alpha = 0.3f),
                                        Color(0xFF9C27B0).copy(alpha = 0.3f),
                                        Color.Transparent
                                    ),
                                    start = Offset(startX - gradientWidth, startY),
                                    end = Offset(startX, endY)
                                ),
                                cornerRadius = CornerRadius(cornerRadius, cornerRadius)
                            )
                        }
                        .padding(horizontal = 16.dp, vertical = 10.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.outline_sync_24),
                            contentDescription = null,
                            tint = Color(0xFFFFD700),
                            modifier = Modifier
                                .size(20.dp)
                                .graphicsLayer { rotationZ = rotation }
                        )

                        Text(
                            text = "自动注册中" + (if (remainingMessages > 0) "，剩余 $remainingMessages 条" else ""),
                            color = MaterialTheme.colorScheme.onPrimaryContainer,
                            fontSize = 14.sp,
                        )
                    }
                }
            }
        }
    }

    // 显示导出账号筛选对话框
    if (isExportAccountDialogVisible) {
        ExportLoginAccountDialog(
            onDismiss = { viewModel.setExportAccountDialogVisible(false) },
            onConfirm = { filterOption ->
                viewModel.onExportAccountsConfirm(filterOption)
                val timeStamp = SimpleDateFormat(
                    "yyyyMMddHHmmss",
                    java.util.Locale.getDefault()
                ).format(Date())
                accountExportFileLauncher.launch("accounts${timeStamp}.txt")
            },
            tokens = loginTokens
        )
    }

    // 显示Token详情对话框
    if (isTokenDetailDialogVisible && longSelectedToken != null) {
        LoginTokenDetailDialog(
            token = longSelectedToken,
            onDismiss = { viewModel.setTokenDetailDialogVisible(false) },
            onCopy = { viewModel.copyTokenToClipboard(it) },
            onDelete = { viewModel.deleteLoginToken(it) }
        )
    }

    // 清空账号确认对话框
    if (showClearAllTokensConfirmDialog) {
        CommonConfirmDialog(
            title = "确认清空账号",
            content = "确定要清空全部账号吗？",
            onDismiss = { showClearAllTokensConfirmDialog = false },
            onConfirm = {
                viewModel.clearAllLoginTokens()
                showClearAllTokensConfirmDialog = false
                Toast.makeText(context, "已清空全部账号", Toast.LENGTH_SHORT).show()
            }
        )
    }

    // 清空密码确认对话框
    if (showClearAllPasswordsConfirmDialog) {
        CommonConfirmDialog(
            title = "确认清空密码",
            content = "确定要清空全部手机号密码吗？",
            onDismiss = { showClearAllPasswordsConfirmDialog = false },
            onConfirm = {
                viewModel.clearAllPhoneWithPasswords()
                showClearAllPasswordsConfirmDialog = false
                Toast.makeText(context, "已清空全部手机号密码", Toast.LENGTH_SHORT).show()
            }
        )
    }

    // 批量登录对话框
    if (viewModel.isMultiThreadLoginDialogVisible.value) {
        CommonConfirmDialog(
            title = "确认批量登录",
            content = "确定要登录所有未登录的账号吗？",
            onDismiss = { viewModel.setMultiThreadLoginDialogVisible(false) },
            onConfirm = {
                viewModel.setMultiThreadLoginDialogVisible(false)
                viewModel.setIsMultiThreadLogin(true)
                viewModel.doMultiThreadLogin()
            }
        )
    }

    // 查询确认对话框
    if (viewModel.isQueryConfirmDialogVisible.value) {
        CommonConfirmDialog(
            title = "确认查询账号信息",
            content = "确定要查询所有账号信息吗？",
            onDismiss = {
                viewModel.setQueryConfirmDialogVisible(false)
                viewModel.setIsQuerying(false)
            },
            onConfirm = {
                viewModel.setIsQuerying(true)
                viewModel.setQueryConfirmDialogVisible(false)
                viewModel.queryAllAccountInfo()
            }
        )
    }
}

/**
 * 账号列表标题
 */
@Composable
fun AccountListHeader(
    title: String,
    onClear: () -> Unit,
    isQuerying: Boolean,
    setIsQuerying: (Boolean) -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = title,
            modifier = Modifier
                .padding(
                    start = 15.dp,
                    end = 12.dp,
                    top = 8.dp,
                    bottom = 8.dp
                ),
            fontSize = 14.sp,
        )

        Row(
            modifier = Modifier
                .padding(horizontal = 12.dp),
        ) {
            // 清空按钮
            ActionIconButton(
                icon = R.drawable.baseline_delete_forever_24,
                contentDescription = "清空",
                onClick = onClear
            )

            // 查询按钮
            ActionIconButton(
                icon = if (isQuerying) R.drawable.baseline_pause_circle_24 else R.drawable.baseline_query_stats_24,
                contentDescription = "查询",
                onClick = {
                    setIsQuerying(!isQuerying)
                }
            )
        }
    }
}

/**
 * 登录令牌项
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun LoginTokenItem(
    loginToken: LoginTokenEntity,
    isClicked: Boolean,
    isLongClicked: Boolean,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    index: Int,
    onLogin: () -> Unit,
    isLoading: Boolean
) {
    // 根据点击状态选择背景颜色
    val targetColor = when {
        isClicked -> Color(0xCBF7DEF4) // 点击后的颜色标记
        isLongClicked -> Color(0xCCBBDEFB) // 长按后的颜色标记
        else -> CardContainerColor // 默认颜色
    }

    val modifier = Modifier
        .padding(horizontal = 10.dp, vertical = 4.dp)
        .fillMaxWidth()

    val primaryColor = MaterialTheme.colorScheme.primary

    val animatedModifier = if (isLoading) {
        val infiniteTransition = rememberInfiniteTransition(label = "loading_animation")
        val progress by infiniteTransition.animateFloat(
            initialValue = -0.2f, // Start off-screen
            targetValue = 1.2f, // End off-screen
            animationSpec = infiniteRepeatable(
                animation = tween(1500, easing = LinearEasing),
                repeatMode = RepeatMode.Restart
            ),
            label = "progress"
        )
        modifier.drawBehind {
            val gradientWidth = size.width * 0.4f
            val start = size.width * progress
            val cornerRadius = 12.dp.toPx()
            drawRoundRect(
                brush = Brush.horizontalGradient(
                    colors = listOf(
                        CardContainerColor,
                        primaryColor.copy(alpha = 0.4f),
                        CardContainerColor,
                    ),
                    startX = start - gradientWidth,
                    endX = start
                ),
                cornerRadius = CornerRadius(cornerRadius, cornerRadius)
            )
        }
    } else {
        modifier
    }

    Card(
        modifier = animatedModifier,
        colors = CardDefaults.cardColors(containerColor = if (isLoading) Color.Transparent else targetColor),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .combinedClickable(
                    onClick = onClick,
                    onLongClick = onLongClick
                ),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .fillMaxHeight()
                ) {
                    Text(
                        text = loginToken.phoneNumber + if (!loginToken.isLogin) "----${loginToken.smsCode}" else ",${loginToken.uid},${loginToken.updateDate}",
                        maxLines = 1,
                        modifier = Modifier
                            .padding(
                                start = 8.dp,
                                end = 8.dp,
                                top = 18.dp,
                                bottom = 18.dp
                            )
                            .horizontalScroll(rememberScrollState()),
                    )
                    // 显示序号（如果有）
                    Text(
                        text = "#${index + 1}",
                        fontSize = 12.sp,
                        lineHeight = 12.sp,
                        color = Color.Unspecified.copy(0.3f),
                        modifier = Modifier
                            .align(Alignment.TopStart)
                            .padding(start = 4.dp, top = 2.dp)
                    )

                    Text(
                        text = loginToken.extraNote,
                        maxLines = 1,
                        fontSize = 12.sp,
                        lineHeight = 12.sp,
                        overflow = TextOverflow.Ellipsis,
                        color = Color.Unspecified.copy(0.8f),
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .padding(start = 8.dp, end = 4.dp, bottom = 2.dp)
                    )
                }
            }

            IconButton(
                onClick = onLogin,
                enabled = !isLoading && !loginToken.isLogin && !loginToken.isTimeOut
            ) {
                when {
                    isLoading -> {
                        val rotationTransition =
                            rememberInfiniteTransition(label = "rotation_animation")
                        val rotation by rotationTransition.animateFloat(
                            initialValue = 0f,
                            targetValue = 360f,
                            animationSpec = infiniteRepeatable(
                                animation = tween(1000, easing = LinearEasing),
                                repeatMode = RepeatMode.Restart
                            ),
                            label = "icon_rotation"
                        )
                        Icon(
                            painter = painterResource(id = R.drawable.outline_sync_24),
                            contentDescription = "正在登录",
                            modifier = Modifier.graphicsLayer { rotationZ = rotation },
                            tint = primaryColor
                        )
                    }

                    loginToken.isLogin -> {
                        Icon(
                            painter = painterResource(id = R.drawable.outline_cloud_done_24),
                            contentDescription = "已同步登录",
                            tint = Color(0xFF2E7D32) // A dark green color
                        )
                    }

                    loginToken.isTimeOut -> {
                        Icon(
                            painter = painterResource(id = R.drawable.outline_timer_off_24),
                            contentDescription = "验证码已失效",
                            tint = Color.Red
                        )
                    }

                    else -> {
                        Icon(
                            painter = painterResource(id = R.drawable.outline_sync_24),
                            contentDescription = "短信登录",
                            tint = Color.Gray
                        )
                    }
                }
            }
        }
    }
}

/**
 * 密码列表标题
 */
@Composable
fun PasswordListHeader(
    title: String,
    isMultiThreadLogin: Boolean,
    setIsMultiThreadLogin: (Boolean) -> Unit,
    setMultiThreadLoginDialogVisible: (Boolean) -> Unit,
    onClear: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = title,
            modifier = Modifier
                .padding(
                    start = 15.dp,
                    end = 12.dp,
                    top = 8.dp,
                    bottom = 8.dp
                ),
            fontSize = 14.sp,
        )

        Row(
            modifier = Modifier
                .padding(horizontal = 12.dp),
        ) {
            // 清空按钮
            ActionIconButton(
                icon = R.drawable.baseline_delete_forever_24,
                contentDescription = "清空",
                onClick = onClear
            )

            // 批量登录
            ActionIconButton(
                icon = if (!isMultiThreadLogin) R.drawable.outline_cloud_done_24 else R.drawable.baseline_pause_circle_24,
                contentDescription = "批量登录",
                onClick = {
                    if (isMultiThreadLogin) {
                        setIsMultiThreadLogin(false)
                    } else {
                        setMultiThreadLoginDialogVisible(true)
                    }
                }
            )
        }
    }
}

/**
 * 手机号和密码项
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun PhoneWithPasswordItem(
    phoneWithPassword: PhoneWithPasswordEntity,
    onLogin: () -> Unit,
    isLoading: Boolean,
    index: Int
) {
    val modifier = Modifier
        .padding(horizontal = 10.dp, vertical = 4.dp)
        .fillMaxWidth()

    val primaryColor = MaterialTheme.colorScheme.primary

    val animatedModifier = if (isLoading) {
        val infiniteTransition = rememberInfiniteTransition(label = "loading_animation")
        val progress by infiniteTransition.animateFloat(
            initialValue = -0.2f, // Start off-screen
            targetValue = 1.2f, // End off-screen
            animationSpec = infiniteRepeatable(
                animation = tween(1500, easing = LinearEasing),
                repeatMode = RepeatMode.Restart
            ),
            label = "progress"
        )
        modifier.drawBehind {
            val gradientWidth = size.width * 0.4f
            val start = size.width * progress
            val cornerRadius = 12.dp.toPx()
            drawRoundRect(
                brush = Brush.horizontalGradient(
                    colors = listOf(
                        CardContainerColor,
                        primaryColor.copy(alpha = 0.4f),
                        CardContainerColor,
                    ),
                    startX = start - gradientWidth,
                    endX = start
                ),
                cornerRadius = CornerRadius(cornerRadius, cornerRadius)
            )
        }
    } else {
        modifier
    }

    Card(
        modifier = animatedModifier,
        colors = CardDefaults.cardColors(containerColor = if (isLoading) Color.Transparent else CardContainerColor),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .fillMaxHeight()
                ) {
                    Text(
                        text = phoneWithPassword.phoneNumber + "----${phoneWithPassword.password}",
                        maxLines = 1,
                        modifier = Modifier
                            .padding(
                                start = 8.dp,
                                end = 8.dp,
                                top = 18.dp,
                                bottom = 18.dp
                            )
                            .horizontalScroll(rememberScrollState()),
                    )
                    // 显示序号（如果有）
                    Text(
                        text = "#${index + 1}",
                        fontSize = 12.sp,
                        lineHeight = 12.sp,
                        color = Color.Unspecified.copy(0.3f),
                        modifier = Modifier
                            .align(Alignment.TopStart)
                            .padding(start = 4.dp, top = 2.dp)
                    )
                }
            }

            IconButton(onClick = onLogin, enabled = !isLoading && !phoneWithPassword.isLogin) {
                if (isLoading) {
                    val rotationTransition =
                        rememberInfiniteTransition(label = "rotation_animation")
                    val rotation by rotationTransition.animateFloat(
                        initialValue = 0f,
                        targetValue = 360f,
                        animationSpec = infiniteRepeatable(
                            animation = tween(1000, easing = LinearEasing),
                            repeatMode = RepeatMode.Restart
                        ),
                        label = "icon_rotation"
                    )
                    Icon(
                        painter = painterResource(id = R.drawable.outline_sync_24),
                        contentDescription = "正在登录",
                        modifier = Modifier.graphicsLayer { rotationZ = rotation },
                        tint = primaryColor
                    )
                } else if (phoneWithPassword.isLogin) {
                    Icon(
                        painter = painterResource(id = R.drawable.outline_cloud_done_24),
                        contentDescription = "已同步登录",
                        tint = Color(0xFF2E7D32) // A dark green color
                    )
                } else {
                    Icon(
                        painter = painterResource(id = R.drawable.outline_sync_24),
                        contentDescription = "密码登录",
                        tint = Color.Gray
                    )
                }
            }
        }
    }
}

/**
 * Tab项数据类
 */
data class TabItem(
    val title: String,
    val icon: Painter,
    val count: Int
)

/**
 * 美化的Tab行组件
 * @param tabs Tab项列表
 * @param selectedTabIndex 当前选中的Tab索引
 * @param onTabSelected Tab选中回调
 */
@Composable
fun EnhancedTabRow(
    tabs: List<TabItem>,
    selectedTabIndex: Int,
    onTabSelected: (Int) -> Unit
) {
    TabRow(
        selectedTabIndex = selectedTabIndex,
        containerColor = Color.Transparent,
        contentColor = MaterialTheme.colorScheme.primary,
        divider = { /* 移除默认分割线 */ },
        indicator = { /* 移除指示器 */ }
    ) {
        tabs.forEachIndexed { index, tabItem ->
            val isSelected = selectedTabIndex == index

            // 动画效果
            val animatedScale by animateFloatAsState(
                targetValue = if (isSelected) 1.03f else 1.0f,
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessLow
                ),
                label = "tab_scale"
            )

            val animatedAlpha by animateFloatAsState(
                targetValue = if (isSelected) 1.0f else 0.7f,
                animationSpec = tween(durationMillis = 300),
                label = "tab_alpha"
            )

            Tab(
                selected = isSelected,
                onClick = { onTabSelected(index) },
                modifier = Modifier
                    .padding(vertical = 10.dp, horizontal = 6.dp)
                    .clip(RoundedCornerShape(10.dp))
                    .graphicsLayer {
                        scaleX = animatedScale
                        scaleY = animatedScale
                        alpha = animatedAlpha
                    }
                    .background(
                        if (isSelected) {
                            Brush.verticalGradient(
                                colors = listOf(
                                    TabSelectedGradientStart.copy(alpha = 0.15f),
                                    TabSelectedGradientEnd.copy(alpha = 0.08f)
                                )
                            )
                        } else {
                            Brush.verticalGradient(
                                colors = listOf(
                                    Color.Transparent,
                                    Color.Transparent
                                )
                            )
                        }
                    )
                    .border(
                        width = if (isSelected) 1.dp else 0.dp,
                        color = if (isSelected)
                            TabSelectedGradientStart.copy(alpha = 0.3f)
                        else
                            Color.Transparent,
                        shape = RoundedCornerShape(10.dp)
                    ),
                text = {
                    Text(
                        text = tabItem.title,
                        fontSize = if (isSelected) 13.sp else 12.sp,
                        fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        color = if (isSelected)
                            TabSelectedGradientStart
                        else
                            TabUnselectedColor,
                        modifier = Modifier.padding(top = 1.dp)
                    )
                },
                icon = {
                    BadgedBox(
                        badge = {
                            if (tabItem.count > 0) {
                                Badge(
                                    containerColor = if (isSelected)
                                        TabBadgeSelectedColor
                                    else
                                        TabBadgeUnselectedColor,
                                    contentColor = Color.White,
                                    modifier = Modifier
                                        .shadow(
                                            elevation = 1.dp,
                                            shape = RoundedCornerShape(6.dp)
                                        )
                                ) {
                                    Text(
                                        text = tabItem.count.toString(),
                                        fontSize = 8.sp,
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                            }
                        }
                    ) {
                        Icon(
                            painter = tabItem.icon,
                            contentDescription = null,
                            tint = if (isSelected)
                                TabSelectedGradientStart
                            else
                                TabUnselectedColor
                        )
                    }
                },
                selectedContentColor = MaterialTheme.colorScheme.primary,
                unselectedContentColor = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
fun CommonConfirmDialog(
    title: String,
    content: String,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        containerColor = dialogContainerColor(),
        onDismissRequest = onDismiss,
        title = { Text(title) },
        text = { Text(content) },
        confirmButton = {
            Button(
                onClick = {
                    onConfirm()
                },
                colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error)
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            Button(onClick = { onDismiss() }) {
                Text("取消")
            }
        }
    )
}

/**
 * 登录令牌详情对话框
 * @param token 登录令牌实体
 * @param onDismiss 关闭对话框回调
 * @param onDelete 删除令牌回调
 */
@Composable
fun LoginTokenDetailDialog(
    token: LoginTokenEntity,
    onDismiss: () -> Unit,
    onCopy: (LoginTokenEntity) -> Unit,
    onDelete: (LoginTokenEntity) -> Unit
) {
    val context = LocalContext.current

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("账号信息") },
        containerColor = dialogContainerColor(),
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                // 操作按钮部分
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xCBDEE6F7)
                    ),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row {
                        // 删除按钮
                        TokenActionButton(
                            imageVector = Icons.Filled.Delete,
                            text = "删除账号",
                            onClick = {
                                onDelete(token)
                                onDismiss()
                                Toast.makeText(context, "已删除账号", Toast.LENGTH_SHORT).show()
                            },
                            tint = MaterialTheme.colorScheme.error
                        )

                        // 复制按钮
                        TokenActionButton(
                            icon = R.drawable.baseline_copy_all_24,
                            text = "复制账号",
                            onClick = {
                                onCopy(token)
                            }
                        )
                    }
                }

                // 信息展示部分
                Column(
                    modifier = Modifier
                        .padding(8.dp)
                        .fillMaxWidth()
                ) {
                    Text("手机号: ${token.phoneNumber}")
                    token.uid.takeIf { it.isNotEmpty() }?.let { Text("UID: $it") }
                    token.updateDate.takeIf { it.isNotEmpty() }?.let { Text("更新日期: $it") }
                    token.smsCode.takeIf { it.isNotEmpty() }?.let { Text("验证码: $it") }
                    token.extraNote.takeIf { it.isNotEmpty() }?.let {
                        Text("备注: $it")
                    }
                    Text("应用参数: ${token.appParam}")
                }
            }
        },
        confirmButton = {},
        dismissButton = null
    )
}

/**
 * 可滚动列表组件，带有顶部和底部滚动按钮
 */
@Composable
fun ScrollableListWithButtons(
    lazyListState: LazyListState,
    itemCount: Int,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
    ) {
        // 列表内容
        content()

        // 滚动按钮
        val coroutineScope = rememberCoroutineScope()

        // 显示回到顶部按钮的条件
        val showScrollToTopButton by remember {
            derivedStateOf {
                lazyListState.firstVisibleItemIndex > 5
            }
        }

        // 显示滚动到底部按钮的条件
        val showScrollToBottomButton by remember {
            derivedStateOf {
                lazyListState.firstVisibleItemIndex > 5
            }
        }

        // 滚动按钮容器
        Column(
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .padding(end = 16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 回到顶部按钮
            AnimatedVisibility(
                visible = showScrollToTopButton,
                enter = fadeIn(animationSpec = tween(300)) + scaleIn(animationSpec = tween(300)),
                exit = fadeOut(animationSpec = tween(300)) + scaleOut(animationSpec = tween(300))
            ) {
                Card(
                    onClick = {
                        coroutineScope.launch {
                            lazyListState.animateScrollToItem(0)
                        }
                    },
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.85f)
                    ),
                    shape = RoundedCornerShape(12.dp),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 2.dp,
                        pressedElevation = 4.dp
                    ),
                    modifier = Modifier
                        .size(36.dp)
                ) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.fillMaxSize()
                    ) {
                        Icon(
                            imageVector = Icons.Filled.KeyboardArrowUp,
                            contentDescription = "回到顶部",
                            tint = Color.White,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(4.dp))

            // 滚动到底部按钮
            AnimatedVisibility(
                visible = showScrollToBottomButton,
                enter = fadeIn(animationSpec = tween(300)) + scaleIn(animationSpec = tween(300)),
                exit = fadeOut(animationSpec = tween(300)) + scaleOut(animationSpec = tween(300))
            ) {
                Card(
                    onClick = {
                        coroutineScope.launch {
                            lazyListState.animateScrollToItem(itemCount - 1)
                        }
                    },
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.85f)
                    ),
                    shape = RoundedCornerShape(12.dp),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 2.dp,
                        pressedElevation = 4.dp
                    ),
                    modifier = Modifier
                        .size(36.dp)
                ) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.fillMaxSize()
                    ) {
                        Icon(
                            imageVector = Icons.Filled.KeyboardArrowDown,
                            contentDescription = "滚动到底部",
                            tint = Color.White,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }
        }
    }
}












