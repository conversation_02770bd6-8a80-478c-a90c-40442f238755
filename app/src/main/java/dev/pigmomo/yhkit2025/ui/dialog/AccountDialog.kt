package dev.pigmomo.yhkit2025.ui.dialog

import android.annotation.SuppressLint
import android.app.Application
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.MailOutline
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat.startActivity
import coil.compose.rememberAsyncImagePainter
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.api.model.boostcoupon.BoostCouponRecord
import dev.pigmomo.yhkit2025.api.model.card.CardItem
import dev.pigmomo.yhkit2025.api.model.coupon.CouponListCoupon
import dev.pigmomo.yhkit2025.api.model.credit.CreditData
import dev.pigmomo.yhkit2025.api.model.credit.CreditDetail
import dev.pigmomo.yhkit2025.api.model.invitatinv2.InvitationReward
import dev.pigmomo.yhkit2025.api.model.order.Order
import dev.pigmomo.yhkit2025.api.model.invitatinv2.SuccessInvite
import dev.pigmomo.yhkit2025.api.model.user.ShopInfo
import dev.pigmomo.yhkit2025.ui.components.AccountTabs
import dev.pigmomo.yhkit2025.ui.components.BoostCouponContent
import dev.pigmomo.yhkit2025.ui.components.CouponContent
import dev.pigmomo.yhkit2025.ui.components.CreditContent
import dev.pigmomo.yhkit2025.ui.components.InvitationContent
import dev.pigmomo.yhkit2025.ui.components.OrderContent
import dev.pigmomo.yhkit2025.ui.components.TabBar
import dev.pigmomo.yhkit2025.ui.components.TabItem
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.components.YhCardContent
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import org.json.JSONObject
import androidx.core.net.toUri
import dev.pigmomo.yhkit2025.api.model.handluck.HandLuckReward
import java.time.format.DateTimeFormatter
import java.time.Instant
import java.time.ZoneId

/**
 * 账户信息对话框
 * 显示账户相关信息，包括永辉卡、优惠券、助力券、订单、邀请有礼和积分等
 *
 * @param onDismiss 对话框关闭回调
 * @param cardList 卡片列表
 * @param cardBalance 卡片余额
 * @param couponList 可用优惠券列表
 * @param unavailableCouponList 不可用优惠券列表
 * @param boostCouponList 助力券列表
 * @param orderList 订单列表
 * @param hasNextPage 是否有下一页
 * @param lastOrderId 最后一页的最后一个订单ID
 * @param invitationRewards 邀请奖励列表
 * @param successInvites 成功邀请列表
 * @param creditData 积分数据
 * @param creditDetails 积分详情列表
 * @param creditCount 积分数量
 * @param onGetAllCard 刷新永辉卡回调
 * @param onGetCardIndexInfo 获取卡片索引信息回调
 * @param onShowSendCardDialog 赠送卡回调
 * @param onSimpleSendCard 简单赠送卡回调
 * @param onCancelSendCard 取消简单赠送卡回调
 * @param onShowCancelSendCardDialog 显示取消赠送卡对话框回调
 * @param onGetCouponList 获取优惠券列表回调
 * @param onShowCouponDetailDialog 显示优惠券详情对话框回调
 * @param onGetBoostCouponList 获取助力券列表回调
 * @param onGetGameCode 获取游戏代码回调
 * @param onShowBoostCouponDetailDialog 显示助力券详情对话框回调
 * @param onGetOrderList 获取订单列表回调
 * @param onGetOrderDetail 获取订单详情回调
 * @param onClearOrder 清空订单回调
 * @param onGetInvitationRewards 获取邀请奖励回调
 * @param onGetSuccessInvites 获取成功邀请回调
 * @param onGetCreditDetail 获取积分详情回调
 * @param onSignRewardRule 获取签到奖励详情回调
 * @param onBindCard 绑定卡回调
 * @param onGetNewPersonCoupon 获取新人优惠券回调
 * @param onKindCoupon 领取优惠券回调
 * @param onHandLuck 拼手气回调
 * @param onEditBoostConfig 编辑助力券配置回调
 * @param onSaveBoostConfig 保存助力券配置回调
 * @param onQueryOrder 查询订单回调
 * @param onGetInvitationRules 获取邀请规则回调
 * @param onCopyInvitationCode 复制邀请码回调
 * @param onBindInvitationCode 绑定邀请码回调
 */
@SuppressLint("MutableCollectionMutableState")
@Composable
fun AccountDialog(
    onDismiss: () -> Unit,
    cardList: List<CardItem>,
    cardBalance: String,
    couponList: List<CouponListCoupon>,
    unavailableCouponList: List<CouponListCoupon>,
    boostCouponList: List<BoostCouponRecord>,
    orderList: List<Order>,
    hasNextPage: Boolean,
    lastOrderId: String,
    invitationRewards: List<InvitationReward>,
    successInvites: List<SuccessInvite>,
    creditData: CreditData?,
    creditDetails: List<CreditDetail>,
    creditCount: Int,
    onGetAllCard: () -> Unit,
    onGetCardIndexInfo: () -> Unit,
    onShowSendCardDialog: (CardItem) -> Unit,
    onSimpleSendCard: (CardItem) -> Unit,
    onCancelSendCard: (CardItem) -> Unit,
    onShowCancelSendCardDialog: (CardItem) -> Unit,
    onGetCouponList: () -> Unit,
    onShowCouponDetailDialog: (CouponListCoupon) -> Unit,
    onGetBoostCouponList: () -> Unit,
    onGetGameCode: (String) -> Unit,
    onShowBoostCouponDetailDialog: (BoostCouponRecord) -> Unit,
    onGetOrderList: (String) -> Unit,
    onGetOrderDetail: (String) -> Unit,
    onClearOrder: () -> Unit,
    onGetInvitationRewards: (Int, Int) -> Unit,
    onGetSuccessInvites: (Int, Int) -> Unit,
    onGetCreditDetail: (Int) -> Unit,
    onPointTeam: () -> Unit,
    onSignRewardRule: () -> Unit,
    onShowJoinPointTeamDialog: () -> Unit,
    onBindCard: () -> Unit = {},
    onGetNewPersonCoupon: () -> Unit = {},
    onKindCoupon: () -> Unit = {},
    onHandLuck: () -> Unit = {},
    onEditBoostConfig: () -> Unit = {},
    onSaveBoostConfig: () -> Unit = {},
    onQueryOrder: () -> Unit = {},
    onGetInvitationRules: () -> Unit = {},
    onCopyInvitationCode: () -> Unit = {},
    onBindInvitationCode: () -> Unit = {}
) {
    // 当前选中的标签
    var selectedTabIndex by remember { mutableIntStateOf(0) }

    // 定义标签列表
    val tabs = remember(creditData, cardBalance) {
        listOf(
            TabItem(name = AccountTabs.YH_CARD, badge = cardBalance),
            TabItem(name = AccountTabs.COUPON),
            TabItem(name = AccountTabs.BOOST_COUPON),
            TabItem(name = AccountTabs.ORDER),
            TabItem(name = AccountTabs.INVITATION),
            TabItem(name = AccountTabs.CREDIT, badge = (creditData?.credit ?: 0).toString())
        )
    }

    // 初始化卡片数据
    remember {
        if (cardList.isEmpty()) {
            onGetAllCard()
            onGetCardIndexInfo()
            true
        } else {
            false
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("账号信息") },
        containerColor = dialogContainerColor(),
        text = {
            Column {
                // 标签栏
                TabBar(
                    tabs = tabs,
                    selectedTabIndex = selectedTabIndex,
                    onTabSelected = { index ->
                        selectedTabIndex = index

                        // 根据选中的标签加载数据
                        when (index) {
                            0 -> {
                                if (cardList.isEmpty()) {
                                    onGetAllCard()
                                    onGetCardIndexInfo()
                                }
                            }

                            1 -> if (couponList.isEmpty() && unavailableCouponList.isEmpty()) onGetCouponList()
                            2 -> if (boostCouponList.isEmpty()) onGetBoostCouponList()
                            3 -> if (orderList.isEmpty()) onGetOrderList("")
                            4 -> if (invitationRewards.isEmpty() && successInvites.isEmpty()) {
                                onGetInvitationRewards(1, 10)
                                onGetSuccessInvites(1, 10)
                            }

                            5 -> if (creditDetails.isEmpty()) onGetCreditDetail(0)
                        }
                    }
                )

                // 根据选中的标签显示相应内容
                when (selectedTabIndex) {
                    0 -> YhCardContent(
                        cardList = cardList,
                        onRefresh = {
                            onGetAllCard()
                            onGetCardIndexInfo()
                        },
                        onBindCard = onBindCard,
                        onShowSendCardDialog = onShowSendCardDialog,
                        onSimpleSendCard = onSimpleSendCard,
                        onCancelSendCard = onCancelSendCard,
                        onShowCancelSendCardDialog = onShowCancelSendCardDialog
                    )

                    1 -> CouponContent(
                        availableCoupons = couponList,
                        unavailableCoupons = unavailableCouponList,
                        onRefresh = onGetCouponList,
                        onGetNewPersonCoupon = onGetNewPersonCoupon,
                        onReceiveCoupon = onKindCoupon,
                        onHandLuck = onHandLuck,
                        onShowCouponDetailDialog = onShowCouponDetailDialog
                    )

                    2 -> BoostCouponContent(
                        boostCouponList = boostCouponList,
                        onRefresh = onGetBoostCouponList,
                        onGetGameCode = onGetGameCode,
                        onEditBoostConfig = onEditBoostConfig,
                        onSaveBoostConfig = onSaveBoostConfig,
                        onShowBoostCouponDetailDialog = onShowBoostCouponDetailDialog
                    )

                    3 -> OrderContent(
                        orderList = orderList,
                        hasNextPage = hasNextPage,
                        lastOrderId = lastOrderId,
                        onLoadMore = { onGetOrderList(lastOrderId) },
                        onRefresh = { onGetOrderList("") },
                        onGetOrderDetail = onGetOrderDetail,
                        onQueryOrder = onQueryOrder,
                        onClearOrder = onClearOrder
                    )

                    4 -> InvitationContent(
                        invitationRewards = invitationRewards,
                        successInvites = successInvites,
                        onRefreshRewards = {
                            onGetInvitationRewards(1, 10)
                            onGetSuccessInvites(1, 10)
                        },
                        onCopyInvitationCode = onCopyInvitationCode,
                        onGetInvitationRules = onGetInvitationRules,
                        onBindInvitationCode = onBindInvitationCode
                    )

                    5 -> CreditContent(
                        creditData = creditData,
                        creditDetails = creditDetails,
                        creditCount = creditCount,
                        onLoadMore = { page -> onGetCreditDetail(page) },
                        onRefresh = { onGetCreditDetail(0) },
                        onPointTeam = onPointTeam,
                        onSignRewardRule = onSignRewardRule,
                        onShowJoinPointTeamDialog = onShowJoinPointTeamDialog
                    )
                }
            }
        },
        confirmButton = { /* 不显示确认按钮 */ },
        dismissButton = null
    )
}

/**
 * 绑定永辉卡对话框
 */
@Composable
fun BindCardDialog(
    onDismiss: () -> Unit,
    onBindCard: (String) -> Unit,
    onReceiveCard: (String) -> Unit,
    onValidateCard: (String, (String) -> Unit) -> Unit
) {
    val context = LocalContext.current
    var bindCardInfo by remember { mutableStateOf("") }
    var bindCardBalance by remember { mutableStateOf("") }
    var confirmButtonText by remember { mutableStateOf("粘贴解析") }

    AlertDialog(
        onDismissRequest = {
            onDismiss()
        },
        title = { Text("绑定永辉卡") },
        containerColor = dialogContainerColor(),
        text = {
            Column {
                OutlinedTextField(
                    value = bindCardInfo,
                    onValueChange = {
                        bindCardInfo = it
                        // 解析转赠链接
                        if (bindCardInfo.contains("yhPackageId")) {
                            val yhPackageIdRegex = Regex("yhPackageId=([^&]+)")
                            val matchResult = yhPackageIdRegex.find(bindCardInfo)
                            if (matchResult != null) {
                                val yhPackageId = matchResult.groupValues[1]
                                bindCardInfo = "0000,$yhPackageId,0000"
                                confirmButtonText = "领取"
                                Toast.makeText(
                                    context,
                                    "参数解析成功",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        }
                        // 解析卡号和卡密格式
                        val cardNumberPasswordRegex = Regex("卡号：(\\d+)\\s+卡密：(\\d+)")
                        val cardNumPwdMatch = cardNumberPasswordRegex.find(bindCardInfo)
                        if (cardNumPwdMatch != null) {
                            val cardNumber = cardNumPwdMatch.groupValues[1]
                            val cardPassword = cardNumPwdMatch.groupValues[2]
                            bindCardInfo = "$cardNumber,$cardPassword"
                            confirmButtonText = "查询"
                            Toast.makeText(
                                context,
                                "卡号卡密解析成功",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                        if (bindCardInfo.contains(",")) {
                            when (bindCardInfo.split(",").size) {
                                3 -> confirmButtonText = "领取"
                                2 -> confirmButtonText = "查询"
                            }
                        } else {
                            confirmButtonText = if (bindCardInfo.startsWith("http")) {
                                "查询"
                            } else {
                                "粘贴解析"
                            }
                        }
                    },
                    label = { Text(if (bindCardBalance.isNotEmpty()) "余额: $bindCardBalance" else "永辉卡参数") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (bindCardInfo.isEmpty() || confirmButtonText == "粘贴解析") {
                        // 获取剪切板文本
                        val clipboard =
                            context.applicationContext.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                        val clipData = clipboard.primaryClip
                        if (clipData != null && clipData.itemCount > 0) {
                            val text = clipData.getItemAt(0).text
                            if (text != null) {
                                bindCardInfo = text.toString()
                                val yhPackageIdRegex = Regex("yhPackageId=([^&]+)")
                                val matchResult = yhPackageIdRegex.find(bindCardInfo)
                                if (matchResult != null) {
                                    val yhPackageId = matchResult.groupValues[1]
                                    bindCardInfo = "0000,$yhPackageId,0000"
                                }
                                // 解析卡号和卡密格式
                                val cardNumberPasswordRegex = Regex("卡号：(\\d+)\\s+卡密：(\\d+)")
                                val cardNumPwdMatch = cardNumberPasswordRegex.find(bindCardInfo)
                                if (cardNumPwdMatch != null) {
                                    val cardNumber = cardNumPwdMatch.groupValues[1]
                                    val cardPassword = cardNumPwdMatch.groupValues[2]
                                    bindCardInfo = "$cardNumber,$cardPassword"
                                    confirmButtonText = "查询"
                                    return@Button
                                }
                                if (bindCardInfo.contains(",")) {
                                    when (bindCardInfo.split(",").size) {
                                        3 -> confirmButtonText = "领取"
                                        2 -> confirmButtonText = "查询"
                                    }
                                } else {
                                    confirmButtonText =
                                        if (bindCardInfo.startsWith("http")) {
                                            "查询"
                                        } else {
                                            "粘贴解析"
                                        }
                                }
                            }
                        }
                        return@Button
                    }
                    if (!bindCardInfo.contains(",") && !bindCardInfo.startsWith("http")) {
                        Toast.makeText(
                            context,
                            "参数格式不正确",
                            Toast.LENGTH_SHORT
                        ).show()
                        return@Button
                    }
                    // 领取逻辑
                    if (bindCardInfo.contains(",") && bindCardInfo.split(",").size == 3) {
                        onReceiveCard(bindCardInfo)
                        return@Button
                    }
                    // 绑卡逻辑
                    if (bindCardBalance.isEmpty() && (bindCardInfo.split(",").size == 2 || bindCardInfo.startsWith(
                            "http"
                        ))
                    ) {
                        onValidateCard(bindCardInfo) { balance ->
                            bindCardBalance = balance
                            confirmButtonText = "绑定"
                        }
                        return@Button
                    }
                    if (bindCardInfo.split(",").size == 2 || bindCardInfo.startsWith("http")) {
                        onBindCard(bindCardInfo)
                    }
                }
            ) {
                Text(confirmButtonText)
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        }
    )
}

/**
 * 新人优惠券对话框
 */
@Composable
fun NewPersonCouponDialog(
    onDismiss: () -> Unit,
    currentShopList: List<ShopInfo>,
    newPersonCouponShopParams: String,
    onSaveConfig: (String) -> Unit,
    onGetActivityRule: (String, String) -> Unit,
    onConfirm: (String, String) -> Unit,
    getPreConfigShopParams: () -> List<JSONObject>
) {
    val context = LocalContext.current
    var confirmClick by remember { mutableStateOf(false) }
    var preConfigListStatus by remember { mutableStateOf(false) }
    val preConfigList = getPreConfigShopParams()
    var tempShopParams by remember { mutableStateOf(newPersonCouponShopParams.ifEmpty { currentShopList[0].sellerid.toString() + "," + currentShopList[0].shopid }) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("领取新人优惠券") },
        containerColor = dialogContainerColor(),
        text = {
            Column {
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row {
                        TokenActionButton(
                            icon = R.drawable.baseline_save_24,
                            text = "账号配置",
                            onClick = { onSaveConfig(tempShopParams) }
                        )
                        TokenActionButton(
                            imageVector = Icons.Filled.Info,
                            text = "规则",
                            onClick = {
                                val shopParamsParts = tempShopParams.split(",")
                                if (shopParamsParts.size < 2) {
                                    Toast.makeText(context, "店铺参数不正确", Toast.LENGTH_SHORT)
                                        .show()
                                    return@TokenActionButton
                                }
                                onGetActivityRule(shopParamsParts[0], shopParamsParts[1])
                            }
                        )
                        Box {
                            TokenActionButton(
                                imageVector = Icons.AutoMirrored.Filled.List,
                                text = "预置",
                                onClick = { preConfigListStatus = true }
                            )

                            DropdownMenu(
                                expanded = preConfigListStatus,
                                onDismissRequest = { preConfigListStatus = false }
                            ) {
                                preConfigList.forEach { pre ->
                                    val preName = pre.getString("preName")
                                    DropdownMenuItem(
                                        text = {
                                            Row(verticalAlignment = Alignment.CenterVertically) {
                                                Text(
                                                    preName,
                                                    modifier = Modifier
                                                        .weight(1f)
                                                        .horizontalScroll(rememberScrollState())
                                                )
                                            }
                                        },
                                        onClick = {
                                            tempShopParams =
                                                pre.getString("newPersonPopupShopParams")
                                            preConfigListStatus = false
                                        }
                                    )
                                }
                            }
                        }
                    }
                }

                OutlinedTextField(
                    value = tempShopParams,
                    onValueChange = { tempShopParams = it },
                    label = { Text("店铺参数") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    val shopParamsParts = tempShopParams.split(",")
                    if (shopParamsParts.size < 2) {
                        Toast.makeText(context, "店铺参数格式不正确", Toast.LENGTH_SHORT).show()
                        return@Button
                    }

                    // 检查当前店铺ID与选中店铺ID是否一致
                    val targetShopId = shopParamsParts[1]

                    if (!confirmClick && targetShopId != currentShopList[0].shopid && currentShopList[0].shopid.isNotEmpty()) {
                        Toast.makeText(
                            context,
                            "领取非地址新人券，请再次点击确认",
                            Toast.LENGTH_SHORT
                        ).show()
                        confirmClick = true
                        return@Button
                    }

                    onConfirm(shopParamsParts[0], shopParamsParts[1])
                },
                enabled = tempShopParams.isNotEmpty()
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        }
    )
}

/**
 * 活动规则对话框
 */
@Composable
fun ActivityRuleDialog(
    rule: String,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("活动规则") },
        containerColor = dialogContainerColor(),
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                Text(
                    text = rule,
                    modifier = Modifier
                        .fillMaxWidth()
                        .horizontalScroll(rememberScrollState())
                )
            }
        },
        confirmButton = { }
    )
}

/**
 * 领取优惠券对话框
 */
@Composable
fun KindCouponDialog(
    onDismiss: () -> Unit,
    couponPromotionCodeStr: String,
    onSaveConfig: (String) -> Unit,
    onGetNewUserCouponInfo: ((String) -> Unit) -> Unit,
    onConfirm: (List<String>) -> Unit,
    getPreConfigKindCouponList: () -> List<JSONObject>
) {
    val context = LocalContext.current
    var tempCouponPromotionCodeStr by remember(couponPromotionCodeStr) {
        mutableStateOf(
            couponPromotionCodeStr
        )
    }
    var preConfigListStatus by remember { mutableStateOf(false) }
    val preConfigList = getPreConfigKindCouponList()

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("领取优惠券") },
        containerColor = dialogContainerColor(),
        text = {
            Column {
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row {
                        TokenActionButton(
                            icon = R.drawable.baseline_save_24,
                            text = "账号配置",
                            onClick = { onSaveConfig(tempCouponPromotionCodeStr) }
                        )
                        TokenActionButton(
                            icon = R.drawable.baseline_groups_24,
                            text = "社群新人券",
                            onClick = {
                                onGetNewUserCouponInfo { code ->
                                    if (code.isNotEmpty()) {
                                        tempCouponPromotionCodeStr = code
                                    }
                                }
                            }
                        )

                        Box {
                            TokenActionButton(
                                imageVector = Icons.AutoMirrored.Filled.List,
                                text = "预置",
                                onClick = { preConfigListStatus = true }
                            )

                            DropdownMenu(
                                expanded = preConfigListStatus,
                                onDismissRequest = { preConfigListStatus = false }
                            ) {
                                preConfigList.forEach { pre ->
                                    val preName = pre.getString("preName")
                                    DropdownMenuItem(
                                        text = {
                                            Row(verticalAlignment = Alignment.CenterVertically) {
                                                Text(
                                                    preName,
                                                    modifier = Modifier
                                                        .weight(1f)
                                                        .horizontalScroll(rememberScrollState())
                                                )
                                            }
                                        },
                                        onClick = {
                                            tempCouponPromotionCodeStr =
                                                pre.getString("couponPromotionCodeStr")
                                            preConfigListStatus = false
                                        }
                                    )
                                }
                            }
                        }
                    }
                }

                OutlinedTextField(
                    value = tempCouponPromotionCodeStr,
                    onValueChange = { tempCouponPromotionCodeStr = it },
                    label = { Text("优惠券参数") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    val kindCouponList = tempCouponPromotionCodeStr.split(";")
                    onConfirm(kindCouponList)
                },
                enabled = tempCouponPromotionCodeStr.isNotEmpty()
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        }
    )
}

/**
 * 送达照片对话框
 */
@Composable
fun DeliveryPhotosDialog(
    photos: List<String>,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("送达照片") },
        text = {
            Column(
                modifier = Modifier
                    .verticalScroll(rememberScrollState())
                    .padding(vertical = 8.dp)
            ) {
                photos.forEach { photoUrl ->
                    Card(
                        colors = CardDefaults.cardColors(),
                        modifier = Modifier
                            .padding(vertical = 8.dp)
                            .fillMaxWidth()
                    ) {
                        Image(
                            painter = rememberAsyncImagePainter(photoUrl),
                            contentDescription = "送达照片",
                            contentScale = ContentScale.FillWidth,
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(max = 600.dp)
                                .clip(RoundedCornerShape(8.dp))
                        )
                    }
                }
            }
        },
        confirmButton = { },
        containerColor = dialogContainerColor()
    )
}

/**
 * 拼手气对话框
 */
@Composable
fun HandLuckDialog(
    onDismiss: () -> Unit,
    handLuckEventIdStr: String,
    onEventIdChange: (String) -> Unit,
    onGetActivityRule: (String) -> Unit,
    onGetActivityRewardList: (String) -> Unit,
    onConfirm: (String) -> Unit
) {
    val context = LocalContext.current
    var tempEventId by remember { mutableStateOf(handLuckEventIdStr) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("拼手气参与") },
        containerColor = dialogContainerColor(),
        text = {
            Column {
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row {
                        TokenActionButton(
                            imageVector = Icons.Filled.Info,
                            text = "规则",
                            onClick = {
                                if (tempEventId.isEmpty()) {
                                    Toast.makeText(context, "拼手气参数为空", Toast.LENGTH_SHORT)
                                        .show()
                                    return@TokenActionButton
                                }
                                onGetActivityRule(tempEventId)
                            }
                        )
                        TokenActionButton(
                            imageVector = Icons.AutoMirrored.Filled.List,
                            text = "奖励列表",
                            onClick = {
                                if (tempEventId.isEmpty()) {
                                    Toast.makeText(context, "拼手气参数为空", Toast.LENGTH_SHORT)
                                        .show()
                                    return@TokenActionButton
                                }
                                onGetActivityRewardList(tempEventId)
                            }
                        )
                    }
                }

                OutlinedTextField(
                    value = tempEventId,
                    onValueChange = {
                        // 解析eventId参数
                        val input = it
                        val eventId = Regex("eventId=(\\d+)").find(input)?.groups?.get(1)?.value
                        tempEventId = eventId ?: input
                        onEventIdChange(tempEventId)
                    },
                    label = { Text("拼手气参数") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    onConfirm(tempEventId)
                },
                enabled = tempEventId.isNotEmpty()
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        }
    )
}

/**
 * 拼手气奖励列表对话框
 */
@Composable
fun HandLuckRewardListDialog(
    onDismiss: () -> Unit,
    rewardList: List<HandLuckReward>
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("拼手气奖励列表") },
        containerColor = dialogContainerColor(),
        text = {
            LazyColumn {
                items(rewardList) { reward ->
                    val nickName = reward.nickName
                    //格式化时间戳 1749739940000
                    val receiveTime = reward.receiveTime
                    val receiveTimeStr =
                        Instant.ofEpochMilli(receiveTime).atZone(ZoneId.systemDefault())
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                    val rewardValue = reward.rewardValue
                    val couponType = when (reward.couponLatitude) {
                        "redEnvelope.coupon" -> "红包"
                        else -> "优惠券"
                    }

                    val couponDesc = if (reward.couponDesc != "现金券") reward.couponDesc else ""

                    val luckiest = if (reward.luckiest == 1) "最幸运" else ""

                    Card(
                        colors = cardThemeOverlay(),
                        modifier = Modifier.padding(top = 2.dp, bottom = 2.dp)
                    ) {
                        Column(
                            verticalArrangement = Arrangement.Center,
                            modifier = Modifier
                                .padding(
                                    start = 8.dp,
                                    top = 12.dp,
                                    end = 6.dp,
                                    bottom = 12.dp
                                )
                                .fillMaxWidth()
                        ) {
                            Text(
                                "$nickName $rewardValue$couponType $couponDesc $luckiest",
                                fontSize = 16.sp,
                                lineHeight = 16.sp,
                                modifier = Modifier.horizontalScroll(rememberScrollState())
                            )
                            Text(
                                "领取时间: $receiveTimeStr",
                                fontSize = 12.sp,
                                lineHeight = 12.sp,
                            )
                        }
                    }
                }
            }
        },
        confirmButton = { },
    )
}

/**
 * 助力券配置对话框
 */
@Composable
fun BoostCouponConfigDialog(
    onDismiss: () -> Unit,
    boostCouponHelpData: List<String>,
    onSaveConfig: (String) -> Unit
) {
    var boostCouponHelpDataStr by remember { mutableStateOf(boostCouponHelpData.joinToString(",")) }
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("助力券配置") },
        containerColor = dialogContainerColor(),
        text = {
            OutlinedTextField(
                value = boostCouponHelpDataStr,
                onValueChange = { boostCouponHelpDataStr = it },
                label = { Text("XXXX,XXXX") },
                singleLine = true,
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(8.dp)
            )
        },
        confirmButton = {
            Button(
                onClick = {
                    onSaveConfig(boostCouponHelpDataStr)
                    onDismiss()
                },
                enabled = boostCouponHelpDataStr.isNotEmpty()
            ) {
                Text("保存")
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        }
    )
}

/**
 * 订单查询对话框
 */
@Composable
fun OrderSearchDialog(
    onDismiss: () -> Unit,
    cardRecordOrderId: String,
    onCardRecordOrderIdChange: (String) -> Unit,
    onGetCardRecordOrderList: () -> Unit,
    onGetAfterSaleRecordOrderList: () -> Unit,
    onGetInvoiceRecordOrderList: () -> Unit,
    onShowDeletedOrderRecords: () -> Unit = {},
    onConfirm: (String) -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("订单查询") },
        containerColor = dialogContainerColor(),
        text = {
            Column {
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row {
                        TokenActionButton(
                            imageVector = Icons.Filled.DateRange,
                            text = "消费记录",
                            onClick = onGetCardRecordOrderList
                        )
                        TokenActionButton(
                            imageVector = Icons.Filled.Notifications,
                            text = "售后记录",
                            onClick = onGetAfterSaleRecordOrderList
                        )
                        TokenActionButton(
                            imageVector = Icons.Filled.MailOutline,
                            text = "发票记录",
                            onClick = onGetInvoiceRecordOrderList
                        )
                        TokenActionButton(
                            imageVector = Icons.Filled.ShoppingCart,
                            text = "数据库记录",
                            onClick = onShowDeletedOrderRecords
                        )
                    }
                }

                OutlinedTextField(
                    value = cardRecordOrderId,
                    onValueChange = onCardRecordOrderIdChange,
                    label = { Text("订单号") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )
            }
        },
        confirmButton = {
            Button(
                onClick = { onConfirm(cardRecordOrderId) },
                enabled = cardRecordOrderId.isNotEmpty()
            ) {
                Text("查询")
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        }
    )
}

/**
 * 卡记录订单列表对话框
 */
@Composable
fun CardRecordOrderListDialog(
    onDismiss: () -> Unit,
    cardRecordOrderList: List<String>,
    onOrderSelect: (String) -> Unit,
    hasNextPage: Boolean = false,
    onLoadMore: () -> Unit = {}
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("请选择消费记录") },
        containerColor = dialogContainerColor(),
        text = {
            Column(
                modifier = Modifier
                    .heightIn(max = 300.dp)
            ) {
                LazyColumn(
                    modifier = Modifier.fillMaxWidth(),
                    content = {
                        items(cardRecordOrderList.size) { index ->
                            val orderInfo = cardRecordOrderList[index]
                            val orderInfoArr = orderInfo.split(",")
                            val orderId = orderInfoArr[0]
                            val orderInfoStr =
                                if (orderInfoArr.size >= 4) {
                                    "${orderInfoArr[1]}/${orderInfoArr[2]}/${orderInfoArr[3]}"
                                } else {
                                    orderId
                                }

                            // 如果到达列表末尾且有下一页，则加载更多，最多加载10条左右
                            if (index == cardRecordOrderList.size - 1 && hasNextPage && cardRecordOrderList.size < 10) {
                                LaunchedEffect(key1 = cardRecordOrderList.size) {
                                    onLoadMore()
                                }
                            }

                            Card(
                                colors = cardThemeOverlay(),
                                modifier = Modifier.padding(top = 2.dp, bottom = 2.dp)
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .clickable { onOrderSelect(orderId) }
                                ) {
                                    Column(
                                        verticalArrangement = Arrangement.Center,
                                        modifier = Modifier
                                            .padding(
                                                start = 8.dp,
                                                top = 12.dp,
                                                end = 6.dp,
                                                bottom = 12.dp
                                            )
                                    ) {
                                        Text(
                                            text = orderId,
                                            fontSize = 16.sp,
                                            lineHeight = 16.sp
                                        )
                                        Text(
                                            text = orderInfoStr,
                                            modifier = Modifier.horizontalScroll(rememberScrollState()),
                                            fontSize = 10.sp,
                                            lineHeight = 10.sp
                                        )
                                    }
                                }
                            }
                        }

                        if (cardRecordOrderList.size > 5) {
                            item {
                                Text(
                                    text = if (!hasNextPage) "到底啦！！！" else "更多请在APP内查看",
                                    fontSize = 10.sp,
                                    lineHeight = 12.sp,
                                    color = Color.Gray,
                                    textAlign = TextAlign.Center,
                                    modifier = Modifier.fillMaxWidth()
                                )
                            }
                        }
                    }
                )
            }
        },
        confirmButton = { }
    )
}

@Composable
fun CancelSendCardDialog(
    onDismiss: () -> Unit,
    onConfirm: (CardItem) -> Unit,
    card: CardItem?
) {
    if (card == null) return

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = dialogContainerColor(),
        title = { Text("确定要取消赠送该永辉卡吗？") },
        text = {
            Text("\n卡号：${card.cardNo}\n面额：${card.cardAmount}")
        },
        confirmButton = {
            Button(
                onClick = {
                    onConfirm(card)
                    onDismiss()
                }
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        }
    )
}

@Composable
fun SendCardDialog(
    onDismiss: () -> Unit,
    onConfirm: (CardItem) -> Unit,
    card: CardItem?
) {
    if (card == null) return

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = dialogContainerColor(),
        title = { Text("确定赠送该永辉卡吗") },
        text = {
            Text(
                "卡号：${card.cardNo}\n面额：${card.cardAmount}\n余额：${
                    if (card.cardBalance.contains(".")) card.cardBalance else "${card.cardBalance}.0"
                }"
            )
        },
        confirmButton = {
            Button(
                onClick = {
                    onConfirm(card)
                    onDismiss()
                }
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        }
    )
}

@SuppressLint("DefaultLocale")
@Composable
fun CouponDetailDialog(
    onDismiss: () -> Unit,
    coupon: CouponListCoupon?
) {
    if (coupon == null) return

    val context = LocalContext.current

    val displayStr = buildString {
        // 优惠券名称
        if (coupon.name.isNotBlank()) {
            append("名称：${coupon.name}\n")
        }

        // 优惠券类型
        when (coupon.couponType) {
            "all_common" -> append("类型：全品类")
            "limit_product" -> append("类型：指定品类")
            else -> append("类型：未知品类")
        }

        // 优惠券金额
        if (coupon.amount > 0) {
            val formattedAmount = String.format("%.2f", coupon.amount / 100.0)
            append("\n面额：¥$formattedAmount")
        }

        // 使用条件
        if (coupon.conditiondesc.isNotBlank()) {
            append("\n使用条件：${coupon.conditiondesc}")
        } else if (coupon.orderminamount > 0) {
            val minAmount = String.format("%.2f", coupon.orderminamount / 100.0)
            append("\n使用条件：满¥${minAmount}可用")
        }

        if (coupon.freightfreedesc.isNotBlank()) {
            append(" ${coupon.freightfreedesc}")
        }

        // 有效期
        val validityPeriod = when {
            coupon.startDate.isNotBlank() && coupon.endDate.isNotBlank() ->
                "${coupon.startDate} 至 ${coupon.endDate}"

            coupon.date.isNotBlank() -> coupon.date
            coupon.expireddate.isNotBlank() -> "截止至 ${coupon.expireddate}"
            else -> null
        }
        validityPeriod?.let { append("\n有效期：$it") }

        // 使用范围
        if (coupon.scope.isNotBlank()) {
            append("\n使用平台：${coupon.scope}")
        }
        if (coupon.shoprealm.isNotBlank()) {
            append("\n使用店铺：${coupon.shoprealm}")
        }
        if (coupon.realm.isNotBlank()) {
            append("\n使用范围：${coupon.realm}")
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = dialogContainerColor(),
        title = { Text("优惠券信息") },
        text = {
            Text(displayStr)
        },
        confirmButton = {
            // 优化按钮逻辑，减少重复代码
            if (coupon.actionurl.isNotBlank()) {
                Button(
                    onClick = {
                        val uri = coupon.actionurl.toUri()
                        val intent = Intent(Intent.ACTION_VIEW, uri)
                        startActivity(context, intent, null)
                    }
                ) {
                    Text(coupon.useprompt.ifBlank { "查看使用范围" })
                }
            } else {
                val formatActionUrl =
                    "myyh://yhlife.com/show/native?name=couponactivity&sellerid=${coupon.sellers.firstOrNull() ?: "7"}&code=${coupon.promotioncode}&selectcurrentseller=1"
                Button(
                    onClick = {
                        val uri = formatActionUrl.toUri()
                        val intent = Intent(Intent.ACTION_VIEW, uri)
                        startActivity(context, intent, null)
                    }
                ) {
                    Text("查看使用范围")
                }
            }
        }
    )
}

@SuppressLint("DefaultLocale")
@Composable
fun BoostCouponDetailDialog(
    onDismiss: () -> Unit,
    boostCoupon: BoostCouponRecord?
) {
    if (boostCoupon == null) return

    val context = LocalContext.current
    val prizeGameDTO = boostCoupon.prizeGameDTO
    val boostCouponVO = boostCoupon.boostCouponVO

    if (prizeGameDTO == null || boostCouponVO == null) {
        Toast.makeText(context, "助力券数据不完整", Toast.LENGTH_SHORT).show()
        onDismiss()
        return
    }

    val displayStr = buildString {
        // 助力券基本信息
        append("名称：${boostCouponVO.name}\n")
        append("面额：${boostCouponVO.amount}${boostCouponVO.unit}")

        // 助力券类型
        val couponType = if (boostCouponVO.isRedEnvelope) "红包" else "优惠券"
        append(" $couponType")

        // 助力券描述
        if (boostCouponVO.couponDescription.isNotBlank() && boostCouponVO.couponDescription != boostCouponVO.name) {
            append(" ${boostCouponVO.couponDescription}\n")
        } else {
            append("\n")
        }

        // 活动规则
        if (prizeGameDTO.activityTitle.isNotBlank()) {
            append("活动规则：\n${prizeGameDTO.activityTitle}；\n")
            append(boostCouponVO.detailList.joinToString("；\n"))
            append("；")
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = dialogContainerColor(),
        title = { Text("助力券信息") },
        text = {
            Text(displayStr)
        },
        confirmButton = {
            Button(
                onClick = {
                    val formatActionUrl =
                        "myyh://yhlife.com/show/native?name=couponactivity&sellerid=7&code=${boostCouponVO.promotionCode}&selectcurrentseller=1"
                    val uri = formatActionUrl.toUri()
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    startActivity(context, intent, null)
                }
            ) {
                Text("查看使用范围")
            }
        }
    )
}

/**
 * 拼手气对话框
 */
@Composable
fun BindInvitationCodeDialog(
    onDismiss: () -> Unit,
    bindInvitationCodeStr: String,
    onBindInvitationCode: (String) -> Unit,
    onSaveInvitationCode: (String) -> Unit
) {
    val context = LocalContext.current
    var tempBindInvitationCodeStr by remember { mutableStateOf(bindInvitationCodeStr) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("邀请有礼参与") },
        containerColor = dialogContainerColor(),
        text = {
            Column {
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row {
                        TokenActionButton(
                            icon = R.drawable.baseline_save_24,
                            text = "保存配置",
                            onClick = {
                                if (tempBindInvitationCodeStr.isEmpty()) {
                                    Toast.makeText(context, "邀请参数为空", Toast.LENGTH_SHORT)
                                        .show()
                                    return@TokenActionButton
                                }
                                onSaveInvitationCode(tempBindInvitationCodeStr)
                            }
                        )
                    }
                }

                OutlinedTextField(
                    value = tempBindInvitationCodeStr,
                    onValueChange = {
                        tempBindInvitationCodeStr = it
                    },
                    label = { Text("邀请码参数") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    onBindInvitationCode(tempBindInvitationCodeStr)
                },
                enabled = tempBindInvitationCodeStr.isNotEmpty()
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        }
    )
}

@Composable
fun JoinPointTeamDialog(
    onDismiss: () -> Unit,
    pointTeamCode: String,
    onPointTeamCodeChange: (String) -> Unit,
    onConfirm: (String) -> Unit
) {
    val context = LocalContext.current
    var tempPointTeamCode by remember { mutableStateOf(pointTeamCode) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("积分组队参与") },
        containerColor = dialogContainerColor(),
        text = {
            Column {
                OutlinedTextField(
                    value = tempPointTeamCode,
                    onValueChange = {
                        // 解析teamCode参数
                        val input = it
                        val teamCode = Regex("teamCode=(\\d+)").find(input)?.groups?.get(1)?.value
                        tempPointTeamCode = teamCode ?: input
                        onPointTeamCodeChange(tempPointTeamCode)
                    },
                    label = { Text("积分组队参数") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    onConfirm(tempPointTeamCode)
                },
                enabled = tempPointTeamCode.isNotEmpty()
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        }
    )
}
