package dev.pigmomo.yhkit2025.ui.dialog

import android.util.Log
import android.widget.Toast
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.SelectableDates
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.api.model.order.DeliveryDate
import dev.pigmomo.yhkit2025.api.model.order.OrderDeliveryInfoData
import dev.pigmomo.yhkit2025.api.model.user.AddressItem
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.utils.DateUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.Instant
import java.time.ZoneId
import java.util.regex.Pattern
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.ui.draw.clip

/**
 * 处理日期更新逻辑，根据选择的日期更新dateList中的日期信息
 *
 * @param dateList 需要更新的日期列表
 * @param replaceDateStr 新选择的日期字符串 (格式: yyyy-MM-dd)
 * @return 更新后的日期列表
 */
private fun updateDateList(
    dateList: List<DeliveryDate>,
    replaceDateStr: String
): List<DeliveryDate> {
    return if (dateList.size == 1) {
        // 当 dateList 大小为 1 时，修改全部
        dateList.map { dateItem ->
            // 提取原始日期部分 (例如: 2025-06-12 00:00:00)
            val needReplaceData = dateItem.date.split(" ")[0]
            val date = dateItem.date.replace(needReplaceData, replaceDateStr)
            dateItem.date = date
            dateItem.dateshow = replaceDateStr

            // 更新时间段列表中的时间
            dateItem.periodlist.forEach { period ->
                period.starttime = period.starttime.replace(needReplaceData, replaceDateStr)
                period.endtime = period.endtime.replace(needReplaceData, replaceDateStr)
            }

            dateItem
        }
    } else {
        // 当 dateList 大小大于等于 2 时，只修改索引 1 及之后的条目
        dateList.mapIndexed { index, dateItem ->
            if (index >= 1) {
                // 解析基准日期
                val baseDate = LocalDate.parse(replaceDateStr, DateTimeFormatter.ISO_LOCAL_DATE)
                // 第二条（索引为1）显示选择的日期，后面的条目再递增
                val daysToAdd = if (index == 1) 0 else index - 1
                val adjustedDate = baseDate.plusDays(daysToAdd.toLong())
                val adjustedDateStr = adjustedDate.format(DateTimeFormatter.ISO_LOCAL_DATE)

                // 更新日期
                val needReplaceData = dateItem.date.split(" ")[0]
                dateItem.date = dateItem.date.replace(needReplaceData, adjustedDateStr)
                dateItem.dateshow = adjustedDateStr

                // 更新时间段列表中的时间
                dateItem.periodlist.forEach { period ->
                    period.starttime = period.starttime.replace(needReplaceData, adjustedDateStr)
                    period.endtime = period.endtime.replace(needReplaceData, adjustedDateStr)
                }
            }
            dateItem
        }
    }
}

/**
 * 订单配送信息对话框
 * 用于显示和修改订单的配送信息，包括收货地址、预约时间和备注
 *
 * @param onDismiss 关闭对话框回调
 * @param service 请求服务
 * @param oderDeliveryInfoData 订单配送信息
 * @param orderId 订单ID
 * @param oderDeliveryAddressList 可选的配送地址列表
 * @param returnParam 返回参数回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OderDeliveryChangeDialog(
    onDismiss: () -> Unit,
    service: RequestService,
    oderDeliveryInfoData: OrderDeliveryInfoData,
    orderId: String,
    oderDeliveryAddressList: List<AddressItem> = emptyList(),
    returnParam: (returnParamJSONObject: JSONObject) -> Unit
) {
    val context = LocalContext.current

    // 状态声明
    var name by remember { mutableStateOf(oderDeliveryInfoData.name) }
    var phone by remember { mutableStateOf(oderDeliveryInfoData.phone) }
    var remark by remember { mutableStateOf(oderDeliveryInfoData.remark) }
    var expectTime by remember { mutableStateOf(oderDeliveryInfoData.expectTime) }
    var dateList = oderDeliveryInfoData.dateList
    val receiveInfo = oderDeliveryInfoData.receiveInfo
    val address = receiveInfo?.address
    val city = address?.city
    val area = address?.area
    val detail = address?.detail
    val location = receiveInfo?.location
    val origLat = location?.lat
    val origLng = location?.lng

    // 记录原始值，用于比较是否有变更
    val originalName = oderDeliveryInfoData.name
    val originalPhone = oderDeliveryInfoData.phone
    val originalRemark = oderDeliveryInfoData.remark
    val originalExpectTime = oderDeliveryInfoData.expectTime
    val originalReceiverArea = area
    val originalReceiverDetail = detail
    val originalReceiverCity = city

    var startdate by remember { mutableStateOf("") }
    var enddate by remember { mutableStateOf("") }

    var receiverArea by remember { mutableStateOf(area) }
    var receiverDetail by remember { mutableStateOf(detail) }
    var receiverCity by remember { mutableStateOf(city) }

    // 用于记录当前选择的地址位置信息
    var currentLat by remember { mutableStateOf(origLat) }
    var currentLng by remember { mutableStateOf(origLng) }

    var expandableAddress by remember { mutableStateOf(false) }
    var expandableDate by remember { mutableStateOf(false) }

    var displayStr by remember { mutableStateOf("$name $phone $area$detail") }

    // 控制 DatePicker 的显示状态
    var showDatePicker by remember { mutableStateOf(false) }
    // DatePicker 的状态管理器
    val datePickerState = rememberDatePickerState(selectableDates = object : SelectableDates {
        // 禁用过去的日期
        override fun isSelectableDate(utcTimeMillis: Long): Boolean {
            val today = LocalDate.now()
            val selectedDate =
                Instant.ofEpochMilli(utcTimeMillis).atZone(ZoneId.systemDefault()).toLocalDate()
            return !selectedDate.isBefore(today)
        }
    })

    // 计算是否有信息被更改
    val isInfoChanged by remember {
        derivedStateOf {
            name != originalName ||
                    phone != originalPhone ||
                    remark != originalRemark ||
                    expectTime != originalExpectTime ||
                    receiverArea != originalReceiverArea ||
                    receiverDetail != originalReceiverDetail ||
                    receiverCity != originalReceiverCity ||
                    startdate.isNotEmpty() ||  // 如果选择了预约时间，则认为有变更
                    enddate.isNotEmpty()
        }
    }

    // DatePicker 对话框
    if (showDatePicker) {
        DatePickerDialog(
            onDismissRequest = { showDatePicker = false },
            colors = DatePickerDefaults.colors(
                containerColor = Color(0xFFF0F2FF),
            ),
            confirmButton = {
                Button(
                    onClick = {
                        datePickerState.selectedDateMillis?.let {
                            val selectedDate =
                                Instant.ofEpochMilli(it).atZone(ZoneId.systemDefault())
                                    .toLocalDate()
                            val replaceDateStr =
                                selectedDate.format(DateTimeFormatter.ISO_LOCAL_DATE)
                            Log.d("customDateString", replaceDateStr)
                            dateList = updateDateList(dateList, replaceDateStr)
                        }
                        showDatePicker = false
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                Button(onClick = { showDatePicker = false }) {
                    Text("取消")
                }
            }
        ) {
            DatePicker(
                state = datePickerState, colors = DatePickerDefaults.colors(
                    containerColor = Color(0xFFF0F2FF),
                )
            )
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = dialogContainerColor(),
        title = {
            Column {
                Text("修改配送信息")
            }
        },
        text = {
            Column {
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(top = 2.dp, bottom = 2.dp)
                        .fillMaxWidth()
                ) {
                    Row {
                        TokenActionButton(
                            imageVector = Icons.Filled.DateRange,
                            text = "自定义日期",
                            onClick = {
                                showDatePicker = true
                            }
                        )
                    }
                }

                Card(
                    colors = CardDefaults.cardColors(
                        Color(0xCBDEE6F7),
                    ),
                    modifier = Modifier
                        .padding(top = 2.dp, bottom = 2.dp)
                        .fillMaxWidth()
                ) {
                    Column {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                "收货信息",
                                modifier = Modifier
                                    .padding(start = 10.dp, top = 10.dp)
                            )
                            Column {
                                Text(
                                    displayStr,
                                    modifier = Modifier
                                        .padding(start = 10.dp, top = 10.dp, end = 10.dp)
                                        .clip(RoundedCornerShape(4.dp))
                                        .clickable {
                                            expandableAddress = true
                                        }
                                        .horizontalScroll(rememberScrollState())
                                )

                                DropdownMenu(
                                    onDismissRequest = { expandableAddress = false },
                                    expanded = expandableAddress,
                                ) {
                                    for (addressItem in oderDeliveryAddressList) {
                                        val currentData = addressItem.address
                                        val deliverydesc = addressItem.deliverydesc.toString()
                                        val isdefault =
                                            if (addressItem.isdefault == 1) "默认地址" else ""
                                        val addressItemName = addressItem.name
                                        val currentPhone = addressItem.phone
                                        val currentScope = addressItem.scope

                                        // 过滤不可用地址
                                        if (currentScope == 3) {
                                            continue
                                        }

                                        val currentAddressid = addressItem.id
                                        val currentAddress =
                                            "${currentData.area}${currentData.detail} $addressItemName,$currentPhone ${currentData.provname}${currentData.city} $currentAddressid $deliverydesc $isdefault"
                                        DropdownMenuItem(
                                            text = {
                                                Text(
                                                    currentAddress,
                                                    modifier = Modifier
                                                        .horizontalScroll(rememberScrollState())
                                                )
                                            },
                                            onClick = {
                                                name = addressItemName
                                                phone = currentPhone
                                                receiverArea = currentData.area.toString()
                                                receiverDetail = currentData.detail.toString()
                                                receiverCity = currentData.city.toString()
                                                val addressLocation = addressItem.location
                                                currentLat = addressLocation?.lat
                                                currentLng = addressLocation?.lng

                                                displayStr =
                                                    "$name $phone $receiverArea$receiverDetail"
                                                expandableAddress = false
                                            }
                                        )
                                    }
                                }
                            }
                        }
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                "预约时间",
                                modifier = Modifier
                                    .padding(start = 10.dp, top = 10.dp)
                            )
                            Column {
                                Text(
                                    expectTime,
                                    modifier = Modifier
                                        .padding(start = 10.dp, top = 10.dp, end = 10.dp)
                                        .clip(RoundedCornerShape(4.dp))
                                        .clickable {
                                            expandableDate = true
                                        }
                                )
                                DropdownMenu(
                                    onDismissRequest = { expandableDate = false },
                                    expanded = expandableDate,
                                ) {
                                    for (i in 0 until dateList.size) {
                                        val curDate = dateList[i]
                                        val date = curDate.date
                                        val dateshow = curDate.dateshow
                                        val periodlist = curDate.periodlist

                                        var expandableTime by remember { mutableStateOf(false) }

                                        DropdownMenuItem(
                                            text = { Text(dateshow) },
                                            onClick = {
                                                expandableTime = true
                                            }
                                        )

                                        DropdownMenu(
                                            onDismissRequest = { expandableTime = false },
                                            expanded = expandableTime,
                                        ) {
                                            for (periodlistIndex in 0 until periodlist.size) {
                                                val period = periodlist[periodlistIndex]
                                                val starttime = period.starttime
                                                val endtime = period.endtime
                                                val starttimeshow = period.starttimeshow
                                                val endtimeshow = period.endtimeshow
                                                val time = "$starttimeshow-$endtimeshow"

                                                DropdownMenuItem(
                                                    text = { Text(time) },
                                                    onClick = {
                                                        expectTime = "$dateshow $time"
                                                        startdate = starttime
                                                        enddate = endtime

                                                        expandableDate = false
                                                        expandableTime = false
                                                    }
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        Spacer(modifier = Modifier.height(10.dp))
                    }
                }

                OutlinedTextField(
                    value = remark,
                    onValueChange = {
                        if (it.length > 30) {
                            Toast.makeText(
                                context,
                                "备注不能超过30字",
                                Toast.LENGTH_SHORT
                            ).show()
                            return@OutlinedTextField
                        }
                        remark = it
                    },
                    label = { Text("订单备注") },
                    singleLine = true,
                    modifier = Modifier
                        .fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    // 在确认按钮点击时构建参数并回调
                    CoroutineScope(Dispatchers.IO).launch {
                        // 获取位置参数
                        val lat = currentLat?.toString() ?: ""
                        val lng = currentLng?.toString() ?: ""

                        val sign = service.getXyhBizParams(
                            lat = lat,
                            lng = lng
                        )

                        if (sign.isSuccess) {
                            val gvo = extractParamValue(sign.data, "gvo=(.*?)&")
                            val gib = extractParamValue(sign.data, "gib=(.*?)&")

                            if (gvo.isEmpty()) {
                                withContext(Dispatchers.Main) {
                                    Toast.makeText(
                                        context,
                                        "定位信息生成错误",
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                            } else {
                                // 按照新格式构建JSON对象
                                val returnParamJSONObject = JSONObject().apply {
                                    // 基本参数
                                    put("orderId", orderId)
                                    put("name", name)
                                    put("phone", phone)
                                    put("remark", remark)
                                    put("startdate", startdate)
                                    put("enddate", enddate)

                                    // 创建嵌套的receiveAddress对象
                                    val receiveAddressObj = JSONObject().apply {
                                        put("receiverArea", receiverArea)
                                        put("receiverDetail", receiverDetail)
                                        put("receiverCity", receiverCity)
                                        put("gvo", gvo)
                                        put("gib", gib)
                                    }

                                    // 添加嵌套对象
                                    put("receiveAddress", receiveAddressObj)
                                }

                                // 成功构建参数后回调
                                withContext(Dispatchers.Main) {
                                    returnParam(returnParamJSONObject)
                                    onDismiss()
                                }
                            }
                        } else {
                            withContext(Dispatchers.Main) {
                                Toast.makeText(
                                    context,
                                    "获取位置参数错误",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        }
                    }
                },
                enabled = isInfoChanged // 根据是否有信息变更来启用或禁用按钮
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 从字符串中提取正则表达式匹配的值
 *
 * @param input 输入字符串
 * @param regex 正则表达式
 * @return 匹配的值，如果没有匹配则返回空字符串
 */
private fun extractParamValue(input: String?, regex: String): String {
    if (input == null) {
        return ""
    }
    val pattern = Pattern.compile(regex)
    val matcher = pattern.matcher(input)
    return if (matcher.find()) {
        matcher.group(1) ?: ""
    } else {
        ""
    }
} 