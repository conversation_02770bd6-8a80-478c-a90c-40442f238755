package dev.pigmomo.yhkit2025.ui.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor

/**
 * 日志筛选的下拉菜单组件
 *
 * @param selectedFilter 当前选中的筛选器
 * @param onFilterSelected 筛选器选中时的回调
 * @param filterOptions 筛选器选项，包含名称和图标
 * @param modifier Modifier
 */
@Composable
fun LogFilterDropdown(
    selectedFilter: String,
    onFilterSelected: (String) -> Unit,
    filterOptions: List<Pair<String, ImageVector>>,
    modifier: Modifier = Modifier
) {
    var showFilterDropdown by remember { mutableStateOf(false) }

    Box(modifier = modifier) {
        Surface(
            onClick = { showFilterDropdown = true },
            shape = RoundedCornerShape(6.dp),
            color = when (selectedFilter) {
                "WARNING" -> MaterialTheme.colorScheme.tertiary.copy(alpha = 0.3f)
                "ERROR" -> MaterialTheme.colorScheme.error.copy(alpha = 0.3f)
                else -> CardContainerColor
            }
        ) {
            Row(
                modifier = Modifier.padding(
                    horizontal = 12.dp,
                    vertical = 6.dp
                ),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                // 筛选器图标
                val icon = filterOptions.find { it.first == selectedFilter }?.second
                if (icon != null) {
                    Icon(
                        icon,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                }

                Text(
                    text = when (selectedFilter) {
                        "全部" -> "全部"
                        "INFO" -> "信息"
                        "WARNING" -> "警告"
                        "ERROR" -> "错误"
                        "按手机号" -> "手机号"
                        else -> selectedFilter
                    },
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Medium
                )
            }
        }

        DropdownMenu(
            expanded = showFilterDropdown,
            onDismissRequest = { showFilterDropdown = false }
        ) {
            filterOptions.forEach { (filter, icon) ->
                DropdownMenuItem(
                    text = {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                icon,
                                contentDescription = null,
                                modifier = Modifier.size(18.dp),
                                tint = when (filter) {
                                    "INFO" -> MaterialTheme.colorScheme.primary
                                    "WARNING" -> MaterialTheme.colorScheme.tertiary
                                    "ERROR" -> MaterialTheme.colorScheme.error
                                    else -> MaterialTheme.colorScheme.onSurface
                                }
                            )
                            Text(
                                text = when (filter) {
                                    "INFO" -> "信息日志"
                                    "WARNING" -> "警告日志"
                                    "ERROR" -> "错误日志"
                                    "按手机号" -> "按手机号"
                                    else -> filter
                                },
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    },
                    onClick = {
                        onFilterSelected(filter)
                        showFilterDropdown = false
                    }
                )
            }
        }
    }
} 