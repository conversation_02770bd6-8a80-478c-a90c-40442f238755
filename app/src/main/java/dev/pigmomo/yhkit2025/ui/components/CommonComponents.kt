package dev.pigmomo.yhkit2025.ui.components

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.sp

/**
 * 通用的操作按钮组件，可用于对话框中的操作按钮
 * @param icon 按钮图标资源ID
 * @param text 按钮文本
 * @param onClick 点击事件处理
 * @param tint 图标颜色，默认为黑色
 */
@Composable
fun TokenActionButton(
    icon: Int,
    text: String,
    onClick: () -> Unit,
    tint: Color = Color(0xFF48454E)
) {
    TextButton(
        onClick = onClick,
        colors = ButtonDefaults.textButtonColors(contentColor = tint)
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Icon(
                painterResource(id = icon),
                contentDescription = null,
                tint = tint
            )
            Text(
                text = text,
                fontSize = 9.sp
            )
        }
    }
}

/**
 * 使用ImageVector的TokenActionButton变体
 * @param imageVector 按钮图标向量
 * @param text 按钮文本
 * @param onClick 点击事件处理
 * @param tint 图标颜色，默认为黑色
 */
@Composable
fun TokenActionButton(
    imageVector: ImageVector,
    text: String,
    onClick: () -> Unit,
    tint: Color = Color(0xFF48454E)
) {
    TextButton(
        onClick = onClick,
        colors = ButtonDefaults.textButtonColors(contentColor = tint)
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Icon(
                imageVector = imageVector,
                contentDescription = null,
                tint = tint
            )
            Text(
                text = text,
                fontSize = 9.sp
            )
        }
    }
} 