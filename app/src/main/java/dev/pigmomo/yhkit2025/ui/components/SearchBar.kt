package dev.pigmomo.yhkit2025.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor
import kotlinx.coroutines.launch

@Composable
fun SearchBar(
    searchKeyword: String,
    onValueChange: (String) -> Unit,
    orderTokens: List<OrderTokenEntity>,
    lazyListState: LazyListState,
    onFilteredTokensChanged: (List<OrderTokenEntity>) -> Unit,
    onCurrentIndexChanged: (Int) -> Unit,
    progressRecords: Map<String, String> = emptyMap()
) {
    var searchLabel by remember { mutableStateOf("搜索关键词") }
    var filteredTokens by remember {
        mutableStateOf<List<OrderTokenEntity>>(emptyList())
    }
    var filteredClickIndex by remember { mutableStateOf(0) }
    val coroutineScope = rememberCoroutineScope()

    // 当搜索关键词变化时，更新过滤结果
    LaunchedEffect(searchKeyword, orderTokens, progressRecords) {
        filteredTokens = if (searchKeyword.isEmpty()) {
            emptyList()
        } else {
            orderTokens.filter { token ->
                // 获取与此令牌关联的进度记录
                val progressRecord = progressRecords[token.uid.toString()] ?: ""
                
                // 在电话号码、备注、UID和进度记录中搜索关键词
                token.phoneNumber.contains(searchKeyword) ||
                        token.extraNote.contains(searchKeyword) ||
                        token.uid.toString().contains(searchKeyword) ||
                        progressRecord.contains(searchKeyword)
            }
        }

        // 只有当搜索关键词不为空且有过滤结果时才保持索引，否则重置索引
        if (searchKeyword.isEmpty() || filteredTokens.isEmpty()) {
            filteredClickIndex = 0
        } else if (filteredClickIndex >= filteredTokens.size) {
            filteredClickIndex = 0
        }

        // 通知外部过滤结果变化
        onFilteredTokensChanged(filteredTokens)
        onCurrentIndexChanged(if (filteredTokens.isEmpty()) -1 else filteredClickIndex)
    }

    // 当索引变化时通知外部
    LaunchedEffect(filteredClickIndex) {
        if (filteredTokens.isNotEmpty()) {
            onCurrentIndexChanged(filteredClickIndex)
        }
    }

    // 当搜索关键词或过滤结果变化时更新标签
    LaunchedEffect(searchKeyword, filteredTokens, filteredClickIndex) {
        if (searchKeyword.isEmpty()) {
            searchLabel = "搜索关键词"
        } else if (filteredTokens.isEmpty()) {
            searchLabel = "搜索结果为空"
        } else {
            searchLabel = "已找到 ${filteredClickIndex + 1}/${filteredTokens.size}"

            val targetIndex = orderTokens.indexOf(filteredTokens[filteredClickIndex])
            if (targetIndex >= 0) {
                lazyListState.scrollToItem(targetIndex)
            }
        }
    }

    Row(modifier = Modifier.background(Color.Transparent)) {
        OutlinedTextField(
            value = searchKeyword,
            onValueChange = { newValue ->
                onValueChange(newValue)
                // 不在这里重置filteredClickIndex，而是在LaunchedEffect中处理
            },
            label = { Text(searchLabel) },
            singleLine = true,
            modifier = Modifier
                .weight(4f)
                .fillMaxWidth()
                .padding(start = 12.dp, end = 8.dp, top = 4.dp, bottom = 4.dp),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = MaterialTheme.colorScheme.primary,
                unfocusedBorderColor = Color.Gray,
            ),
            shape = RoundedCornerShape(8.dp)
        )

        Button(
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.primary,
                disabledContainerColor = CardContainerColor
            ),
            shape = RoundedCornerShape(12.dp),
            onClick = {
                if (filteredTokens.isNotEmpty()) {
                    // 使用协程统一处理状态更新和滚动，避免分离导致的闪烁
                    coroutineScope.launch {
                        val newIndex = (filteredClickIndex + 1) % filteredTokens.size
                        val targetIndex = orderTokens.indexOf(filteredTokens[newIndex])

                        // 先滚动到目标位置，再更新状态
                        if (targetIndex >= 0) {
                            lazyListState.scrollToItem(targetIndex)
                            // 延迟一帧后更新索引，确保滚动完成后再高亮
                            filteredClickIndex = newIndex
                        }
                    }
                }
            },
            modifier = Modifier
                .height(64.dp)
                .padding(top = 8.dp, end = 12.dp)
                .align(Alignment.CenterVertically),
            enabled = filteredTokens.isNotEmpty()
        ) {
            Icon(
                imageVector = Icons.Default.KeyboardArrowDown,
                contentDescription = "下一个结果"
            )
        }
    }
}