package dev.pigmomo.yhkit2025.ui.components

import android.util.Log
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import coil.compose.AsyncImage
import coil.request.ImageRequest
import dev.pigmomo.yhkit2025.api.model.cart.CartModelTypes
import dev.pigmomo.yhkit2025.api.model.cart.CartModelWrapper
import androidx.compose.ui.unit.sp
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.Icon
import dev.pigmomo.yhkit2025.api.model.cart.Product
import dev.pigmomo.yhkit2025.ui.screens.removeHtmlTags

/**
 * 购物车模型项组件
 * 根据modelType显示不同类型的购物车项目
 *
 * @param cartModel 购物车模型包装类
 * @param onItemClick 项目点击回调
 * @param onAddItem 增加商品数量回调
 * @param onReduceItem 减少商品数量回调
 * @param onDeleteItem 删除商品回调
 * @param onItemLongClick 项目长按回调，用于显示删除对话框
 */
@Composable
fun CartModelItem(
    cartModel: CartModelWrapper,
    onItemClick: (Product) -> Unit = {},
    onAddItem: (Product) -> Unit = {},
    onReduceItem: (Product) -> Unit = {},
    onDeleteItem: (Product) -> Unit = {},
    onItemLongClick: (Product) -> Unit = onDeleteItem
) {
    // 根据modelType显示不同类型的组件
    when (cartModel.modelType) {
        CartModelTypes.SHOP_NAME -> {
            // 店铺名称项
            //ShopNameItem(cartModel)
        }

        CartModelTypes.DELIVERY_INFO -> {
            // 配送信息项
            //DeliveryInfoItem(cartModel)
        }

        CartModelTypes.PROMOTION_INFO -> {
            // 促销信息项
            //PromotionInfoItem(cartModel)
        }

        CartModelTypes.PRODUCT_ITEM -> {
            // 商品项
            ProductItem(
                cartModel = cartModel,
                onItemClick = onItemClick,
                onAddItem = onAddItem,
                onReduceItem = onReduceItem,
                onDeleteItem = onDeleteItem,
                onItemLongClick = onItemLongClick
            )
        }
    }
}

/**
 * 店铺名称项组件
 */
@Composable
fun ShopNameItem(cartModel: CartModelWrapper) {
    // 获取店铺名称
    val shopName = cartModel.getShopName()
    if (shopName == null) {
        Log.e("CartModelItem", "店铺名称数据解析错误")
        return
    }

    Text(
        text = shopName,
        fontSize = 14.sp,
        fontWeight = FontWeight.Bold,
        modifier = Modifier
            .padding(start = 6.dp, bottom = 6.dp)
            .horizontalScroll(rememberScrollState())
    )
}


/**
 * 配送信息项组件
 */
@Composable
fun DeliveryInfoItem(cartModel: CartModelWrapper) {
    // 获取配送信息数据
    val deliveryInfo = cartModel.getDelivery()
    if (deliveryInfo == null) {
        Log.e("CartModelItem", "配送信息数据解析错误")
        return
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp, vertical = 6.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 配送方式图标
        if (deliveryInfo.image.isNotEmpty()) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(deliveryInfo.image)
                    .crossfade(true)
                    .build(),
                contentDescription = deliveryInfo.name,
                modifier = Modifier.size(20.dp),
                contentScale = ContentScale.Fit
            )
        }

        // 配送方式名称和描述
        if (deliveryInfo.name.isNotEmpty()) {
            Text(
                text = deliveryInfo.name + " " + deliveryInfo.deliveryDesc,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .padding(start = 8.dp)
                    .horizontalScroll(rememberScrollState())
            )
        }
    }
}

/**
 * 促销信息项组件
 */
@Composable
fun PromotionInfoItem(cartModel: CartModelWrapper) {
    Text("促销数据未解析")
    return
}

/**
 * 商品项组件
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ProductItem(
    cartModel: CartModelWrapper,
    onItemClick: (Product) -> Unit,
    onAddItem: (Product) -> Unit,
    onReduceItem: (Product) -> Unit,
    onDeleteItem: (Product) -> Unit,
    onItemLongClick: (Product) -> Unit
) {
    // 使用getProductItem方法获取商品信息
    val product = cartModel.getProduct()
    if (product == null) {
        Log.e("CartModelItem", "商品信息数据解析错误")
        return
    }

    val backgroundColor = when (product.selectstate) {
        0 -> Color(0xCBDEE6F7)
        1 -> Color(0xCBF7DEF4)
        -1 -> Color.Gray.copy(0.1f)
        else -> Color.Gray
    }
    val stockNum = product.stocknum
    val titledescription = product.titledescription
    val taglist = product.taglist
    val titletags = product.titletags
    val orderremark = product.orderremark
    val goodstag = product.goodstag

    val available = product.available

    var displayTags = ""
    displayTags += if (stockNum == 0) {
        "缺货 "
    } else {
        "库存${stockNum / 100} "
    }
    if (available == 0) {
        displayTags += "已下架 "
    }
    if (titledescription.isNotEmpty()) {
        displayTags += "$titledescription "
    }
    if (goodstag.isNotEmpty()) {
        displayTags += "$goodstag "
    }
    if (orderremark.isNotEmpty()) {
        displayTags += "$orderremark "
    }
    titletags.forEach { tag ->
        displayTags += "${tag.text} "
    }
    taglist.forEach { tag ->
        displayTags += "${tag.text} "
    }

    val actualPrice = (product.actualPrice?.displayMoney ?: product.price?.value) ?: 0

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor)
            .combinedClickable(
                onClick = { if (product.goodstagid != 3) onItemClick(product) },
                onLongClick = { onItemLongClick(product) }
            )
            .padding(horizontal = 12.dp, vertical = 8.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {

            // 商品图片
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(product.imgurl)
                    .crossfade(true)
                    .build(),
                contentDescription = product.title,
                modifier = Modifier
                    .size(46.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .background(Color.White),
                contentScale = ContentScale.Crop
            )

            Spacer(modifier = Modifier.width(6.dp))

            // 商品信息
            Column {
                // 商品标题
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = removeHtmlTags(product.title),
                        fontSize = 13.sp,
                        lineHeight = 13.sp,
                        maxLines = 1,
                        modifier = Modifier
                            .weight(1f) // 使用weight让标题占用剩余空间
                            .horizontalScroll(rememberScrollState())
                    )

                    if (stockNum > 0 && available != 0) {
                        Spacer(modifier = Modifier.width(2.dp)) // 添加间距
                        Box(
                            modifier = Modifier
                                .size(16.dp)
                                .clip(RoundedCornerShape(2.dp))
                                .background(
                                    Color.Transparent
                                )
                                .clickable { onDeleteItem(product) },
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Close, // 使用删除图标
                                contentDescription = "删除", // 无障碍描述
                                modifier = Modifier.size(14.dp), // 图标大小
                                tint = Color.Gray // 图标颜色
                            )
                        }
                    }
                }

                // 商品标签
                Row {
                    if (displayTags.isNotEmpty()) {
                        Text(
                            text = displayTags,
                            fontSize = 10.sp,
                            lineHeight = 10.sp,
                            color = Color.Gray,
                            modifier = Modifier
                                .horizontalScroll(rememberScrollState())
                        )
                    }
                }

                // 价格和数量控制
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // 价格信息
                    product.price?.let { price ->
                        Row(
                            verticalAlignment = Alignment.Bottom
                        ) {
                            Text(
                                text = "¥${actualPrice / 100.00}",
                                color = Color(0xFFFF5722),
                                fontSize = 12.sp,
                                lineHeight = 12.sp,
                            )

                            if (actualPrice > 0) {
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = if (price.market > actualPrice) "¥${price.market / 100.00}" else "¥${actualPrice / 100.00}",
                                    color = Color.Gray,
                                    fontSize = 11.sp,
                                    lineHeight = 11.sp,
                                    textDecoration = TextDecoration.LineThrough
                                )
                            }
                        }
                    }

                    if (stockNum > 0 && available != 0 && product.goodstagid != 3) {
                        // 数量控制器
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 减少按钮
                            Box(
                                modifier = Modifier
                                    .size(16.dp)
                                    .clip(RoundedCornerShape(2.dp))
                                    .background(
                                        Color(0xFFF5F5F5)
                                    )
                                    .clickable { onReduceItem(product) },
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "-",
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp,
                                    color = Color.Black
                                )
                            }

                            // 数量
                            Box(
                                modifier = Modifier
                                    .width(24.dp)
                                    .height(16.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "${product.num / 100}",
                                    lineHeight = 12.sp,
                                    fontSize = 12.sp
                                )
                            }

                            // 增加按钮
                            Box(
                                modifier = Modifier
                                    .size(16.dp)
                                    .clip(RoundedCornerShape(2.dp))
                                    .background(
                                        // 根据数量判断背景色
                                        if (product.num < product.stocknum) Color(0xFFF5F5F5) else Color(
                                            0xFFE0E0E0
                                        )
                                    )
                                    // 只有当数量小于库存时才可点击
                                    .clickable(enabled = product.num < product.stocknum) {
                                        onAddItem(
                                            product
                                        )
                                    },
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "+",
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp,
                                    // 根据数量判断文字颜色
                                    color = if (product.num < product.stocknum) Color.Black else Color.Gray
                                )
                            }
                        }
                    } else {
                        // 删除按钮
                        Box(
                            modifier = Modifier
                                .height(16.dp) // 与数量控制按钮同高
                                .clip(RoundedCornerShape(2.dp))
                                .background(
                                    Color(0xFFF5F5F5) // 与数量控制按钮同背景色
                                )
                                .clickable { onDeleteItem(product) }
                                .padding(horizontal = 8.dp), // 给文字一些边距
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Delete, // 使用删除图标
                                contentDescription = "删除", // 无障碍描述
                                modifier = Modifier.size(14.dp), // 图标大小
                                tint = Color.Gray // 图标颜色
                            )
                        }
                    }
                }
            }
        }
    }

    Spacer(modifier = Modifier.height(4.dp))
}