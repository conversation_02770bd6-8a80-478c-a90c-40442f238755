package dev.pigmomo.yhkit2025.ui.screens

import android.widget.Toast
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.ConfigTokenEntity
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.viewmodel.MainViewModel
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.runtime.remember
import androidx.compose.ui.draw.scale
import androidx.compose.ui.platform.LocalContext

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun MainScreen(viewModel: MainViewModel) {
    val context = LocalContext.current
    
    val inputParams by viewModel.inputParams
    val configTokens by viewModel.configTokens.collectAsState()
    val deviceInfo by viewModel.deviceInfo
    val clickedTokenUid by viewModel.clickedTokenUid
    val longClickedTokenUid by viewModel.longClickedTokenUid
    val isTokenDetailDialogVisible by viewModel.isTokenDetailDialogVisible

    // 获取长按选中的Token
    val selectedToken = configTokens.find { it.uid == longClickedTokenUid }

    // 判断输入参数是否为空
    val isInputParamsNotEmpty = inputParams.isNotEmpty()

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Row {
                        Text(
                            text = "YH KIT",
                        )
                        Text(
                            text = deviceInfo.ifEmpty { "IP,ID" },
                            fontSize = 15.sp,
                            maxLines = 1,
                            modifier = Modifier
                                .padding(start = 5.dp)
                                .horizontalScroll(rememberScrollState())
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    actionIconContentColor = Color.White
                ),
                actions = {
                    IconButton(onClick = { viewModel.openLogViewActivity() }) {
                        Icon(
                            Icons.Filled.Notifications,
                            contentDescription = "查看日志"
                        )
                    }
                    IconButton(onClick = { viewModel.pasteFromClipboard() }) {
                        Icon(
                            painter = painterResource(id = R.drawable.baseline_content_paste_24),
                            contentDescription = "粘贴",
                            tint = MaterialTheme.colorScheme.onPrimary
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .padding(paddingValues)
                .fillMaxSize()
                .imePadding()
        ) {
            Column {
                OutlinedTextField(
                    value = inputParams,
                    onValueChange = { viewModel.updateInputParams(it) },
                    label = { Text("配置参数") },
                    shape = RoundedCornerShape(16),
                    singleLine = true,
                    keyboardActions = KeyboardActions(
                        onDone = { viewModel.saveAndSetConfigToken() }
                    ),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MaterialTheme.colorScheme.primary,
                        unfocusedBorderColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f),
                        cursorColor = MaterialTheme.colorScheme.primary,
                    ),
                    modifier = Modifier
                        .padding(start = 12.dp, end = 12.dp, top = 8.dp)
                        .fillMaxWidth()
                )

                if (configTokens.isNotEmpty()) {
                    Text(
                        text = "最近载入配置(${configTokens.size}条):",
                        modifier = Modifier
                            .padding(
                                start = 12.dp,
                                end = 12.dp,
                                top = 12.dp,
                                bottom = 0.dp
                            ),
                        fontSize = 14.sp,
                    )
                }

                LazyColumn {
                    items(configTokens) { configToken ->
                        ConfigTokenItem(
                            configToken = configToken,
                            isClicked = clickedTokenUid == configToken.uid,
                            isLongClicked = longClickedTokenUid == configToken.uid,
                            onClick = { viewModel.saveConfigTokenToPrefs(configToken) },
                            onLongClick = {
                                viewModel.setLongClickedTokenUid(configToken.uid)
                                viewModel.setTokenDetailDialogVisible(true)
                            }
                        )
                    }
                }
            }

            Column(Modifier.align(Alignment.BottomEnd)) {
                FloatingActionButton(
                    onClick = { viewModel.openLoginActivity() },
                    containerColor = MaterialTheme.colorScheme.primary,
                    modifier = Modifier
                        .padding(end = 16.dp, start = 16.dp, top = 16.dp, bottom = 8.dp),
                    elevation = FloatingActionButtonDefaults.bottomAppBarFabElevation(),
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.baseline_egg_alt_24),
                        contentDescription = "账号注册"
                    )
                }

                FloatingActionButton(
                    onClick = { viewModel.openOrderActivity() },
                    containerColor = MaterialTheme.colorScheme.primary,
                    modifier = Modifier
                        .padding(end = 16.dp, start = 16.dp, top = 16.dp, bottom = 8.dp),
                    elevation = FloatingActionButtonDefaults.bottomAppBarFabElevation(),
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.baseline_add_shopping_cart_24),
                        contentDescription = "账号管理"
                    )
                }

                FloatingActionButton(
                    onClick = {
                        if (isInputParamsNotEmpty) {
                            viewModel.saveAndSetConfigToken()
                        } else {
                            Toast.makeText(context, "请输入配置参数", Toast.LENGTH_SHORT).show()
                        }
                    },
                    containerColor = MaterialTheme.colorScheme.primary,
                    modifier = Modifier
                        .padding(16.dp),
                    elevation = FloatingActionButtonDefaults.bottomAppBarFabElevation(),
                ) {
                    Icon(Icons.AutoMirrored.Filled.ArrowForward, contentDescription = "执行")
                }

            }
        }
    }

    // 显示Token详情对话框
    if (isTokenDetailDialogVisible && selectedToken != null) {
        TokenDetailDialog(
            token = selectedToken,
            viewModel = viewModel,
            onDismiss = { viewModel.setTokenDetailDialogVisible(false) }
        )
    }
}

@Composable
fun TokenDetailDialog(
    token: ConfigTokenEntity,
    viewModel: MainViewModel,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("账号信息") },
        containerColor = dialogContainerColor(),
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                // 操作按钮部分，使用与OrderScreen相同的样式
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row {
                        // 删除按钮
                        TokenActionButton(
                            imageVector = Icons.Filled.Delete,
                            text = "删除账号",
                            onClick = {
                                viewModel.copyTokenToClipboard(token)
                                viewModel.deleteConfigRecord(token)
                            },
                            tint = MaterialTheme.colorScheme.error
                        )

                        // 复制按钮
                        TokenActionButton(
                            icon = R.drawable.baseline_copy_all_24,
                            text = "复制账号",
                            onClick = {
                                viewModel.copyTokenToClipboard(token)
                            }
                        )

                        // 设定按钮
                        TokenActionButton(
                            imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                            text = "设定",
                            onClick = {
                                viewModel.saveConfigTokenToPrefs(token)
                            }
                        )
                    }
                }
                // 信息展示部分
                Column(
                    modifier = Modifier
                        .padding(8.dp)
                        .fillMaxWidth()
                ) {
                    Text("手机号: ${token.phoneNumber}")
                    Text("UID: ${token.uid}")
                    Text("更新日期: ${token.updateDate}")
                    token.extraNote.takeIf { it.isNotEmpty() }?.let {
                        Text("备注: $it")
                    }
                    Text("应用参数: ${token.appParam}")
                }
            }
        },
        confirmButton = {},
        dismissButton = null
    )
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ConfigTokenItem(
    configToken: ConfigTokenEntity,
    isClicked: Boolean,
    isLongClicked: Boolean,
    onClick: () -> Unit,
    onLongClick: () -> Unit
) {
    // 根据点击状态选择背景颜色
    val backgroundColor = when {
        isClicked -> Color(0xCBF7DEF4) // 点击后的颜色标记
        isLongClicked -> Color(0xCCBBDEFB) // 长按后的颜色标记
        else -> CardContainerColor // 默认颜色
    }

    Card(
        modifier = Modifier
            .padding(horizontal = 10.dp, vertical = 4.dp)
            .fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor,
        )
    ) {
        Column(
            modifier = Modifier
                .combinedClickable(
                    onClick = onClick,
                    onLongClick = onLongClick
                )
                .fillMaxWidth()
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
            ) {
                Text(
                    text = configToken.phoneNumber + ",${configToken.uid},${configToken.updateDate}",
                    maxLines = 1,
                    modifier = Modifier
                        .padding(
                            start = 8.dp,
                            end = 8.dp,
                            top = 18.dp,
                            bottom = 18.dp
                        )
                        .horizontalScroll(rememberScrollState())
                )
                Text(
                    text = configToken.extraNote,
                    maxLines = 1,
                    fontSize = 12.sp,
                    lineHeight = 12.sp,
                    color = Color.Unspecified.copy(0.8f),
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(end = 4.dp, bottom = 2.dp)
                )
            }
        }
    }
} 