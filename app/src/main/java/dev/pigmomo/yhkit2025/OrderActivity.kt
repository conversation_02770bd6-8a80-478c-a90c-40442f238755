package dev.pigmomo.yhkit2025

import android.content.ClipData
import android.content.ClipboardManager
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.repository.LogRepositoryImpl
import dev.pigmomo.yhkit2025.data.repository.TokenRepositoryImpl
import dev.pigmomo.yhkit2025.service.BackgroundService
import dev.pigmomo.yhkit2025.ui.screens.OrderScreen
import dev.pigmomo.yhkit2025.ui.theme.Yhkit2025Theme
import dev.pigmomo.yhkit2025.viewmodel.OrderViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class OrderActivity : ComponentActivity() {

    private lateinit var viewModel: OrderViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化数据库、仓库和ViewModel
        val database = AppDatabase.getDatabase(applicationContext)
        val orderTokenDao = database.orderTokenDao()
        val logDao = database.logDao()
        val deletedOrderDao = database.deletedOrderDao()

        val tokenRepository = TokenRepositoryImpl(orderTokenDao = orderTokenDao)
        val logRepository = LogRepositoryImpl(logDao = logDao)

        viewModel = ViewModelProvider(
            this,
            OrderViewModel.Factory(tokenRepository, application)
        )[OrderViewModel::class.java]

        // 注入日志仓库和删除订单DAO
        viewModel.setLogRepository(logRepository)
        viewModel.setDeletedOrderDao(deletedOrderDao)

        setContent {
            Yhkit2025Theme(dynamicColor = false) {
                OrderScreen(viewModel)
            }
        }
    }

    override fun onResume() {
        super.onResume()

        // 检查剪贴板变化
        lifecycleScope.launch {
            delay(500)
            // 在Composable函数外不能使用LocalClipboardManager，需要在OrderScreen中处理
            // 设置标志让OrderScreen检查剪贴板
            viewModel.checkClipboardOnResume = true
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // 重置后台服务的注册信息
        BackgroundService.resetRegistrationInfo()
    }
} 