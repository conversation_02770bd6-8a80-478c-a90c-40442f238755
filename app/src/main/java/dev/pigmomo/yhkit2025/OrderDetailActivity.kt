package dev.pigmomo.yhkit2025

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.lifecycle.ViewModelProvider
import com.google.gson.Gson
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailData

import dev.pigmomo.yhkit2025.ui.screens.OrderDetailScreen
import dev.pigmomo.yhkit2025.ui.theme.Yhkit2025Theme
import dev.pigmomo.yhkit2025.viewmodel.OrderDetailViewModel

/**
 * 订单详情界面的Activity
 */
class OrderDetailActivity : ComponentActivity() {
    
    private lateinit var viewModel: OrderDetailViewModel
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化ViewModel（不需要数据库依赖）
        viewModel = ViewModelProvider(
            this,
            OrderDetailViewModel.Factory(application)
        )[OrderDetailViewModel::class.java]
        
        // 从Intent中获取订单详情数据
        val orderDetailJson = intent.getStringExtra("orderDetail")
        val orderDetail = if (orderDetailJson != null) {
            try {
                Gson().fromJson(orderDetailJson, OrderDetailData::class.java)
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
        
        setContent {
            Yhkit2025Theme(dynamicColor = false) {
                OrderDetailScreen(
                    viewModel = viewModel,
                    orderDetail = orderDetail,
                    onBackClick = { finish() }
                )
            }
        }
    }
    
    companion object {
        /**
         * 创建启动OrderDetailActivity的Intent
         * @param orderDetail 订单详情数据
         * @return Intent
         */
        fun createIntent(
            context: android.content.Context,
            orderDetail: OrderDetailData
        ): android.content.Intent {
            val intent = android.content.Intent(context, OrderDetailActivity::class.java)
            val gson = Gson()
            intent.putExtra("orderDetail", gson.toJson(orderDetail))
            return intent
        }
    }
}
