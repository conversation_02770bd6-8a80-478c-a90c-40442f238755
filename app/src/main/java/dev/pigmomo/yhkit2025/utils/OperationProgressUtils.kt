package dev.pigmomo.yhkit2025.utils

import android.util.Log
import dev.pigmomo.yhkit2025.utils.BatchOperationUtils.OperationType
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger

/**
 * 批量操作进度工具类
 *
 * 用于记录批量操作的类型、操作列表、总数量、当前进度、成功数量和失败数量
 * 提供了实时进度更新和状态查询功能
 */
object OperationProgressUtils {
    private const val TAG = "OperationProgressUtils"

    /**
     * 操作进度数据类
     *
     * @property operationType 操作类型 (账号操作或活动操作)
     * @property operations 操作列表
     * @property totalCount 总操作数量
     * @property currentProgress 当前进度
     * @property successCount 成功数量
     * @property failCount 失败数量
     * @property isRunning 是否正在运行
     */
    data class OperationProgress(
        val operationType: OperationType = OperationType.ACCOUNT,
        val operations: Set<String> = emptySet(),
        val totalCount: Int = 0,
        val currentProgress: Int = 0,
        val successCount: Int = 0,
        val failCount: Int = 0,
        val isRunning: Boolean = false
    )

    /**
     * 定义操作的类型，分为账号操作和活动操作。
     */
    enum class OperationType {
        ACCOUNT,
        ACTIVITY,
        ORDER,
        COUPON
    }

    // 使用MutableStateFlow存储进度状态，便于在UI中观察
    private val _progressFlow = MutableStateFlow(OperationProgress())

    /**
     * 进度状态流，可被观察以实时更新UI
     */
    val progressFlow: StateFlow<OperationProgress> = _progressFlow.asStateFlow()

    // 使用ConcurrentHashMap跟踪已记录的操作，确保线程安全
    // 键为操作索引，值为操作状态（true表示成功，false表示失败）
    private val recordedOperations = ConcurrentHashMap<Int, Boolean>()
    
    // 为没有提供操作索引的调用生成唯一ID
    private val autoIncrementIndex = AtomicInteger(10000)

    /**
     * 初始化操作进度
     *
     * @param operationType 操作类型
     * @param operations 操作列表
     * @param totalCount 总操作数量
     */
    fun initProgress(operationType: OperationType, operations: Set<String>, totalCount: Int) {
        Log.d(
            TAG,
            "Initializing progress: type=$operationType, operations=$operations, total=$totalCount"
        )
        // 重置已记录操作的跟踪
        recordedOperations.clear()
        // 重置自增索引
        autoIncrementIndex.set(10000)
        
        _progressFlow.value = OperationProgress(
            operationType = operationType,
            operations = operations,
            totalCount = totalCount,
            currentProgress = 0,
            successCount = 0,
            failCount = 0,
            isRunning = true
        )
    }

    /**
     * 更新当前进度
     *
     * @param increment 进度增量，默认为1
     * @param operationIndex 操作索引，用于跟踪哪个操作正在更新
     */
    fun updateProgress(increment: Int = 1, operationIndex: Int = -1) {
        _progressFlow.update { currentState ->
            currentState.copy(
                currentProgress = currentState.currentProgress + increment
            )
        }
        Log.d(
            TAG,
            "Progress updated: ${_progressFlow.value.currentProgress}/${_progressFlow.value.totalCount}"
        )
    }

    /**
     * 更新总操作数量
     *
     * @param totalCount 总操作数量
     */
    fun updateTotalCount(totalCount: Int) {
        _progressFlow.update { currentState ->
            currentState.copy(
                totalCount = totalCount
            )
        }
    }

    /**
     * 记录操作成功
     *
     * @param increment 成功数量增量，默认为1
     * @param operationIndex 操作索引，用于防止重复记录
     *                      如果为-1，则自动生成唯一索引
     */
    @JvmOverloads
    fun recordSuccess(increment: Int = 1, operationIndex: Int = -1) {
        // 确定操作索引
        val actualIndex = if (operationIndex >= 0) {
            operationIndex
        } else {
            // 为旧代码调用生成唯一索引
            autoIncrementIndex.getAndIncrement()
        }
        
        // 检查此操作是否已记录
        if (recordedOperations.putIfAbsent(actualIndex, true) != null) {
            Log.d(TAG, "Operation $actualIndex already recorded, skipping")
            return
        }
        
        _progressFlow.update { currentState ->
            currentState.copy(
                successCount = currentState.successCount + increment
            )
        }
        Log.d(TAG, "Success recorded for operation $actualIndex: ${_progressFlow.value.successCount}")
    }

    /**
     * 记录操作失败
     *
     * @param increment 失败数量增量，默认为1
     * @param operationIndex 操作索引，用于防止重复记录
     *                      如果为-1，则自动生成唯一索引
     */
    @JvmOverloads
    fun recordFailure(increment: Int = 1, operationIndex: Int = -1) {
        // 确定操作索引
        val actualIndex = if (operationIndex >= 0) {
            operationIndex
        } else {
            // 为旧代码调用生成唯一索引
            autoIncrementIndex.getAndIncrement()
        }
        
        // 检查此操作是否已记录
        if (recordedOperations.putIfAbsent(actualIndex, false) != null) {
            Log.d(TAG, "Operation $actualIndex already recorded, skipping")
            return
        }
        
        _progressFlow.update { currentState ->
            currentState.copy(
                failCount = currentState.failCount + increment
            )
        }
        Log.d(TAG, "Failure recorded for operation $actualIndex: ${_progressFlow.value.failCount}")
    }

    /**
     * 完成操作进度记录
     */
    fun completeProgress() {
        _progressFlow.update { currentState ->
            currentState.copy(
                isRunning = false
            )
        }
        Log.d(
            TAG,
            "Operation completed: success=${_progressFlow.value.successCount}, fail=${_progressFlow.value.failCount}"
        )
    }

    /**
     * 重置进度记录
     */
    fun resetProgress() {
        recordedOperations.clear()
        autoIncrementIndex.set(10000)
        _progressFlow.value = OperationProgress()
        Log.d(TAG, "Progress reset")
    }

    /**
     * 获取当前进度百分比
     *
     * @return 进度百分比，范围0-100
     */
    fun getProgressPercentage(): Int {
        val current = _progressFlow.value
        return if (current.totalCount > 0) {
            (current.successCount + current.failCount) * 100 / current.totalCount
        } else {
            0
        }
    }

    /**
     * 获取操作摘要信息
     *
     * @return 包含操作类型、进度和成功/失败数量的摘要字符串
     */
    fun getProgressSummary(): String {
        val current = _progressFlow.value
        return "类型: ${current.operationType.name}, " +
                "操作: ${current.operations.joinToString(", ")}, " +
                "进度: ${current.currentProgress}/${current.totalCount} (${getProgressPercentage()}%), " +
                "成功: ${current.successCount}, " +
                "失败: ${current.failCount}"
    }

    /**
     * 检查操作是否正在进行中
     *
     * @return 是否有操作正在进行
     */
    fun isOperationInProgress(): Boolean {
        return _progressFlow.value.isRunning
    }
} 