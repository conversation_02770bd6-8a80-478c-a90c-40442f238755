package dev.pigmomo.yhkit2025.utils

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject

/**
 * 手机号归属地查询工具类
 * 提供手机号归属地、运营商等信息的查询功能
 */
object PhoneLocationUtils {
    // 缓存查询结果，避免重复请求
    private val phoneLocationCache = mutableMapOf<String, String>()
    
    // 日志标签
    private const val TAG = "PhoneLocationUtils"
    
    /**
     * 获取手机号归属地信息
     * @param phone 手机号码
     * @return 格式化的归属地信息字符串，格式为"省份城市/运营商"，失败返回空字符串
     */
    suspend fun getPhoneLocationFromQqsuu(phone: String): String {
        // 检查参数有效性
        if (phone.length < 7) {
            Log.e(TAG, "Invalid phone number length: ${phone.length}")
            return ""
        }
        
        val subPhoneStr = phone.substring(0, 7)
        
        // 检查缓存
        phoneLocationCache[subPhoneStr]?.let {
            Log.d(TAG, "Phone location found in cache for: $subPhoneStr")
            return it
        }
        
        // 在IO线程中执行网络请求
        return withContext(Dispatchers.IO) {
            try {
                val url =
                    "https://api.qqsuu.cn/api/dm-mobilelocal?phone=$subPhoneStr&apiKey=cbf64781b829b8497847c1329ae45276"
                val client = OkHttpClient()

                val request = Request.Builder()
                    .url(url)
                    .build()

                val response = client.newCall(request).execute()
                val resJSONObject = JSONObject(response.body?.string()?:"")
                if (resJSONObject.getInt("code") == 200) {
                    val data = resJSONObject.getJSONObject("data")
                    val mobileprovice = data.getString("mobileprovice")
                    val mobilearea = data.getString("mobilearea")
                    val mobiletype = data.getString("mobiletype")
                    val result = "$mobileprovice$mobilearea/$mobiletype"
                    
                    // 保存到缓存
                    phoneLocationCache[subPhoneStr] = result
                    Log.d(TAG, "Added phone location to cache for: $subPhoneStr")
                    
                    result
                } else {
                    Log.e(TAG, "API returned error code: ${resJSONObject.getInt("code")}")
                    ""
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error querying phone location: ${e}")
                ""
            }
        }
    }

     /**
     * 从Fly63 API获取手机号归属地信息
     * @param phonePrefix 手机号前7位
     * @return 格式化的归属地信息字符串，格式为"省份城市/运营商"，失败返回空字符串
     */
    suspend fun getPhoneLocationFromFly63(phonePrefix: String): String {
        return withContext(Dispatchers.IO) {
            try {
                val url = "https://api.fly63.com/api/mobile/api.php"
                val client = OkHttpClient()
                
                val jsonBody = JSONObject().apply {
                    put("mobile", phonePrefix)
                }.toString()
                
                val mediaType = "application/json;charset=UTF-8".toMediaType()
                val requestBody = jsonBody.toRequestBody(mediaType)
                
                val request = Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .header("Host", "api.fly63.com")
                    .header("User-Agent", "Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36")
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .header("Accept", "*/*")
                    .header("Origin", "https://fly63.com")
                    .header("Referer", "https://fly63.com/")
                    .build()
                
                val response = client.newCall(request).execute()
                val responseString = response.body?.string() ?: ""
                
                if (responseString.isNotEmpty()) {
                    val resJSONObject = JSONObject(responseString)
                    if (resJSONObject.getInt("code") == 200) {
                        val data = resJSONObject.getJSONObject("data")
                        val province = data.getString("province")
                        val city = data.getString("city")
                        val op = data.getString("op")
                        val result = "$province$city/$op"
                        
                        // 保存到缓存
                        phoneLocationCache[phonePrefix] = result
                        Log.d(TAG, "Added phone location to cache from Fly63 for: $phonePrefix")
                        
                        return@withContext result
                    } else {
                        Log.e(TAG, "Fly63 API returned error")
                    }
                }
                
                return@withContext ""
            } catch (e: Exception) {
                Log.e(TAG, "Error querying phone location from Fly63: ${e.message}")
                return@withContext ""
            }
        }
    }
    
    /**
     * 清除缓存
     */
    fun clearCache() {
        phoneLocationCache.clear()
        Log.d(TAG, "Phone location cache cleared")
    }
    
    /**
     * 获取缓存大小
     * @return 缓存中的条目数量
     */
    fun getCacheSize(): Int {
        return phoneLocationCache.size
    }
} 