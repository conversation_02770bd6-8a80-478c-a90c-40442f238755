package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.repository.LogRepositoryImpl
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import java.io.File
import java.util.Date

/**
 * 数据库维护工具类
 * 提供数据库健康检查、清理和优化功能
 */
object DatabaseMaintenanceUtils {
    private const val TAG = "DatabaseMaintenance"
    private const val MAX_DATABASE_SIZE_MB = 100 // 最大数据库大小限制（MB）
    private const val LOG_RETENTION_DAYS = 7 // 日志保留天数
    private const val HEALTH_CHECK_TIMEOUT_MS = 5000L // 健康检查超时时间
    
    /**
     * 检查数据库健康状态
     * @param context 应用上下文
     * @return 数据库健康状态信息
     */
    suspend fun checkDatabaseHealth(context: Context): DatabaseHealthInfo {
        val startTime = System.currentTimeMillis()
        
        return try {
            val database = AppDatabase.getDatabase(context)
            val logDao = database.logDao()
            val logRepository = LogRepositoryImpl(logDao)
            
            // 检查数据库文件大小
            val dbFile = context.getDatabasePath("yhkit_database")
            val dbSizeMB = if (dbFile.exists()) {
                dbFile.length() / (1024 * 1024).toDouble()
            } else {
                0.0
            }
            
            // 执行简单查询测试响应性
            val queryStartTime = System.currentTimeMillis()
            val testResult = withTimeoutOrNull(HEALTH_CHECK_TIMEOUT_MS) {
                logRepository.getLogsWithPagination(0, 1)
            }
            val queryTime = System.currentTimeMillis() - queryStartTime
            
            // 获取日志总数
            val totalLogs = withTimeoutOrNull(HEALTH_CHECK_TIMEOUT_MS) {
                logRepository.getLogCount()
            } ?: 0
            
            val totalTime = System.currentTimeMillis() - startTime
            
            DatabaseHealthInfo(
                isHealthy = testResult != null && queryTime < 3000,
                databaseSizeMB = dbSizeMB,
                queryResponseTimeMs = queryTime,
                totalLogCount = totalLogs,
                totalCheckTimeMs = totalTime,
                issues = mutableListOf<String>().apply {
                    if (dbSizeMB > MAX_DATABASE_SIZE_MB) {
                        add("数据库大小超过限制: ${dbSizeMB}MB > ${MAX_DATABASE_SIZE_MB}MB")
                    }
                    if (queryTime > 3000) {
                        add("查询响应时间过长: ${queryTime}ms")
                    }
                    if (testResult == null) {
                        add("数据库查询超时或失败")
                    }
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "Database health check failed", e)
            DatabaseHealthInfo(
                isHealthy = false,
                databaseSizeMB = 0.0,
                queryResponseTimeMs = -1,
                totalLogCount = 0,
                totalCheckTimeMs = System.currentTimeMillis() - startTime,
                issues = listOf("数据库健康检查异常: ${e.message}")
            )
        }
    }
    
    /**
     * 执行数据库清理
     * @param context 应用上下文
     * @param retentionDays 日志保留天数，默认为7天
     * @return 清理结果
     */
    suspend fun performDatabaseCleanup(
        context: Context, 
        retentionDays: Int = LOG_RETENTION_DAYS
    ): DatabaseCleanupResult {
        val startTime = System.currentTimeMillis()
        
        return try {
            val database = AppDatabase.getDatabase(context)
            val logDao = database.logDao()
            val logRepository = LogRepositoryImpl(logDao)
            
            // 计算清理时间点
            val cutoffTime = Date(System.currentTimeMillis() - retentionDays * 24 * 60 * 60 * 1000L)
            
            Log.d(TAG, "Starting database cleanup, removing logs before $cutoffTime")
            
            // 删除过期日志
            val deletedCount = logRepository.deleteLogsBefore(cutoffTime)
            
            val totalTime = System.currentTimeMillis() - startTime
            
            Log.d(TAG, "Database cleanup completed: deleted $deletedCount logs in ${totalTime}ms")
            
            DatabaseCleanupResult(
                success = true,
                deletedLogCount = deletedCount,
                cleanupTimeMs = totalTime,
                message = "成功清理 $deletedCount 条过期日志"
            )
        } catch (e: Exception) {
            val totalTime = System.currentTimeMillis() - startTime
            Log.e(TAG, "Database cleanup failed", e)
            
            DatabaseCleanupResult(
                success = false,
                deletedLogCount = 0,
                cleanupTimeMs = totalTime,
                message = "数据库清理失败: ${e.message}"
            )
        }
    }
    
    /**
     * 自动维护数据库
     * 根据数据库健康状态自动执行相应的维护操作
     * @param context 应用上下文
     */
    fun performAutoMaintenance(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "Starting automatic database maintenance")
                
                // 检查数据库健康状态
                val healthInfo = checkDatabaseHealth(context)
                
                Log.d(TAG, "Database health check result: ${healthInfo}")
                
                if (!healthInfo.isHealthy) {
                    Log.w(TAG, "Database health issues detected: ${healthInfo.issues}")
                    
                    // 如果数据库大小过大或响应时间过长，执行清理
                    if (healthInfo.databaseSizeMB > MAX_DATABASE_SIZE_MB || 
                        healthInfo.queryResponseTimeMs > 3000) {
                        
                        val cleanupResult = performDatabaseCleanup(context)
                        Log.d(TAG, "Auto cleanup result: ${cleanupResult}")
                        
                        // 清理后再次检查健康状态
                        if (cleanupResult.success) {
                            val postCleanupHealth = checkDatabaseHealth(context)
                            Log.d(TAG, "Post-cleanup health: ${postCleanupHealth}")
                        }
                    }
                } else {
                    Log.d(TAG, "Database is healthy, no maintenance needed")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Auto maintenance failed", e)
            }
        }
    }
    
    /**
     * 获取数据库文件大小
     * @param context 应用上下文
     * @return 数据库文件大小（MB）
     */
    fun getDatabaseSizeMB(context: Context): Double {
        return try {
            val dbFile = context.getDatabasePath("yhkit_database")
            if (dbFile.exists()) {
                dbFile.length() / (1024 * 1024).toDouble()
            } else {
                0.0
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get database size", e)
            0.0
        }
    }
}

/**
 * 数据库健康状态信息
 */
data class DatabaseHealthInfo(
    val isHealthy: Boolean,
    val databaseSizeMB: Double,
    val queryResponseTimeMs: Long,
    val totalLogCount: Int,
    val totalCheckTimeMs: Long,
    val issues: List<String>
) {
    override fun toString(): String {
        return "DatabaseHealthInfo(healthy=$isHealthy, size=${String.format("%.2f", databaseSizeMB)}MB, " +
                "queryTime=${queryResponseTimeMs}ms, logs=$totalLogCount, issues=$issues)"
    }
}

/**
 * 数据库清理结果
 */
data class DatabaseCleanupResult(
    val success: Boolean,
    val deletedLogCount: Int,
    val cleanupTimeMs: Long,
    val message: String
) {
    override fun toString(): String {
        return "DatabaseCleanupResult(success=$success, deleted=$deletedLogCount, " +
                "time=${cleanupTimeMs}ms, message='$message')"
    }
}
