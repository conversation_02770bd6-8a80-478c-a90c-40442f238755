package dev.pigmomo.yhkit2025.utils

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import androidx.compose.ui.text.AnnotatedString
import dev.pigmomo.yhkit2025.data.model.ConfigTokenEntity
import dev.pigmomo.yhkit2025.data.model.TokenEntity

/**
 * 剪贴板工具类 - 处理剪贴板相关操作
 */
object ClipboardUtils {

    /**
     * 复制令牌信息到剪贴板
     *
     * @param clipboardManager 剪贴板管理器
     * @param token 要复制的令牌
     */
    fun copyToken(clipboardManager: ClipboardManager, token: TokenEntity) {
        val tokenString = listOfNotNull(
            token.phoneNumber,
            token.uid,
            token.userKey + "-601933-" + token.accessToken,
            token.refreshToken,
            token.appParam + "," + token.expiresIn,
            token.updateDate + " " + token.extraNote,
        ).joinToString("----")

        val clipData = ClipData.newPlainText("Token", tokenString)
        clipboardManager.setPrimaryClip(clipData)
    }

    /**
     * 从剪贴板获取文本
     *
     * @param context 上下文
     * @return 剪贴板中的文本，如果没有则返回null
     */
    fun getTextFromClipboard(context: Context): String? {
        val clipboardManager =
            context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        return clipboardManager.primaryClip?.getItemAt(0)?.text?.toString()
    }

    /**
     * 复制文本到剪贴板
     *
     * @param context 上下文
     * @param text 要复制的文本
     * @param label 剪贴板标签
     */
    fun copyTextToClipboard(context: Context, text: String, label: String = "Text") {
        val clipboardManager =
            context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clipData = ClipData.newPlainText(label, text)
        clipboardManager.setPrimaryClip(clipData)
    }

    /**
     * 复制文本到剪贴板
     *
     * @param clipboardManager 剪贴板管理器
     * @param text 要复制的文本
     * @param label 剪贴板标签
     */
    fun copyTextToClipboard(clipboardManager: ClipboardManager, text: String, label: String = "Text") {
        val clipData = ClipData.newPlainText(label, text)
        clipboardManager.setPrimaryClip(clipData)
    }
} 