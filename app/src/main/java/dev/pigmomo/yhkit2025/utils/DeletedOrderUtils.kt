package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.model.DeletedOrderEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 删除订单记录工具类
 * 提供便捷的删除订单记录查询和统计功能
 */
object DeletedOrderUtils {
    
    private const val TAG = "DeletedOrderUtils"
    
    /**
     * 获取所有删除订单记录
     */
    fun getAllDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        return database.deletedOrderDao().getAllDeletedOrders()
    }
    
    /**
     * 根据账号UID获取删除订单记录
     */
    fun getDeletedOrdersByUid(context: Context, uid: String): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        return database.deletedOrderDao().getDeletedOrdersByUid(uid)
    }
    
    /**
     * 根据手机号获取删除订单记录
     */
    fun getDeletedOrdersByPhoneNumber(context: Context, phoneNumber: String): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        return database.deletedOrderDao().getDeletedOrdersByPhoneNumber(phoneNumber)
    }
    
    /**
     * 获取今日删除的订单记录
     */
    fun getTodayDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfDay = calendar.timeInMillis
        
        calendar.add(Calendar.DAY_OF_MONTH, 1)
        val startOfNextDay = calendar.timeInMillis
        
        return database.deletedOrderDao().getDeletedOrdersByDateRange(startOfDay, startOfNextDay)
    }
    
    /**
     * 获取本周删除的订单记录
     */
    fun getThisWeekDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        val calendar = Calendar.getInstance()
        
        // 设置为本周第一天（周一）
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfWeek = calendar.timeInMillis
        
        // 设置为下周第一天
        calendar.add(Calendar.WEEK_OF_YEAR, 1)
        val startOfNextWeek = calendar.timeInMillis
        
        return database.deletedOrderDao().getDeletedOrdersByDateRange(startOfWeek, startOfNextWeek)
    }
    
    /**
     * 获取本月删除的订单记录
     */
    fun getThisMonthDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        val calendar = Calendar.getInstance()
        
        // 设置为本月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfMonth = calendar.timeInMillis
        
        // 设置为下月第一天
        calendar.add(Calendar.MONTH, 1)
        val startOfNextMonth = calendar.timeInMillis
        
        return database.deletedOrderDao().getDeletedOrdersByDateRange(startOfMonth, startOfNextMonth)
    }
    
    /**
     * 检查订单是否已被删除
     */
    suspend fun isOrderDeleted(context: Context, orderId: String): Boolean {
        return try {
            val database = AppDatabase.getDatabase(context)
            database.deletedOrderDao().isOrderDeleted(orderId)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check if order is deleted: ${e.message}")
            false
        }
    }
    
    /**
     * 获取删除订单统计信息
     */
    suspend fun getDeletedOrderStatistics(context: Context): DeletedOrderStatistics {
        return try {
            val database = AppDatabase.getDatabase(context)
            val dao = database.deletedOrderDao()
            
            val totalCount = dao.getDeletedOrderCount()
            
            // 获取今日删除数量
            val calendar = Calendar.getInstance()
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfDay = calendar.timeInMillis
            calendar.add(Calendar.DAY_OF_MONTH, 1)
            val startOfNextDay = calendar.timeInMillis
            val todayCount = dao.getDeletedOrderCountByDateRange(startOfDay, startOfNextDay)
            
            // 获取本周删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfWeek = calendar.timeInMillis
            calendar.add(Calendar.WEEK_OF_YEAR, 1)
            val startOfNextWeek = calendar.timeInMillis
            val thisWeekCount = dao.getDeletedOrderCountByDateRange(startOfWeek, startOfNextWeek)
            
            // 获取本月删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_MONTH, 1)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfMonth = calendar.timeInMillis
            calendar.add(Calendar.MONTH, 1)
            val startOfNextMonth = calendar.timeInMillis
            val thisMonthCount = dao.getDeletedOrderCountByDateRange(startOfMonth, startOfNextMonth)
            
            DeletedOrderStatistics(
                totalCount = totalCount,
                todayCount = todayCount,
                thisWeekCount = thisWeekCount,
                thisMonthCount = thisMonthCount
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get deleted order statistics: ${e.message}")
            DeletedOrderStatistics()
        }
    }
    
    /**
     * 获取指定账号的删除订单统计信息
     */
    suspend fun getDeletedOrderStatisticsByUid(context: Context, uid: String): DeletedOrderStatistics {
        return try {
            val database = AppDatabase.getDatabase(context)
            val dao = database.deletedOrderDao()
            
            val totalCount = dao.getDeletedOrderCountByUid(uid)
            
            // 获取今日删除数量
            val calendar = Calendar.getInstance()
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfDay = calendar.timeInMillis
            calendar.add(Calendar.DAY_OF_MONTH, 1)
            val startOfNextDay = calendar.timeInMillis
            val todayRecords = dao.getDeletedOrdersByUidAndDateRange(uid, startOfDay, startOfNextDay).first()
            val todayCount = todayRecords.size
            
            // 获取本周删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfWeek = calendar.timeInMillis
            calendar.add(Calendar.WEEK_OF_YEAR, 1)
            val startOfNextWeek = calendar.timeInMillis
            val thisWeekRecords = dao.getDeletedOrdersByUidAndDateRange(uid, startOfWeek, startOfNextWeek).first()
            val thisWeekCount = thisWeekRecords.size
            
            // 获取本月删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_MONTH, 1)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfMonth = calendar.timeInMillis
            calendar.add(Calendar.MONTH, 1)
            val startOfNextMonth = calendar.timeInMillis
            val thisMonthRecords = dao.getDeletedOrdersByUidAndDateRange(uid, startOfMonth, startOfNextMonth).first()
            val thisMonthCount = thisMonthRecords.size
            
            DeletedOrderStatistics(
                totalCount = totalCount,
                todayCount = todayCount,
                thisWeekCount = thisWeekCount,
                thisMonthCount = thisMonthCount
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get deleted order statistics by uid: ${e.message}")
            DeletedOrderStatistics()
        }
    }
    
    /**
     * 格式化删除时间
     */
    fun formatDeleteTime(timestamp: Long): String {
        return try {
            val date = Date(timestamp)
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            formatter.format(date)
        } catch (e: Exception) {
            "时间格式错误"
        }
    }
    
    /**
     * 清理指定时间之前的删除记录（用于数据清理）
     */
    suspend fun cleanupOldRecords(context: Context, daysToKeep: Int = 30): Int {
        return try {
            val database = AppDatabase.getDatabase(context)
            val calendar = Calendar.getInstance()
            calendar.add(Calendar.DAY_OF_MONTH, -daysToKeep)
            val cutoffTime = calendar.timeInMillis
            
            database.deletedOrderDao().deleteDeletedOrdersBeforeTimestamp(cutoffTime)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup old records: ${e.message}")
            0
        }
    }
}

/**
 * 删除订单统计信息数据类
 */
data class DeletedOrderStatistics(
    val totalCount: Int = 0,
    val todayCount: Int = 0,
    val thisWeekCount: Int = 0,
    val thisMonthCount: Int = 0
)
