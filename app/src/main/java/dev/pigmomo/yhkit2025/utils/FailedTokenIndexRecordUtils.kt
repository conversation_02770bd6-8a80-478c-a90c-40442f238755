package dev.pigmomo.yhkit2025.utils

import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

/**
 * 用于临时记录失败账号序号的工具类
 *
 * 失败账号序号按照操作类型进行分类：
 * - 批量账号操作失败序号
 * - 批量活动操作失败序号
 * - 批量下单操作失败序号
 * - 批量抢券操作失败序号
 */
object FailedTokenIndexRecordUtils {
    private const val TAG = "FailedTokenIndexRecordUtils"

    /**
     * 操作类型枚举
     */
    enum class OperationType {
        ACCOUNT_OPERATION,  // 账号操作
        ACTIVITY_OPERATION, // 活动操作
        ORDER_OPERATION,    // 订单操作
        COUPON_OPERATION    // 抢券操作
    }

    // 使用 MutableStateFlow 存储各类型的失败账号序号集合
    private val _accountOperationFailedTokenIndexes = MutableStateFlow<Set<Int>>(emptySet())
    private val _activityOperationFailedTokenIndexes = MutableStateFlow<Set<Int>>(emptySet())
    private val _orderOperationFailedTokenIndexes = MutableStateFlow<Set<Int>>(emptySet())
    private val _couponOperationFailedTokenIndexes = MutableStateFlow<Set<Int>>(emptySet())

    // 对外暴露只读的 StateFlow
    val accountOperationFailedIndexes: StateFlow<Set<Int>> =
        _accountOperationFailedTokenIndexes.asStateFlow()
    val activityOperationFailedIndexes: StateFlow<Set<Int>> =
        _activityOperationFailedTokenIndexes.asStateFlow()
    val orderOperationFailedIndexes: StateFlow<Set<Int>> =
        _orderOperationFailedTokenIndexes.asStateFlow()
    val couponOperationFailedIndexes: StateFlow<Set<Int>> =
        _couponOperationFailedTokenIndexes.asStateFlow()

    /**
     * 记录失败账号序号
     *
     * @param type 操作类型
     * @param accountIndex 失败账号序号
     * @param operationName 操作名称，用于日志记录
     */
    fun recordFailedTokenIndex(type: OperationType, accountIndex: Int, operationName: String = "") {
        if (accountIndex <= 0) {
            Log.w(TAG, "Invalid account index: $accountIndex")
            return
        }

        when (type) {
            OperationType.ACCOUNT_OPERATION -> {
                _accountOperationFailedTokenIndexes.update { currentSet ->
                    currentSet + accountIndex
                }
                Log.d(
                    TAG,
                    "Recorded failed account operation index: $accountIndex, operation: $operationName"
                )
            }

            OperationType.ACTIVITY_OPERATION -> {
                _activityOperationFailedTokenIndexes.update { currentSet ->
                    currentSet + accountIndex
                }
                Log.d(
                    TAG,
                    "Recorded failed activity operation index: $accountIndex, operation: $operationName"
                )
            }

            OperationType.ORDER_OPERATION -> {
                _orderOperationFailedTokenIndexes.update { currentSet ->
                    currentSet + accountIndex
                }
                Log.d(
                    TAG,
                    "Recorded failed order operation index: $accountIndex, operation: $operationName"
                )
            }
            
            OperationType.COUPON_OPERATION -> {
                _couponOperationFailedTokenIndexes.update { currentSet ->
                    currentSet + accountIndex
                }
                Log.d(
                    TAG,
                    "Recorded failed coupon operation index: $accountIndex, operation: $operationName"
                )
            }
        }
    }

    /**
     * 获取指定类型的失败账号序号集合
     *
     * @param type 操作类型
     * @return 失败账号序号集合
     */
    fun getFailedTokenIndexes(type: OperationType): Set<Int> {
        return when (type) {
            OperationType.ACCOUNT_OPERATION -> _accountOperationFailedTokenIndexes.value
            OperationType.ACTIVITY_OPERATION -> _activityOperationFailedTokenIndexes.value
            OperationType.ORDER_OPERATION -> _orderOperationFailedTokenIndexes.value
            OperationType.COUPON_OPERATION -> _couponOperationFailedTokenIndexes.value
        }
    }

    /**
     * 清除指定类型的失败账号序号记录
     *
     * @param type 操作类型，如果为null则清除所有类型
     */
    fun clearFailedTokenIndexes(type: OperationType? = null) {
        when (type) {
            OperationType.ACCOUNT_OPERATION -> {
                _accountOperationFailedTokenIndexes.value = emptySet()
                Log.d(TAG, "Cleared account operation failed indexes")
            }

            OperationType.ACTIVITY_OPERATION -> {
                _activityOperationFailedTokenIndexes.value = emptySet()
                Log.d(TAG, "Cleared activity operation failed indexes")
            }

            OperationType.ORDER_OPERATION -> {
                _orderOperationFailedTokenIndexes.value = emptySet()
                Log.d(TAG, "Cleared order operation failed indexes")
            }
            
            OperationType.COUPON_OPERATION -> {
                _couponOperationFailedTokenIndexes.value = emptySet()
                Log.d(TAG, "Cleared coupon operation failed indexes")
            }

            null -> {
                _accountOperationFailedTokenIndexes.value = emptySet()
                _activityOperationFailedTokenIndexes.value = emptySet()
                _orderOperationFailedTokenIndexes.value = emptySet()
                _couponOperationFailedTokenIndexes.value = emptySet()
                Log.d(TAG, "Cleared all failed indexes")
            }
        }
    }

    /**
     * 将失败账号序号列表转换为范围表示的字符串
     * 例如: [1,2,3,5,6,9] -> "1-3,5-6,9"
     *
     * @param indexes 失败账号序号集合
     * @return 范围表示的字符串
     */
    fun convertToRangeString(indexes: Set<Int>): String {
        if (indexes.isEmpty()) {
            return ""
        }

        val sortedIndexes = indexes.sorted()
        val ranges = mutableListOf<String>()

        var start = sortedIndexes[0]
        var end = start

        for (i in 1 until sortedIndexes.size) {
            val current = sortedIndexes[i]
            if (current == end + 1) {
                // 连续序号，扩展当前范围
                end = current
            } else {
                // 不连续，保存当前范围并开始新范围
                ranges.add(if (start == end) start.toString() else "$start-$end")
                start = current
                end = current
            }
        }

        // 添加最后一个范围
        ranges.add(if (start == end) start.toString() else "$start-$end")

        return ranges.joinToString(",")
    }

    /**
     * 获取指定类型的失败账号序号范围字符串
     *
     * @param type 操作类型
     * @return 失败账号序号范围字符串
     */
    fun getFailedTokenIndexesRangeString(type: OperationType): String {
        val indexes = getFailedTokenIndexes(type)
        return convertToRangeString(indexes)
    }

    /**
     * 检查指定账号序号是否已记录为失败
     *
     * @param type 操作类型
     * @param accountIndex 账号序号
     * @return 是否已记录为失败
     */
    fun isTokenIndexFailed(type: OperationType, accountIndex: Int): Boolean {
        return when (type) {
            OperationType.ACCOUNT_OPERATION -> _accountOperationFailedTokenIndexes.value.contains(
                accountIndex
            )

            OperationType.ACTIVITY_OPERATION -> _activityOperationFailedTokenIndexes.value.contains(
                accountIndex
            )

            OperationType.ORDER_OPERATION -> _orderOperationFailedTokenIndexes.value.contains(
                accountIndex
            )
            
            OperationType.COUPON_OPERATION -> _couponOperationFailedTokenIndexes.value.contains(
                accountIndex
            )
        }
    }
} 