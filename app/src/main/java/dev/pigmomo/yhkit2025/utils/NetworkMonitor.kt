package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 网络状态监听器
 * 监听网络连接状态变化，并提供网络状态流
 */
object NetworkMonitor {
    private const val TAG = "NetworkMonitor"
    
    private val _networkAvailable = MutableStateFlow(false)
    val networkAvailable: StateFlow<Boolean> = _networkAvailable.asStateFlow()
    
    private var connectivityManager: ConnectivityManager? = null
    
    /**
     * 初始化网络监听
     */
    fun initialize(context: Context) {
        connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        // 检查当前网络状态
        updateNetworkStatus()
        
        // 注册网络回调
        val networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                Log.d(TAG, "Network available")
                _networkAvailable.value = true
            }
            
            override fun onLost(network: Network) {
                Log.d(TAG, "Network lost")
                updateNetworkStatus()
            }
            
            override fun onCapabilitiesChanged(
                network: Network,
                networkCapabilities: NetworkCapabilities
            ) {
                updateNetworkStatus()
            }
        }
        
        val request = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()
            
        connectivityManager?.registerNetworkCallback(request, networkCallback)
    }
    
    /**
     * 更新当前网络状态
     */
    private fun updateNetworkStatus() {
        val isAvailable = connectivityManager?.activeNetwork?.let { network ->
            connectivityManager?.getNetworkCapabilities(network)?.hasCapability(
                NetworkCapabilities.NET_CAPABILITY_INTERNET
            ) ?: false
        } ?: false
        
        _networkAvailable.value = isAvailable
        Log.d(TAG, "Network status updated: $isAvailable")
    }
}