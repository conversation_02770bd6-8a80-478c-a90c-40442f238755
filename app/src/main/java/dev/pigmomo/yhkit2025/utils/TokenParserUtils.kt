package dev.pigmomo.yhkit2025.utils

import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.data.model.ConfigTokenEntity
import dev.pigmomo.yhkit2025.data.model.LoginTokenEntity
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Token解析工具类
 * 用于从文件或字符串中解析Token数据
 */
object TokenParserUtils {

    /**
     * 解析结果数据类
     * @param tokens 解析出的token列表
     */
    data class ParseResult(
        val tokens: List<Any>
    )

    /**
     * 从文件内容仅解析Token列表，不进行插入操作
     * @param content 文件内容
     * @param tokenType Token类型 (config, login, order)
     * @return 解析结果，包含token列表
     */
    fun parseTokensFromContent(
        content: String,
        tokenType: String = "config"
    ): ParseResult {
        val lines = content.split("\n")
        return parseAccountLines(lines, tokenType)
    }

    /**
     * 解析单行数据
     * @param line 单行数据
     * @param tokenType Token类型 (config, login, order)
     * @return Token实体或null
     */
    fun parseTokenFromLine(line: String, tokenType: String = "config"): Any? {
        if (line.isBlank()) return null

        //去除空格与回车
        val line = line.trim()

        val parts = line.split("----")
        val currentTimeStamp = System.currentTimeMillis()
        val currentDate = DateUtils.getCurrentDateString("MM.dd.yy")

        return when (parts.size) {
            5 -> parseAccountWith5Parts(parts, currentTimeStamp, currentDate, tokenType)
            6 -> parseAccountWith6Parts(parts, currentTimeStamp, currentDate, tokenType)
            else -> null
        }
    }

    /**
     * 仅解析账号行列表，不进行插入操作
     * @param lines 账号行列表
     * @param tokenType Token类型 (config, login, order)
     * @return 解析结果，包含token列表
     */
    private fun parseAccountLines(
        lines: List<String>,
        tokenType: String
    ): ParseResult {
        val tokens = mutableListOf<Any>()

        for (line in lines) {
            val token = parseTokenFromLine(line, tokenType) ?: continue
            tokens.add(token)
        }

        return ParseResult(tokens)
    }

    /**
     * 解析5部分的账号行
     * @param parts 分割后的部分
     * @param currentTimeStamp 当前时间戳
     * @param currentDate 当前日期
     * @param tokenType Token类型 (config, login, order)
     * @return Token实体或null
     */
    private fun parseAccountWith5Parts(
        parts: List<String>,
        currentTimeStamp: Long,
        currentDate: String,
        tokenType: String
    ): Any? {
        val phoneNumber = parts[0]
        val uid = parts[1]
        val userKeyAndAccessToken = parts[2].split("-601933-")

        // 旧格式兼容，检查格式
        if (userKeyAndAccessToken.size != 2) {
            return null
        }

        val userKey = userKeyAndAccessToken[0]
        val accessToken = userKeyAndAccessToken[1]
        val refreshToken = parts[3]
        val appParamAndExpiresIn = parts[4].split(",")

        // 处理appParam和expiresIn
        val (appParam, expiresIn) = processAppParamAndExpiresIn(
            appParamAndExpiresIn,
            currentTimeStamp
        )

        // 根据tokenType返回不同类型的实体
        return when (tokenType) {
            "config" -> ConfigTokenEntity(
                uid = uid,
                phoneNumber = phoneNumber,
                userKey = userKey,
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = expiresIn,
                updateDate = currentDate,
                isNew = true,
                bargainFirst = true,
                activityLimited = false,
                yhCardLimited = false,
                appParam = appParam,
                extraNote = "",
                updateTimestamp = currentTimeStamp
            )

            "login" -> LoginTokenEntity(
                uid = uid,
                phoneNumber = phoneNumber,
                userKey = userKey,
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = expiresIn,
                updateDate = currentDate,
                isNew = true,
                bargainFirst = true,
                activityLimited = false,
                yhCardLimited = false,
                appParam = appParam,
                extraNote = ""
            )

            "order" -> OrderTokenEntity(
                uid = uid,
                phoneNumber = phoneNumber,
                userKey = userKey,
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = expiresIn,
                updateDate = currentDate,
                isNew = true,
                bargainFirst = true,
                activityLimited = false,
                yhCardLimited = false,
                appParam = appParam,
                extraNote = ""
            )

            else -> null
        }
    }

    /**
     * 解析6部分的账号行
     * @param parts 分割后的部分
     * @param currentTimeStamp 当前时间戳
     * @param currentDate 当前日期
     * @param tokenType Token类型 (config, login, order)
     * @return Token实体或null
     */
    private fun parseAccountWith6Parts(
        parts: List<String>,
        currentTimeStamp: Long,
        currentDate: String,
        tokenType: String
    ): Any? {
        val phoneNumber = parts[0]
        val uid = parts[1]
        val userKeyAndAccessToken = parts[2].split("-601933-")

        // 检查格式
        if (userKeyAndAccessToken.size != 2) {
            return null
        }

        val userKey = userKeyAndAccessToken[0]
        val accessToken = userKeyAndAccessToken[1]
        val refreshToken = parts[3]
        val appParamAndExpiresIn = parts[4].split(",")

        // 处理appParam和expiresIn
        val (appParam, expiresIn) = processAppParamAndExpiresIn(
            appParamAndExpiresIn,
            currentTimeStamp
        )

        // 处理updateDate和extraNote
        val (updateDate, extraNote) = processUpdateDateAndExtraNote(parts[5], currentDate)

        // 根据tokenType返回不同类型的实体
        return when (tokenType) {
            "config" -> ConfigTokenEntity(
                uid = uid,
                phoneNumber = phoneNumber,
                userKey = userKey,
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = expiresIn,
                updateDate = updateDate,
                isNew = true,
                bargainFirst = true,
                activityLimited = false,
                yhCardLimited = false,
                appParam = appParam,
                extraNote = extraNote,
                updateTimestamp = currentTimeStamp
            )

            "login" -> LoginTokenEntity(
                uid = uid,
                phoneNumber = phoneNumber,
                userKey = userKey,
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = expiresIn,
                updateDate = updateDate,
                isNew = true,
                bargainFirst = true,
                activityLimited = false,
                yhCardLimited = false,
                appParam = appParam,
                extraNote = extraNote
            )

            "order" -> OrderTokenEntity(
                uid = uid,
                phoneNumber = phoneNumber,
                userKey = userKey,
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = expiresIn,
                updateDate = updateDate,
                isNew = true,
                bargainFirst = true,
                activityLimited = false,
                yhCardLimited = false,
                appParam = appParam,
                extraNote = extraNote
            )

            else -> null
        }
    }

    /**
     * 处理appParam和expiresIn
     * @param appParamAndExpiresIn appParam和expiresIn的分割部分
     * @param currentTimeStamp 当前时间戳
     * @return Pair<String, Long> appParam和expiresIn
     */
    private fun processAppParamAndExpiresIn(
        appParamAndExpiresIn: List<String>,
        currentTimeStamp: Long
    ): Pair<String, Long> {
        return when (appParamAndExpiresIn.size) {
            8 -> {
                // 只有appParam，缺少version和expiresIn
                Pair(
                    appParamAndExpiresIn.joinToString(",") + ",${RequestConfig.AppVersion.VERSION}",
                    currentTimeStamp
                )
            }

            9 -> {
                // 有appParam和version，缺少expiresIn
                Pair(
                    appParamAndExpiresIn.joinToString(","),
                    currentTimeStamp
                )
            }

            10 -> {
                // 有完整参数
                val expIn = appParamAndExpiresIn[9].toLongOrNull()
                    ?: currentTimeStamp
                Pair(
                    // 删除第10个expIn参数
                    appParamAndExpiresIn.subList(0, 9).joinToString(","),
                    expIn
                )
            }

            else -> {
                Pair("", currentTimeStamp)
            }
        }
    }

    /**
     * 处理updateDate和extraNote
     * @param part 第6部分
     * @param currentDate 当前日期
     * @return Pair<String, String> updateDate和extraNote
     */
    private fun processUpdateDateAndExtraNote(
        part: String,
        currentDate: String
    ): Pair<String, String> {
        val dateRegex = Regex("\\d{2}\\.\\d{2}\\.\\d{2}")
        return if (dateRegex.containsMatchIn(part)) {
            // 包含日期格式，分割updateDate和extraNote
            val updateDateAndExtraNote = part.split(" ", limit = 2)
            Pair(
                updateDateAndExtraNote[0],
                if (updateDateAndExtraNote.size > 1) updateDateAndExtraNote[1] else ""
            )
        } else {
            // 不包含日期格式，使用当前日期
            Pair(currentDate, part)
        }
    }
} 