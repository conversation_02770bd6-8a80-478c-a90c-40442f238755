package dev.pigmomo.yhkit2025.utils

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.api.model.boostcoupon.BoostCouponRecord
import dev.pigmomo.yhkit2025.api.model.card.CardItem
import dev.pigmomo.yhkit2025.api.model.cart.CartItem
import dev.pigmomo.yhkit2025.api.model.coupon.CouponListCoupon
import dev.pigmomo.yhkit2025.api.model.credit.CreditData
import dev.pigmomo.yhkit2025.api.model.credit.CreditDetail
import dev.pigmomo.yhkit2025.api.model.invitatinv2.InvitationReward
import dev.pigmomo.yhkit2025.api.model.invitatinv2.SuccessInvite
import dev.pigmomo.yhkit2025.api.model.order.Order
import dev.pigmomo.yhkit2025.api.model.user.AddressItem
import dev.pigmomo.yhkit2025.api.model.user.ShopInfo

/**
 * 账号缓存管理工具类
 * 用于在内存中临时保存多个账号的缓存数据
 */
object AccountCacheManager {
    // 日志标签
    private const val TAG = "AccountCacheManager"
    
    // 内存缓存，使用HashMap存储UID到缓存数据的映射
    private val cacheMap = HashMap<String, AccountCacheData>()
    
    /**
     * 账号缓存数据类
     * 包含需要缓存的所有数据字段
     */
    data class AccountCacheData(
        val requestService: RequestService?,
        val accountDisplayText: String = "",
        val uid: String,
        val phoneNumber: String,
        val addressList: List<AddressItem> = emptyList(),
        val shopList: List<ShopInfo> = emptyList(),
        val cartList: List<CartItem> = emptyList(),
        val addressDisplayText: String = "",
        val shopDisplayText: String = "",
        val cartDisplayText: String = "",
        val selectedAddressItem: AddressItem? = null,
        val currentCartItem: CartItem? = null,
        val accountType: String = "",
        val needLogin: Boolean = false,
        val checkActivityStr: String = "",
        val checkCardBuyStr: String = "",
        val cardList: List<CardItem> = emptyList(),
        val cardBalance: String = "0",
        val couponList: List<CouponListCoupon> = emptyList(),
        val unavailableCouponList: List<CouponListCoupon> = emptyList(),
        val boostCouponList: List<BoostCouponRecord> = emptyList(),
        val orderList: List<Order> = emptyList(),
        val hasNextPage: Boolean = false,
        val lastOrderId: String = "",
        val orderListInfo: String = "",
        val invitationRewards: List<InvitationReward> = emptyList(),
        val successInvites: List<SuccessInvite> = emptyList(),
        val creditData: CreditData? = null,
        val creditDetails: List<CreditDetail> = emptyList(),
        val creditCount: Int = 0,
        val realProxyIp: String = "",
        val phoneLocation: String = "",
        val cacheTimestamp: Long = System.currentTimeMillis()
    )
    
    /**
     * 保存账号缓存数据
     * @param cacheData 要保存的缓存数据
     */
    fun saveAccountCacheData(cacheData: AccountCacheData) {
        try {
            cacheMap[cacheData.uid] = cacheData
            Log.d(TAG, "Saved cache data for account: ${cacheData.uid}, phone: ${cacheData.phoneNumber}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save account cache data: ${e.message}", e)
        }
    }
    
    /**
     * 获取指定账号的缓存数据
     * @param uid 账号UID
     * @return 账号缓存数据，如果不存在则返回null
     */
    fun getAccountCacheData(uid: String): AccountCacheData? {
        return try {
            cacheMap[uid]
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get account cache data: ${e.message}", e)
            null
        }
    }
    
    /**
     * 获取所有账号的缓存数据
     * @return 所有账号的缓存数据映射表 (UID -> 缓存数据)
     */
    fun getAllAccountCacheData(): Map<String, AccountCacheData> {
        return HashMap(cacheMap)
    }
    
    /**
     * 删除指定账号的缓存数据
     * @param uid 账号UID
     * @return 是否删除成功
     */
    fun deleteAccountCacheData(uid: String): Boolean {
        return try {
            cacheMap.remove(uid)
            Log.d(TAG, "Deleted cache data for account: $uid")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to delete account cache data: ${e.message}", e)
            false
        }
    }
    
    /**
     * 清除所有账号的缓存数据
     * @return 是否清除成功
     */
    fun clearAllAccountCacheData(): Boolean {
        return try {
            cacheMap.clear()
            Log.d(TAG, "Cleared all account cache data")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear all account cache data: ${e.message}", e)
            false
        }
    }
    
    /**
     * 获取缓存中的账号数量
     * @return 缓存中的账号数量
     */
    fun getAccountCacheCount(): Int {
        return cacheMap.size
    }
    
    /**
     * 检查指定账号是否有缓存数据
     * @param uid 账号UID
     * @return 是否存在缓存数据
     */
    fun hasAccountCacheData(uid: String): Boolean {
        return cacheMap.containsKey(uid)
    }
    
    /**
     * 更新指定账号的部分缓存数据
     * @param uid 账号UID
     * @param updateBlock 更新操作块，接收当前缓存数据并返回更新后的数据
     * @return 是否更新成功
     */
    fun updateAccountCacheData(
        uid: String, 
        updateBlock: (AccountCacheData) -> AccountCacheData
    ): Boolean {
        try {
            val currentData = getAccountCacheData(uid) ?: return false
            val updatedData = updateBlock(currentData)
            saveAccountCacheData(updatedData)
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update account cache data: ${e.message}", e)
            return false
        }
    }
} 