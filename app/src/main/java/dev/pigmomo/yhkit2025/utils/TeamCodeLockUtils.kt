package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout

/**
 * 积分组队TeamCode锁定工具类
 * 
 * 提供安全的teamCode操作封装，参考BoostCouponLockUtils的实现模式
 */
object TeamCodeLockUtils {
    private const val TAG = "TeamCodeLockUtils"
    
    /**
     * 安全执行积分组队操作，自动处理锁定和释放
     * 
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码
     * @param operation 要执行的操作，接收teamCode信息
     * @return 操作是否成功
     */
    suspend fun safeExecuteTeamCodeOperation(
        context: Context,
        excludePhoneNumber: String? = null,
        operation: suspend (TeamCodeManager.TeamCodeInfo) -> Boolean
    ): Boolean = withContext(Dispatchers.IO) {
        val teamCodeInfo = TeamCodeManager.getAndMarkTeamCode(context, excludePhoneNumber)
        
        if (teamCodeInfo == null) {
            Log.w(TAG, "No team codes available for operation")
            return@withContext false
        }
        
        return@withContext try {
            Log.d(TAG, "Executing operation with team code: ${teamCodeInfo.teamCode}")
            val success = operation(teamCodeInfo)
            
            if (success) {
                Log.d(TAG, "Operation completed successfully with team code: ${teamCodeInfo.teamCode}")
                // 增加使用次数
                TeamCodeManager.incrementTeamCodeUsage(context, teamCodeInfo.teamCode)
            } else {
                Log.w(TAG, "Operation failed with team code: ${teamCodeInfo.teamCode}")
            }
            
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error during team code operation", e)
            false
        } finally {
            // 释放锁定
            TeamCodeManager.releaseTeamCodeLock(teamCodeInfo.teamCode)
            Log.d(TAG, "Released team code lock: ${teamCodeInfo.teamCode}")
        }
    }
    
    /**
     * 带超时的安全执行积分组队操作
     * 
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码
     * @param timeoutMs 超时时间（毫秒）
     * @param operation 要执行的操作
     * @return 操作是否成功
     */
    suspend fun safeExecuteTeamCodeOperationWithTimeout(
        context: Context,
        excludePhoneNumber: String? = null,
        timeoutMs: Long = 60_000L,
        operation: suspend (TeamCodeManager.TeamCodeInfo) -> Boolean
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            withTimeout(timeoutMs) {
                safeExecuteTeamCodeOperation(context, excludePhoneNumber, operation)
            }
        } catch (e: TimeoutCancellationException) {
            Log.e(TAG, "Team code operation timed out after ${timeoutMs}ms", e)
            false
        }
    }
    
    /**
     * 紧急恢复：清理所有锁定并重置状态
     * 
     * @return 清理的锁定数量
     */
    fun emergencyRecovery(): Int {
        Log.w(TAG, "Performing emergency recovery - clearing all locks")
        val clearedCount = TeamCodeManager.forceCleanupAllLocks()
        Log.w(TAG, "Emergency recovery completed - cleared $clearedCount locks")
        return clearedCount
    }
    
    /**
     * 获取系统状态摘要
     * 
     * @param context 上下文
     * @return 状态摘要字符串
     */
    fun getSystemStatusSummary(context: Context): String {
        val (total, soonExpired) = TeamCodeManager.getLockStatistics()
        val allTeamCodes = TeamCodeManager.getAllTeamCodes(context)
        val availableCount = allTeamCodes.count { it.teamStatus == 0 && it.usageCount < 4 }
        val completedCount = allTeamCodes.count { it.teamStatus != 0 }
        
        return buildString {
            appendLine("=== 积分组队系统状态摘要 ===")
            appendLine("当前锁定数: $total")
            appendLine("即将过期数: $soonExpired")
            appendLine("总teamCode数: ${allTeamCodes.size}")
            appendLine("可用teamCode数: $availableCount")
            appendLine("已完成teamCode数: $completedCount")
            appendLine("========================")
        }
    }
    
    /**
     * 执行系统健康检查
     * 
     * @param context 上下文
     * @return 健康检查结果
     */
    fun performHealthCheck(context: Context): HealthCheckResult {
        val issues = mutableListOf<String>()
        val (totalLocks, soonExpired) = TeamCodeManager.getLockStatistics()
        val allTeamCodes = TeamCodeManager.getAllTeamCodes(context)
        val availableCount = allTeamCodes.count { it.teamStatus == 0 && it.usageCount < 4 }
        
        // 检查锁定数量
        if (totalLocks > 10) {
            issues.add("锁定数量过多: $totalLocks")
        }
        
        if (soonExpired > 5) {
            issues.add("即将过期的锁定过多: $soonExpired")
        }
        
        // 检查可用teamCode数量
        if (availableCount == 0) {
            issues.add("没有可用的teamCode")
        } else if (availableCount < 3) {
            issues.add("可用teamCode数量较少: $availableCount")
        }
        
        // 检查使用次数过多的teamCode
        val overusedCount = allTeamCodes.count { it.usageCount >= 4 }
        if (overusedCount > allTeamCodes.size * 0.5) {
            issues.add("过度使用的teamCode比例过高: ${overusedCount}/${allTeamCodes.size}")
        }
        
        val status = when {
            issues.isEmpty() -> "健康"
            issues.size <= 2 -> "警告"
            else -> "异常"
        }
        
        return HealthCheckResult(status, issues)
    }
    
    /**
     * 健康检查结果数据类
     * 
     * @property status 状态（健康/警告/异常）
     * @property issues 问题列表
     */
    data class HealthCheckResult(
        val status: String,
        val issues: List<String>
    )
    
    /**
     * 获取详细的锁定状态信息
     * 
     * @return 锁定状态信息列表
     */
    fun getDetailedLockStatus(): List<TeamCodeManager.LockInfo> {
        return TeamCodeManager.getLockStatusInfo()
    }
    
    /**
     * 强制释放指定的teamCode锁定
     * 
     * @param teamCode 团队代码
     */
    fun forceReleaseTeamCodeLock(teamCode: String) {
        TeamCodeManager.releaseTeamCodeLock(teamCode)
        Log.w(TAG, "Force released team code lock: $teamCode")
    }
    
    /**
     * 强制释放多个teamCode锁定
     * 
     * @param teamCodes 团队代码列表
     */
    fun forceReleaseTeamCodeLocks(teamCodes: List<String>) {
        TeamCodeManager.releaseTeamCodeLocks(teamCodes)
        Log.w(TAG, "Force released ${teamCodes.size} team code locks")
    }
}
