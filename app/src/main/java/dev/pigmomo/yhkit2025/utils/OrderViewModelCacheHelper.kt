package dev.pigmomo.yhkit2025.utils

import dev.pigmomo.yhkit2025.viewmodel.OrderViewModel

/**
 * OrderViewModel缓存助手类
 * 用于实现OrderViewModel中的saveCurrentTokenCacheData方法
 */
object OrderViewModelCacheHelper {

    /**
     * 保存当前账号的缓存数据
     * 在开始加载新Token前，保存当前的账号的缓存数据以便下次加载时快速载入
     *
     * @param viewModel OrderViewModel实例
     * @return 是否保存成功
     */
    fun saveCurrentTokenCacheData(viewModel: OrderViewModel): Bo<PERSON>an {
        // 获取当前选中的账号
        val currentAccount = viewModel.currentSelectedAccount.value ?: return false

        // 创建缓存数据对象
        val cacheData = AccountCacheManager.AccountCacheData(
            requestService = viewModel.requestService,
            accountDisplayText = viewModel.accountDisplayText.value,
            uid = currentAccount.uid,
            phoneNumber = currentAccount.phoneNumber,
            addressList = viewModel.addressList.value,
            shopList = viewModel.shopList.value,
            cartList = viewModel.cartList.value,
            addressDisplayText = viewModel.addressDisplayText.value,
            shopDisplayText = viewModel.shopDisplayText.value,
            cartDisplayText = viewModel.cartDisplayText.value,
            selectedAddressItem = viewModel.selectedAddressItem.value,
            currentCartItem = viewModel.currentCartItem.value,
            accountType = viewModel.accountType.value,
            needLogin = viewModel.needLogin.value,
            checkActivityStr = viewModel.checkActivityStr.value,
            checkCardBuyStr = viewModel.checkCardBuyStr.value,
            cardList = viewModel.cardList.value,
            cardBalance = viewModel.cardBalance.value,
            couponList = viewModel.couponList.value,
            unavailableCouponList = viewModel.unavailableCouponList.value,
            boostCouponList = viewModel.boostCouponList.value,
            orderList = viewModel.orderList.value,
            hasNextPage = viewModel.hasNextPage.value,
            lastOrderId = viewModel.lastOrderId.value,
            orderListInfo = viewModel.orderListInfo.value,
            invitationRewards = viewModel.invitationRewards.value,
            successInvites = viewModel.successInvites.value,
            creditData = viewModel.creditData.value,
            creditDetails = viewModel.creditDetails.value,
            creditCount = viewModel.creditCount.value,
            realProxyIp = viewModel.realProxyIp.value,
            phoneLocation = viewModel.phoneLocation.value
        )

        // 保存缓存数据
        AccountCacheManager.saveAccountCacheData(cacheData)
        return true
    }

    /**
     * 加载指定账号的缓存数据到ViewModel
     *
     * @param viewModel OrderViewModel实例
     * @param uid 账号UID
     * @return 是否加载成功
     */
    fun loadTokenCacheData(viewModel: OrderViewModel, uid: String, phoneNumber: String): Boolean {
        // 获取缓存数据
        val cacheData = AccountCacheManager.getAccountCacheData(uid) ?: return false

        if (cacheData.requestService == null || cacheData.addressList.isEmpty()) {
            return false
        }

        // 将缓存数据应用到ViewModel
        viewModel.apply {

            // 设置请求服务
            setRequestService(cacheData.requestService)

            // 更新UI显示文本
            val accountDisplayText = cacheData.accountDisplayText
            if (accountDisplayText.isNotEmpty() && accountDisplayText != "账号未设定") {
                setAccountDisplayText(accountDisplayText)
            } else {
                setAccountDisplayText(phoneNumber)
            }
            setAddressDisplayText(cacheData.addressDisplayText)
            setShopDisplayText(cacheData.shopDisplayText)
            setCartDisplayText(cacheData.cartDisplayText)

            // 设置地址相关数据
            setAddressList(cacheData.addressList)
            if (cacheData.selectedAddressItem != null) {
                setSelectedAddressItem(cacheData.selectedAddressItem)
            }

            setCurrentCartItem(cacheData.currentCartItem)

            // 设置店铺相关数据
            setShopList(cacheData.shopList)

            // 设置购物车相关数据
            setCartList(cacheData.cartList)

            // 设置账号类型和登录状态
            setAccountType(cacheData.accountType)
            setNeedLogin(cacheData.needLogin)

            // 设置账号检测相关状态
            setCheckActivityStr(cacheData.checkActivityStr)
            setCheckCardBuyStr(cacheData.checkCardBuyStr)

            // 设置永辉卡相关状态
            setCardList(cacheData.cardList)
            setCardBalance(cacheData.cardBalance)

            // 设置优惠券相关状态
            setCouponList(cacheData.couponList)
            setUnavailableCouponList(cacheData.unavailableCouponList)

            // 设置助力券相关状态
            setBoostCouponList(cacheData.boostCouponList)

            // 设置订单列表相关状态
            setOrderList(cacheData.orderList)
            setHasNextPage(cacheData.hasNextPage)
            setLastOrderId(cacheData.lastOrderId)
            setOrderListInfo(cacheData.orderListInfo)

            // 设置邀请相关状态
            setInvitationRewards(cacheData.invitationRewards)
            setSuccessInvites(cacheData.successInvites)

            // 设置积分相关状态
            cacheData.creditData?.let { setCreditData(it) }
            setCreditDetails(cacheData.creditDetails)
            setCreditCount(cacheData.creditCount)

            // 设置IP/归属地相关状态
            setRealProxyIp(cacheData.realProxyIp)
            setPhoneLocation(cacheData.phoneLocation)

            return true
        }
    }

    /**
     * 清除所有账号的缓存数据
     *
     * @return 是否清除成功
     */
    fun clearAllTokenCacheData(): Boolean {
        return AccountCacheManager.clearAllAccountCacheData()
    }
} 