package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*

/**
 * 助力券锁定工具类
 * 
 * 提供便捷的锁定管理和恢复功能
 */
object BoostCouponLockUtils {
    private const val TAG = "BoostCouponLockUtils"
    
    /**
     * 安全执行助力券操作，自动处理锁定和释放
     * 
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码
     * @param maxCount 最大获取数量
     * @param operation 要执行的操作，接收游戏码列表
     * @return 操作是否成功
     */
    suspend fun safeExecuteBoostCouponOperation(
        context: Context,
        excludePhoneNumber: String? = null,
        maxCount: Int = 10,
        operation: suspend (List<GameCodeManager.GameCodeInfo>) -> Boolean
    ): Boolean = withContext(Dispatchers.IO) {
        Log.d(TAG, "Starting safe boost coupon operation, excludePhoneNumber: $excludePhoneNumber, maxCount: $maxCount")

        val gameCodeInfoList = try {
            GameCodeManager.getAndMarkMultipleGameCodes(
                context,
                excludePhoneNumber,
                maxCount
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting and marking game codes", e)
            return@withContext false
        }

        try {
            // 执行操作
            Log.d(TAG, "Executing boost coupon operation...")
            val result = operation(gameCodeInfoList)
            Log.d(TAG, "Boost coupon operation completed with result: $result")
            return@withContext result
        } catch (e: Exception) {
            Log.e(TAG, "Error during boost coupon operation: ${e.javaClass.simpleName} - ${e.message}", e)
            return@withContext false
        } finally {
            // 确保释放所有锁定的游戏码
            try {
                gameCodeInfoList.forEach { gameCodeInfo ->
                    GameCodeManager.releaseGameCode(gameCodeInfo.prizeId, gameCodeInfo.gameCode)
                }
                Log.d(TAG, "Released ${gameCodeInfoList.size} game codes")
            } catch (e: Exception) {
                Log.e(TAG, "Error releasing game codes", e)
            }
        }
    }
    
    /**
     * 带超时的安全执行助力券操作
     * 
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码
     * @param maxCount 最大获取数量
     * @param timeoutMs 超时时间（毫秒）
     * @param operation 要执行的操作
     * @return 操作是否成功
     */
    suspend fun safeExecuteBoostCouponOperationWithTimeout(
        context: Context,
        excludePhoneNumber: String? = null,
        maxCount: Int = 10,
        timeoutMs: Long = 60_000L,
        operation: suspend (List<GameCodeManager.GameCodeInfo>) -> Boolean
    ): Boolean = withContext(Dispatchers.IO) {
        Log.d(TAG, "Starting boost coupon operation with timeout: ${timeoutMs}ms")
        try {
            val result = withTimeout(timeoutMs) {
                safeExecuteBoostCouponOperation(context, excludePhoneNumber, maxCount, operation)
            }
            Log.d(TAG, "Boost coupon operation with timeout completed: $result")
            result
        } catch (e: TimeoutCancellationException) {
            Log.e(TAG, "Boost coupon operation timed out after ${timeoutMs}ms (excludePhoneNumber: $excludePhoneNumber)", e)
            false
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error in boost coupon operation with timeout: ${e.javaClass.simpleName} - ${e.message}", e)
            false
        }
    }
    
    /**
     * 紧急恢复：清理所有锁定并重置状态
     * 
     * @return 清理的锁定数量
     */
    fun emergencyRecovery(): Int {
        Log.w(TAG, "Performing emergency recovery - clearing all locks")
        val clearedCount = GameCodeManager.forceCleanupAllLocks()
        Log.w(TAG, "Emergency recovery completed - cleared $clearedCount locks")
        return clearedCount
    }
    
    /**
     * 检查系统健康状态并执行必要的恢复操作
     * 
     * @param autoRecover 是否自动执行恢复操作
     * @return 健康检查结果
     */
    fun checkAndRecoverSystem(autoRecover: Boolean = false): BoostCouponLockMonitor.HealthCheckResult {
        val healthResult = BoostCouponLockMonitor.performHealthCheck()
        
        Log.i(TAG, "System health check completed: ${healthResult.status}")
        Log.d(TAG, healthResult.getFormattedReport())
        
        if (autoRecover && healthResult.status == BoostCouponLockMonitor.HealthStatus.CRITICAL) {
            Log.w(TAG, "Critical status detected, performing auto-recovery")
            emergencyRecovery()
        }
        
        return healthResult
    }
    
    /**
     * 启动系统监控
     * 
     * @param intervalMs 监控间隔
     * @param enableDetailedLog 是否启用详细日志
     * @param autoRecover 是否启用自动恢复
     */
    fun startSystemMonitoring(
        intervalMs: Long = 30_000L,
        enableDetailedLog: Boolean = false,
        autoRecover: Boolean = false
    ) {
        Log.i(TAG, "Starting system monitoring with auto-recover: $autoRecover")
        
        BoostCouponLockMonitor.startMonitoring(intervalMs, enableDetailedLog)
        
        if (autoRecover) {
            // 启动定期健康检查
            CoroutineScope(Dispatchers.IO).launch {
                while (BoostCouponLockMonitor.isMonitoring()) {
                    delay(intervalMs * 2) // 健康检查频率是监控频率的一半
                    
                    try {
                        checkAndRecoverSystem(true)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error during auto health check", e)
                    }
                }
            }
        }
    }
    
    /**
     * 停止系统监控
     */
    fun stopSystemMonitoring() {
        Log.i(TAG, "Stopping system monitoring")
        BoostCouponLockMonitor.stopMonitoring()
    }
    
    /**
     * 获取系统状态摘要
     * 
     * @return 状态摘要字符串
     */
    fun getSystemStatusSummary(): String {
        val (total, soonExpired) = GameCodeManager.getLockStatistics()
        val isMonitoring = BoostCouponLockMonitor.isMonitoring()
        
        return buildString {
            appendLine("=== 助力券系统状态摘要 ===")
            appendLine("监控状态: ${if (isMonitoring) "运行中" else "已停止"}")
            appendLine("当前锁定数: $total")
            appendLine("即将过期数: $soonExpired")
            
            val healthResult = BoostCouponLockMonitor.performHealthCheck()
            appendLine("系统健康状态: ${healthResult.status}")
            
            if (healthResult.issues.isNotEmpty()) {
                appendLine("主要问题: ${healthResult.issues.first()}")
            }
            
            appendLine("========================")
        }
    }
    
    /**
     * 执行系统诊断
     * 
     * @return 诊断报告
     */
    fun performSystemDiagnostics(): String {
        val report = StringBuilder()
        
        report.appendLine("=== 助力券系统诊断报告 ===")
        report.appendLine("诊断时间: ${System.currentTimeMillis()}")
        report.appendLine()
        
        // 基本状态
        val (total, soonExpired) = GameCodeManager.getLockStatistics()
        report.appendLine("基本状态:")
        report.appendLine("- 总锁定数: $total")
        report.appendLine("- 即将过期数: $soonExpired")
        report.appendLine("- 监控状态: ${if (BoostCouponLockMonitor.isMonitoring()) "运行中" else "已停止"}")
        report.appendLine()
        
        // 健康检查
        val healthResult = BoostCouponLockMonitor.performHealthCheck()
        report.appendLine("健康检查:")
        report.appendLine("- 状态: ${healthResult.status}")
        if (healthResult.issues.isNotEmpty()) {
            report.appendLine("- 问题数: ${healthResult.issues.size}")
            healthResult.issues.forEach { issue ->
                report.appendLine("  * $issue")
            }
        }
        report.appendLine()
        
        // 锁定详情
        if (total > 0) {
            val lockInfoList = GameCodeManager.getLockStatusInfo()
            report.appendLine("锁定详情:")
            lockInfoList.take(5).forEach { lockInfo ->
                val remainingSeconds = lockInfo.remainingTime / 1000
                report.appendLine("- ${lockInfo.key} (剩余: ${remainingSeconds}秒)")
            }
            if (lockInfoList.size > 5) {
                report.appendLine("- ... 还有 ${lockInfoList.size - 5} 个锁定")
            }
        }
        
        report.appendLine("========================")
        return report.toString()
    }
}
