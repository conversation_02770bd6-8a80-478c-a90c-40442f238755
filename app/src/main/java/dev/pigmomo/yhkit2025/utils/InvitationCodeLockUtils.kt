package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout

/**
 * 邀请码锁定工具类
 * 
 * 提供安全的邀请码操作，自动处理锁定和释放
 * 参考BoostCouponLockUtils的实现模式
 */
object InvitationCodeLockUtils {
    private const val TAG = "InvitationCodeLockUtils"
    
    /**
     * 安全执行邀请码操作，自动处理锁定和释放
     * 
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码
     * @param operation 要执行的操作，接收邀请码信息
     * @return 操作是否成功
     */
    suspend fun safeExecuteInvitationCodeOperation(
        context: Context,
        excludePhoneNumber: String? = null,
        operation: suspend (InvitationCodeManager.InvitationCodeInfo) -> Boolean
    ): Boolean = withContext(Dispatchers.IO) {
        val invitationCodeInfo = InvitationCodeManager.getAndMarkInvitationCode(context, excludePhoneNumber)
        
        if (invitationCodeInfo == null) {
            Log.w(TAG, "No invitation codes available for operation")
            return@withContext false
        }
        
        Log.d(TAG, "Acquired invitation code for operation: ${invitationCodeInfo.getInvitationCodeString()}")
        
        try {
            // 执行操作
            val result = operation(invitationCodeInfo)
            Log.d(TAG, "Invitation code operation completed with result: $result")
            
            // 如果操作成功，删除已使用的邀请码
            if (result) {
                val removeSuccess = InvitationCodeManager.removeUsedInvitationCode(context, invitationCodeInfo)
                if (removeSuccess) {
                    Log.d(TAG, "Successfully removed used invitation code: ${invitationCodeInfo.getInvitationCodeString()}")
                } else {
                    Log.w(TAG, "Failed to remove used invitation code: ${invitationCodeInfo.getInvitationCodeString()}")
                }
            }
            
            return@withContext result
        } catch (e: Exception) {
            Log.e(TAG, "Error during invitation code operation", e)
            return@withContext false
        } finally {
            // 确保释放锁定（如果邀请码没有被删除的话）
            InvitationCodeManager.releaseInvitationCodeLock(invitationCodeInfo.getInvitationCodeString())
            Log.d(TAG, "Released invitation code lock: ${invitationCodeInfo.getInvitationCodeString()}")
        }
    }
    
    /**
     * 带超时的安全执行邀请码操作
     * 
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码
     * @param timeoutMs 超时时间（毫秒）
     * @param operation 要执行的操作
     * @return 操作是否成功
     */
    suspend fun safeExecuteInvitationCodeOperationWithTimeout(
        context: Context,
        excludePhoneNumber: String? = null,
        timeoutMs: Long = 60_000L,
        operation: suspend (InvitationCodeManager.InvitationCodeInfo) -> Boolean
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            withTimeout(timeoutMs) {
                safeExecuteInvitationCodeOperation(context, excludePhoneNumber, operation)
            }
        } catch (e: TimeoutCancellationException) {
            Log.e(TAG, "Invitation code operation timed out after ${timeoutMs}ms", e)
            false
        }
    }
}
