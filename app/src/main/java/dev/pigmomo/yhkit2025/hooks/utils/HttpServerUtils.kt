package dev.pigmomo.yhkit2025.hooks.utils

import de.robv.android.xposed.XposedHelpers
import io.ktor.http.*
import io.ktor.server.application.call
import io.ktor.server.engine.*
import io.ktor.server.netty.*
import io.ktor.server.request.receiveText
import io.ktor.server.response.respondText
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.routing
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * HTTP服务器工具类，用于处理本地HTTP服务器相关功能
 */
object HttpServerUtils {
    
    // HTTP服务器实例
    private var httpServer: ApplicationEngine? = null
    
    /**
     * 启动HTTP服务器
     * @param asciiUtilsInstance AsciiUtils实例，用于加密字符串
     * @param httpSecurityClass HttpSecurity类，用于签名参数
     */
    fun launchHttpServer(asciiUtilsInstance: Any?, httpSecurityClass: Class<*>?) {
        CoroutineScope(Dispatchers.IO).launch {
            if (httpServer == null) {
                httpServer = embeddedServer(Netty, port = 8883, host = "localhost") {
                    routing {
                        get("/") {
                            call.respondText("HTTP Server is running")
                        }

                        post("/process") {
                            val message = call.receiveText()
                            val responseMessage = processHttpMessage(
                                message,
                                asciiUtilsInstance,
                                httpSecurityClass
                            )
                            call.respondText(responseMessage, ContentType.Text.Plain)
                        }
                    }
                }.start(wait = false)
                HookLogUtils.info("HTTP server started")
            } else {
                HookLogUtils.debug("HTTP server is already running")
            }
        }
    }
    
    /**
     * 停止HTTP服务器
     */
    fun stopHttpServer() {
        httpServer?.stop(1000, 2000)
        httpServer = null
        HookLogUtils.info("HTTP server stopped")
    }
    
    /**
     * 处理HTTP消息
     * @param message 接收到的消息
     * @param asciiUtilsInstance AsciiUtils实例
     * @param httpSecurityClass HttpSecurity类
     * @return 处理后的响应消息
     */
    private fun processHttpMessage(
        message: String,
        asciiUtilsInstance: Any?,
        httpSecurityClass: Class<*>?
    ): String {
        return when {
            message.contains("XYHBizParams") -> {
                val messageArr = message.split(",")
                val str = messageArr[1]
                val sign = XposedHelpers.callMethod(asciiUtilsInstance, "encryptString", str)
                HookLogUtils.debug("XYHBizParams encrypt result: $sign")
                sign.toString()
            }
            else -> {
                val sign = XposedHelpers.callStaticMethod(
                    httpSecurityClass,
                    "signParams",
                    message
                )
                HookLogUtils.debug("signParams sign result: $sign")
                sign.toString()
            }
        }
    }
    
    /**
     * 检查服务器是否正在运行
     * @return 服务器是否正在运行
     */
    fun isServerRunning(): Boolean {
        return httpServer != null
    }
    
    /**
     * 获取服务器地址
     * @return 服务器地址
     */
    fun getServerAddress(): String {
        return "http://localhost:8883"
    }
} 