package dev.pigmomo.yhkit2025.hooks.config

import de.robv.android.xposed.XSharedPreferences
import dev.pigmomo.yhkit2025.hooks.config.Config
import dev.pigmomo.yhkit2025.hooks.utils.HookLogUtils
import dev.pigmomo.yhkit2025.viewmodel.MainViewModel

/**
 * 配置管理类，负责读取和管理配置信息
 */
object ConfigManager {
    /**
     * 读取配置信息
     */
    fun readConfig(): Config? {
        // 使用XSharedPreferences读取配置
        val prefs = XSharedPreferences("dev.pigmomo.yhkit2025", MainViewModel.PREF_NAME)
        
        // 检查是否能够正常读取
        if (!prefs.file.canRead()) {
            HookLogUtils.error("can't read config file, please enable world readable and lsposed")
            return null
        }
        
        // 确保重新加载最新配置
        prefs.reload()

        // 读取配置
        val uid = prefs.getString(MainViewModel.KEY_UID, "")
        val phoneNumber = prefs.getString(MainViewModel.KEY_PHONE_NUMBER, "")
        val userKey = prefs.getString(MainViewModel.KEY_USER_KEY, "")
        val accessToken = prefs.getString(MainViewModel.KEY_ACCESS_TOKEN, "")
        val refreshToken = prefs.getString(MainViewModel.KEY_REFRESH_TOKEN, "")
        val expiresIn = prefs.getLong(MainViewModel.KEY_EXPIRES_IN, 0)
        val appParam = prefs.getString(MainViewModel.KEY_APP_PARAM, "")
        
        // 检查关键配置是否存在
        if (uid.isNullOrEmpty() || accessToken.isNullOrEmpty()) {
            HookLogUtils.warn("config file is missing key information")
            return null
        }

        // 处理appParam
        val appParamList = appParam?.split(",")
        val channel = appParamList?.getOrNull(0)
        val screen = appParamList?.getOrNull(1)
        val deviceId = appParamList?.getOrNull(2)
        val distinctId = appParamList?.getOrNull(3)
        val osVersion = appParamList?.getOrNull(4)
        val model = appParamList?.getOrNull(5)
        val networkType = appParamList?.getOrNull(6)
        val brand = appParamList?.getOrNull(7)
        val version = appParamList?.getOrNull(8)

        // 打印读取到的配置信息
        HookLogUtils.info("config read successfully")
        HookLogUtils.debug("UID = $uid")
        HookLogUtils.debug("phone number = $phoneNumber")

        return Config(
            uid = uid,
            phoneNumber = phoneNumber!!,
            userKey = userKey!!,
            accessToken = accessToken,
            refreshToken = refreshToken!!,
            expiresIn = expiresIn,
            channel = channel!!,
            screen = screen!!,
            deviceId = deviceId!!,
            distinctId = distinctId!!,
            osVersion = osVersion!!,
            model = model!!,
            networkType = networkType!!,
            brand = brand!!,
            version = version!!
        )
    }
} 