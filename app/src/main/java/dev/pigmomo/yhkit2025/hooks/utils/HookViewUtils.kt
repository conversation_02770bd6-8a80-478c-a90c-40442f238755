package dev.pigmomo.yhkit2025.hooks.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.util.Log
import android.widget.TextView
import android.widget.Toast
import dev.pigmomo.yhkit2025.utils.HttpProxyUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 视图工具类，处理UI相关功能
 */
@SuppressLint("StaticFieldLeak")
object HookViewUtils {

    // 代理配置按钮
    private var proxyConfigButton: TextView? = null
    
    /**
     * 查找代理配置按钮
     * @param activity 当前Activity
     * @return 找到的TextView，如果未找到则返回null
     */
    @SuppressLint("ResourceType")
    fun findProxyConfigButton(activity: Activity): TextView? {
        var textView2023147000: TextView? = null

        try {
            textView2023147000 = activity.findViewById(0x7f091836)
        } catch (e: Exception) {
            HookLogUtils.error("find TextView v11.7.0.5 error: ${e.message}")
        }

        proxyConfigButton = textView2023147000
        return proxyConfigButton
    }
    
    /**
     * 设置代理配置按钮文本
     * @param text 要设置的文本
     */
    fun setProxyButtonText(text: String) {
        proxyConfigButton?.text = text
    }
    
    /**
     * 配置代理按钮
     * @param activity 当前Activity
     */
    @SuppressLint("StaticFieldLeak")
    fun setupProxyButton(
        activity: Activity
    ) {
        val button = findProxyConfigButton(activity) ?: return
        
        // 检查按钮文本，避免重复设置
        val text = button.text
        if (text == "   配置代理   " || text == " 代理已配置 " || text == " 代理已关闭 ") {
            HookLogUtils.debug("proxy button already configured, skip")
            return
        }
        
        // 设置初始文本
        button.text = "   配置代理   "
        
        // 设置点击监听器
        button.setOnClickListener {
            if (HttpProxyUtils.currentProxy == null) {
                // 启用代理
                CoroutineScope(Dispatchers.IO).launch {
                    try {
                        // 代理启用
                        val proxyInfo =
                            HttpProxyUtils.getProxyIp("3").proxyInfo
                        HttpProxyUtils.setupProxy(proxyInfo)
                        
                        withContext(Dispatchers.Main) {
                            if (HttpProxyUtils.currentProxy != null) {
                                button.text = " 代理已配置 "
                                Toast.makeText(
                                    activity,
                                    "代理配置成功 ${HttpProxyUtils.currentProxyInfo}",
                                    Toast.LENGTH_SHORT
                                ).show()
                            } else {
                                Toast.makeText(
                                    activity,
                                    "代理配置未获取成功",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("HookViewUtils", "configure proxy error: ${e.message}", e)
                        withContext(Dispatchers.Main) {
                            Toast.makeText(
                                activity,
                                "配置代理出错: ${e.message}",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                }
            } else {
                // 禁用代理
                CoroutineScope(Dispatchers.IO).launch {
                    try {
                        // 代理禁用
                        HttpProxyUtils.clearProxy()
                        
                        withContext(Dispatchers.Main) {
                            button.text = " 代理已关闭 "
                            Toast.makeText(
                                activity,
                                "代理已关闭",
                                Toast.LENGTH_SHORT
                            ).show()
                        }

                        delay(300)
                        
                        withContext(Dispatchers.Main) {
                            button.text = "   配置代理   "
                        }
                    } catch (e: Exception) {
                        Log.e("HookViewUtils", "close proxy error: ${e.message}", e)
                    }
                }
            }
        }
        
        HookLogUtils.info("proxy button configured successfully")
    }
} 