package dev.pigmomo.yhkit2025.hooks.utils

import de.robv.android.xposed.XposedBridge
import dev.pigmomo.yhkit2025.hooks.MainHook

/**
 * 日志工具类，提供分级日志功能
 */
object HookLogUtils {
    // 日志标签
    private const val LOG_TAG = MainHook.LOG_TAG
    
    // 控制是否输出日志
    var isLogEnabled = true
    
    // 日志级别，值越大级别越高
    enum class LogLevel(val level: Int) {
        DEBUG(0), INFO(1), WARN(2), ERROR(3)
    }
    
    // 当前日志级别，低于此级别的日志不会输出
    var currentLogLevel = LogLevel.WARN
    
    /**
     * 输出调试级别日志
     */
    fun debug(message: String) {
        if (isLogEnabled && currentLogLevel.level <= LogLevel.DEBUG.level) {
            XposedBridge.log("$LOG_TAG [DEBUG]: $message")
        }
    }
    
    /**
     * 输出信息级别日志
     */
    fun info(message: String) {
        if (isLogEnabled && currentLogLevel.level <= LogLevel.INFO.level) {
            XposedBridge.log("$LOG_TAG [INFO]: $message")
        }
    }
    
    /**
     * 输出警告级别日志
     */
    fun warn(message: String) {
        if (isLogEnabled && currentLogLevel.level <= LogLevel.WARN.level) {
            XposedBridge.log("$LOG_TAG [WARN]: $message")
        }
    }
    
    /**
     * 输出错误级别日志
     */
    fun error(message: String) {
        if (isLogEnabled && currentLogLevel.level <= LogLevel.ERROR.level) {
            XposedBridge.log("$LOG_TAG [ERROR]: $message")
        }
    }
} 