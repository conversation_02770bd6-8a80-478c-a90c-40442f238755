package dev.pigmomo.yhkit2025.hooks.utils

import android.content.Context
import android.content.SharedPreferences
import de.robv.android.xposed.XSharedPreferences
import org.luckypray.dexkit.DexKitBridge
import org.luckypray.dexkit.result.ClassData
import org.luckypray.dexkit.result.FieldData
import org.luckypray.dexkit.result.MethodData
import org.luckypray.dexkit.wrap.DexClass
import org.luckypray.dexkit.wrap.DexField
import org.luckypray.dexkit.wrap.DexMethod
import java.lang.reflect.Field
import java.lang.reflect.Method
import androidx.core.content.edit

/**
 * DexKit缓存工具类，用于序列化和反序列化DexKit查询结果
 */
object DexKitCacheUtils {
    // 缓存文件名
    private const val CACHE_FILE_NAME = "dexkit_cache"
    
    // 缓存版本，用于在应用更新时清除缓存
    private const val CACHE_VERSION = 1
    
    // 缓存键前缀
    private const val KEY_PREFIX_CLASS = "class_"
    private const val KEY_PREFIX_METHOD = "method_"
    private const val KEY_PREFIX_FIELD = "field_"
    
    /**
     * 保存类数据到缓存
     * @param context 上下文
     * @param key 缓存键
     * @param classData 类数据
     * @param appVersionCode 应用版本号
     */
    fun saveClassData(context: Context, key: String, classData: ClassData, appVersionCode: Int) {
        try {
            val dexClass = classData.toDexType()
            val serialized = dexClass.serialize()
            
            val prefs = context.getSharedPreferences(CACHE_FILE_NAME, Context.MODE_PRIVATE)
            prefs.edit {
                putString("${KEY_PREFIX_CLASS}${key}_$appVersionCode", serialized)
                    .putInt("cache_version", CACHE_VERSION)
            }
            
            HookLogUtils.debug("Saved class data for key: $key, version: $appVersionCode")
        } catch (e: Exception) {
            HookLogUtils.error("Failed to save class data: ${e.message}")
        }
    }
    
    /**
     * 保存方法数据到缓存
     * @param context 上下文
     * @param key 缓存键
     * @param methodData 方法数据
     * @param appVersionCode 应用版本号
     */
    fun saveMethodData(context: Context, key: String, methodData: MethodData, appVersionCode: Int) {
        try {
            val dexMethod = methodData.toDexMethod()
            val serialized = dexMethod.serialize()
            
            val prefs = context.getSharedPreferences(CACHE_FILE_NAME, Context.MODE_PRIVATE)
            prefs.edit {
                putString("${KEY_PREFIX_METHOD}${key}_$appVersionCode", serialized)
                    .putInt("cache_version", CACHE_VERSION)
            }
            
            HookLogUtils.debug("Saved method data for key: $key, version: $appVersionCode")
        } catch (e: Exception) {
            HookLogUtils.error("Failed to save method data: ${e.message}")
        }
    }
    
    /**
     * 保存字段数据到缓存
     * @param context 上下文
     * @param key 缓存键
     * @param fieldData 字段数据
     * @param appVersionCode 应用版本号
     */
    fun saveFieldData(context: Context, key: String, fieldData: FieldData, appVersionCode: Int) {
        try {
            val dexField = fieldData.toDexField()
            val serialized = dexField.serialize()
            
            val prefs = context.getSharedPreferences(CACHE_FILE_NAME, Context.MODE_PRIVATE)
            prefs.edit {
                putString("${KEY_PREFIX_FIELD}${key}_$appVersionCode", serialized)
                    .putInt("cache_version", CACHE_VERSION)
            }
            
            HookLogUtils.debug("Saved field data for key: $key, version: $appVersionCode")
        } catch (e: Exception) {
            HookLogUtils.error("Failed to save field data: ${e.message}")
        }
    }
    
    /**
     * 保存多个类数据到缓存
     * @param context 上下文
     * @param key 缓存键
     * @param classDataList 类数据列表
     * @param appVersionCode 应用版本号
     */
    fun saveClassDataList(context: Context, key: String, classDataList: List<ClassData>, appVersionCode: Int) {
        try {
            val prefs = context.getSharedPreferences(CACHE_FILE_NAME, Context.MODE_PRIVATE)
            prefs.edit {

                // 保存列表大小
                putInt("${KEY_PREFIX_CLASS}${key}_size_$appVersionCode", classDataList.size)

                // 保存每个类数据
                classDataList.forEachIndexed { index, classData ->
                    val dexClass = classData.toDexType()
                    val serialized = dexClass.serialize()
                    putString("${KEY_PREFIX_CLASS}${key}_${index}_$appVersionCode", serialized)
                }

                putInt("cache_version", CACHE_VERSION)
            }
            
            HookLogUtils.debug("Saved ${classDataList.size} class data items for key: $key, version: $appVersionCode")
        } catch (e: Exception) {
            HookLogUtils.error("Failed to save class data list: ${e.message}")
        }
    }
    
    /**
     * 保存多个方法数据到缓存
     * @param context 上下文
     * @param key 缓存键
     * @param methodDataList 方法数据列表
     * @param appVersionCode 应用版本号
     */
    fun saveMethodDataList(context: Context, key: String, methodDataList: List<MethodData>, appVersionCode: Int) {
        try {
            val prefs = context.getSharedPreferences(CACHE_FILE_NAME, Context.MODE_PRIVATE)
            prefs.edit {

                // 保存列表大小
                putInt("${KEY_PREFIX_METHOD}${key}_size_$appVersionCode", methodDataList.size)

                // 保存每个方法数据
                methodDataList.forEachIndexed { index, methodData ->
                    val dexMethod = methodData.toDexMethod()
                    val serialized = dexMethod.serialize()
                    putString("${KEY_PREFIX_METHOD}${key}_${index}_$appVersionCode", serialized)
                }

                putInt("cache_version", CACHE_VERSION)
            }
            
            HookLogUtils.debug("Saved ${methodDataList.size} method data items for key: $key, version: $appVersionCode")
        } catch (e: Exception) {
            HookLogUtils.error("Failed to save method data list: ${e.message}")
        }
    }
    
    /**
     * 从缓存读取类实例
     * @param context 上下文
     * @param key 缓存键
     * @param classLoader 类加载器
     * @param appVersionCode 应用版本号
     * @return 类实例，如果缓存不存在则返回null
     */
    fun loadClass(context: Context, key: String, classLoader: ClassLoader, appVersionCode: Int): Class<*>? {
        try {
            val prefs = context.getSharedPreferences(CACHE_FILE_NAME, Context.MODE_PRIVATE)
            val serialized = prefs.getString("${KEY_PREFIX_CLASS}${key}_$appVersionCode", null) ?: return null
            
            val dexClass = DexClass(serialized)
            return dexClass.getInstance(classLoader)
        } catch (e: Exception) {
            HookLogUtils.error("Failed to load class data: ${e.message}")
            return null
        }
    }
    
    /**
     * 从缓存读取方法实例
     * @param context 上下文
     * @param key 缓存键
     * @param classLoader 类加载器
     * @param appVersionCode 应用版本号
     * @return 方法实例，如果缓存不存在则返回null
     */
    fun loadMethod(context: Context, key: String, classLoader: ClassLoader, appVersionCode: Int): Method? {
        try {
            val prefs = context.getSharedPreferences(CACHE_FILE_NAME, Context.MODE_PRIVATE)
            val serialized = prefs.getString("${KEY_PREFIX_METHOD}${key}_$appVersionCode", null) ?: return null
            
            val dexMethod = DexMethod(serialized)
            return dexMethod.getMethodInstance(classLoader)
        } catch (e: Exception) {
            HookLogUtils.error("Failed to load method data: ${e.message}")
            return null
        }
    }
    
    /**
     * 从缓存读取字段实例
     * @param context 上下文
     * @param key 缓存键
     * @param classLoader 类加载器
     * @param appVersionCode 应用版本号
     * @return 字段实例，如果缓存不存在则返回null
     */
    fun loadField(context: Context, key: String, classLoader: ClassLoader, appVersionCode: Int): Field? {
        try {
            val prefs = context.getSharedPreferences(CACHE_FILE_NAME, Context.MODE_PRIVATE)
            val serialized = prefs.getString("${KEY_PREFIX_FIELD}${key}_$appVersionCode", null) ?: return null
            
            val dexField = DexField(serialized)
            return dexField.getFieldInstance(classLoader)
        } catch (e: Exception) {
            HookLogUtils.error("Failed to load field data: ${e.message}")
            return null
        }
    }
    
    /**
     * 从缓存读取类实例列表
     * @param context 上下文
     * @param key 缓存键
     * @param classLoader 类加载器
     * @param appVersionCode 应用版本号
     * @return 类实例列表，如果缓存不存在则返回空列表
     */
    fun loadClassList(context: Context, key: String, classLoader: ClassLoader, appVersionCode: Int): List<Class<*>> {
        try {
            val prefs = context.getSharedPreferences(CACHE_FILE_NAME, Context.MODE_PRIVATE)
            val size = prefs.getInt("${KEY_PREFIX_CLASS}${key}_size_$appVersionCode", -1)
            
            if (size == -1) {
                return emptyList()
            }
            
            val result = mutableListOf<Class<*>>()
            for (i in 0 until size) {
                val serialized = prefs.getString("${KEY_PREFIX_CLASS}${key}_${i}_$appVersionCode", null) ?: continue
                val dexClass = DexClass(serialized)
                val clazz = dexClass.getInstance(classLoader)
                result.add(clazz)
            }
            
            HookLogUtils.debug("Loaded ${result.size} class data items for key: $key, version: $appVersionCode")
            return result
        } catch (e: Exception) {
            HookLogUtils.error("Failed to load class data list: ${e.message}")
            return emptyList()
        }
    }
    
    /**
     * 从缓存读取方法实例列表
     * @param context 上下文
     * @param key 缓存键
     * @param classLoader 类加载器
     * @param appVersionCode 应用版本号
     * @return 方法实例列表，如果缓存不存在则返回空列表
     */
    fun loadMethodList(context: Context, key: String, classLoader: ClassLoader, appVersionCode: Int): List<Method> {
        try {
            val prefs = context.getSharedPreferences(CACHE_FILE_NAME, Context.MODE_PRIVATE)
            val size = prefs.getInt("${KEY_PREFIX_METHOD}${key}_size_$appVersionCode", -1)
            
            if (size == -1) {
                return emptyList()
            }
            
            val result = mutableListOf<Method>()
            for (i in 0 until size) {
                val serialized = prefs.getString("${KEY_PREFIX_METHOD}${key}_${i}_$appVersionCode", null) ?: continue
                val dexMethod = DexMethod(serialized)
                val method = dexMethod.getMethodInstance(classLoader)
                result.add(method)
            }
            
            HookLogUtils.debug("Loaded ${result.size} method data items for key: $key, version: $appVersionCode")
            return result
        } catch (e: Exception) {
            HookLogUtils.error("Failed to load method data list: ${e.message}")
            return emptyList()
        }
    }
    
    /**
     * 从Xposed环境中读取缓存
     * @param packageName 包名
     * @param key 缓存键
     * @param classLoader 类加载器
     * @param appVersionCode 应用版本号
     * @return 类实例，如果缓存不存在则返回null
     */
    fun loadClassFromXposed(packageName: String, key: String, classLoader: ClassLoader, appVersionCode: Int): Class<*>? {
        try {
            val prefs = XSharedPreferences(packageName, CACHE_FILE_NAME)
            if (!prefs.file.canRead()) {
                HookLogUtils.warn("Cannot read XSharedPreferences file")
                return null
            }
            
            val serialized = prefs.getString("${KEY_PREFIX_CLASS}${key}_$appVersionCode", null) ?: return null
            
            val dexClass = DexClass(serialized)
            return dexClass.getInstance(classLoader)
        } catch (e: Exception) {
            HookLogUtils.error("Failed to load class data from Xposed: ${e.message}")
            return null
        }
    }
    
    /**
     * 从Xposed环境中读取方法实例
     * @param packageName 包名
     * @param key 缓存键
     * @param classLoader 类加载器
     * @param appVersionCode 应用版本号
     * @return 方法实例，如果缓存不存在则返回null
     */
    fun loadMethodFromXposed(packageName: String, key: String, classLoader: ClassLoader, appVersionCode: Int): Method? {
        try {
            val prefs = XSharedPreferences(packageName, CACHE_FILE_NAME)
            if (!prefs.file.canRead()) {
                HookLogUtils.warn("Cannot read XSharedPreferences file")
                return null
            }
            
            val serialized = prefs.getString("${KEY_PREFIX_METHOD}${key}_$appVersionCode", null) ?: return null
            
            val dexMethod = DexMethod(serialized)
            return dexMethod.getMethodInstance(classLoader)
        } catch (e: Exception) {
            HookLogUtils.error("Failed to load method data from Xposed: ${e.message}")
            return null
        }
    }
    
    /**
     * 从Xposed环境中读取类实例列表
     * @param packageName 包名
     * @param key 缓存键
     * @param classLoader 类加载器
     * @param appVersionCode 应用版本号
     * @return 类实例列表，如果缓存不存在则返回空列表
     */
    fun loadClassListFromXposed(packageName: String, key: String, classLoader: ClassLoader, appVersionCode: Int): List<Class<*>> {
        try {
            val prefs = XSharedPreferences(packageName, CACHE_FILE_NAME)
            if (!prefs.file.canRead()) {
                HookLogUtils.warn("Cannot read XSharedPreferences file")
                return emptyList()
            }
            
            val size = prefs.getInt("${KEY_PREFIX_CLASS}${key}_size_$appVersionCode", -1)
            
            if (size == -1) {
                return emptyList()
            }
            
            val result = mutableListOf<Class<*>>()
            for (i in 0 until size) {
                val serialized = prefs.getString("${KEY_PREFIX_CLASS}${key}_${i}_$appVersionCode", null) ?: continue
                val dexClass = DexClass(serialized)
                val clazz = dexClass.getInstance(classLoader)
                result.add(clazz)
            }
            
            HookLogUtils.debug("Loaded ${result.size} class data items from Xposed for key: $key, version: $appVersionCode")
            return result
        } catch (e: Exception) {
            HookLogUtils.error("Failed to load class data list from Xposed: ${e.message}")
            return emptyList()
        }
    }
    
    /**
     * 从Xposed环境中读取方法实例列表
     * @param packageName 包名
     * @param key 缓存键
     * @param classLoader 类加载器
     * @param appVersionCode 应用版本号
     * @return 方法实例列表，如果缓存不存在则返回空列表
     */
    fun loadMethodListFromXposed(packageName: String, key: String, classLoader: ClassLoader, appVersionCode: Int): List<Method> {
        try {
            val prefs = XSharedPreferences(packageName, CACHE_FILE_NAME)
            if (!prefs.file.canRead()) {
                HookLogUtils.warn("Cannot read XSharedPreferences file")
                return emptyList()
            }
            
            val size = prefs.getInt("${KEY_PREFIX_METHOD}${key}_size_$appVersionCode", -1)
            
            if (size == -1) {
                return emptyList()
            }
            
            val result = mutableListOf<Method>()
            for (i in 0 until size) {
                val serialized = prefs.getString("${KEY_PREFIX_METHOD}${key}_${i}_$appVersionCode", null) ?: continue
                val dexMethod = DexMethod(serialized)
                val method = dexMethod.getMethodInstance(classLoader)
                result.add(method)
            }
            
            HookLogUtils.debug("Loaded ${result.size} method data items from Xposed for key: $key, version: $appVersionCode")
            return result
        } catch (e: Exception) {
            HookLogUtils.error("Failed to load method data list from Xposed: ${e.message}")
            return emptyList()
        }
    }

    fun clearCache(context: Context, key: String) {
        try {
            val prefs = context.getSharedPreferences(CACHE_FILE_NAME, Context.MODE_PRIVATE)
            prefs.edit {
                prefs.all.keys.filter { it.contains(key) }.forEach { key ->
                    remove(key)
                }
            }
            HookLogUtils.info("Cleared cache for key: $key")
        } catch (e: Exception) {
            HookLogUtils.error("Failed to clear cache: ${e.message}")
        }
    }
    
    /**
     * 清除指定版本的缓存
     * @param context 上下文
     * @param appVersionCode 应用版本号
     */
    fun clearCache(context: Context, appVersionCode: Int) {
        try {
            val prefs = context.getSharedPreferences(CACHE_FILE_NAME, Context.MODE_PRIVATE)
            prefs.edit {

                // 删除所有包含版本号的键
                prefs.all.keys.filter { it.endsWith("_$appVersionCode") }.forEach { key ->
                    remove(key)
                }

            }
            HookLogUtils.info("Cleared cache for version: $appVersionCode")
        } catch (e: Exception) {
            HookLogUtils.error("Failed to clear cache: ${e.message}")
        }
    }
    
    /**
     * 清除所有缓存
     * @param context 上下文
     */
    fun clearAllCache(context: Context) {
        try {
            val prefs = context.getSharedPreferences(CACHE_FILE_NAME, Context.MODE_PRIVATE)
            prefs.edit { clear() }
            HookLogUtils.info("Cleared all cache")
        } catch (e: Exception) {
            HookLogUtils.error("Failed to clear all cache: ${e.message}")
        }
    }
} 