package dev.pigmomo.yhkit2025.hooks.config

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import android.widget.Toast
import androidx.core.content.edit
import dev.pigmomo.yhkit2025.data.model.ConfigTokenEntity
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.viewmodel.MainViewModel

/**
 * XSharedPreferences写入工具类
 * 负责将配置写入到XSharedPreferences中
 */
object XPrefsWriter {
    /**
     * 使用XSharedPreferences写入配置
     * @param context 上下文
     * @param configToken 配置令牌实体
     * @return 是否写入成功
     */
    @SuppressLint("WorldReadableFiles")
    fun writeConfig(context: Context, configToken: ConfigTokenEntity): Boolean {
        try {
            // 使用应用包名创建SharedPreferences
            val prefs =
                context.getSharedPreferences(MainViewModel.PREF_NAME, Context.MODE_WORLD_READABLE)

            prefs.edit {
                putString(MainViewModel.KEY_UID, configToken.uid)
                putString(MainViewModel.KEY_PHONE_NUMBER, configToken.phoneNumber)
                putString(MainViewModel.KEY_USER_KEY, configToken.userKey)
                putString(MainViewModel.KEY_ACCESS_TOKEN, configToken.accessToken)
                putString(MainViewModel.KEY_REFRESH_TOKEN, configToken.refreshToken)
                putLong(MainViewModel.KEY_EXPIRES_IN, configToken.expiresIn)
                putString(MainViewModel.KEY_APP_PARAM, configToken.appParam)
            }

            Log.d("XPrefsWriter", "config written to XSharedPreferences successfully: ${configToken.uid}")
            Toast.makeText(context, "配置已载入", Toast.LENGTH_SHORT).show()
            return true
        } catch (e: Exception) {
            Log.e("XPrefsWriter", "write config to XSharedPreferences failed: ${e.message}")
            Toast.makeText(context, "模块未初始化", Toast.LENGTH_SHORT).show()
            return false
        }
    }

    /**
     * 使用XSharedPreferences写入配置
     * @param context 上下文
     * @param orderToken 配置令牌实体
     * @return 是否写入成功
     */
    @SuppressLint("WorldReadableFiles")
    fun writeConfig(context: Context, orderToken: OrderTokenEntity): Boolean {
        try {
            // 使用应用包名创建SharedPreferences
            val prefs =
                context.getSharedPreferences(MainViewModel.PREF_NAME, Context.MODE_WORLD_READABLE)

            prefs.edit {
                putString(MainViewModel.KEY_UID, orderToken.uid)
                putString(MainViewModel.KEY_PHONE_NUMBER, orderToken.phoneNumber)
                putString(MainViewModel.KEY_USER_KEY, orderToken.userKey)
                putString(MainViewModel.KEY_ACCESS_TOKEN, orderToken.accessToken)
                putString(MainViewModel.KEY_REFRESH_TOKEN, orderToken.refreshToken)
                putLong(MainViewModel.KEY_EXPIRES_IN, orderToken.expiresIn)
                putString(MainViewModel.KEY_APP_PARAM, orderToken.appParam)
            }

            Log.d("XPrefsWriter", "config written to XSharedPreferences successfully: ${orderToken.uid}")
            Toast.makeText(context, "配置已载入", Toast.LENGTH_SHORT).show()
            return true
        } catch (e: Exception) {
            Log.e("XPrefsWriter", "write config to XSharedPreferences failed: ${e.message}")
            Toast.makeText(context, "模块未初始化", Toast.LENGTH_SHORT).show()
            return false
        }
    }

    /**
     * 从XSharedPreferences中删除配置
     * @param context 上下文
     * @param uid 用户ID
     * @return 是否删除成功
     */
    @SuppressLint("WorldReadableFiles")
    fun deleteConfig(context: Context, uid: String): Boolean {
        try {
            val prefs =
                context.getSharedPreferences(MainViewModel.PREF_NAME, Context.MODE_WORLD_READABLE)
            val currentUid = prefs.getString(MainViewModel.KEY_UID, null)

            // 只有当当前保存的uid与要删除的uid匹配时才删除
            if (currentUid == uid) {
                prefs.edit {
                    clear()
                }
                Log.d("XPrefsWriter", "config deleted from XSharedPreferences: $uid")
                Toast.makeText(context, "config deleted", Toast.LENGTH_SHORT).show()
                return true
            }
            return false
        } catch (e: Exception) {
            Log.e("XPrefsWriter", "delete config from XSharedPreferences failed: ${e.message}")
            return false
        }
    }
} 