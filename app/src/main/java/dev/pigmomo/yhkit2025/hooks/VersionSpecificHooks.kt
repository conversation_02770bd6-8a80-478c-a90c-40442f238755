package dev.pigmomo.yhkit2025.hooks

import android.content.Context
import android.widget.Toast
import de.robv.android.xposed.XC_MethodHook
import de.robv.android.xposed.XposedBridge
import dev.pigmomo.yhkit2025.hooks.utils.DexKitCacheUtils
import dev.pigmomo.yhkit2025.hooks.utils.DexKitUtils
import dev.pigmomo.yhkit2025.hooks.utils.HookCommonUtils
import dev.pigmomo.yhkit2025.hooks.utils.HookLogUtils
import org.json.JSONObject
import org.luckypray.dexkit.query.FindClass
import org.luckypray.dexkit.query.matchers.ClassMatcher
import org.luckypray.dexkit.result.ClassData
import java.io.InputStream
import java.lang.reflect.Modifier

/**
 * 处理特定版本Hook
 */
object VersionSpecificHooks {

    /**
     * 处理特定版本Hook
     * @param classLoader 类加载器
     * @param context 上下文
     * @param versionCode 版本号
     */
    fun hookByVersion(
        classLoader: ClassLoader,
        context: Context,
        versionCode: Int,
    ) {
        HookLogUtils.info("start hook version $versionCode specific methods")

        // 初始化DexKit
        if (!DexKitUtils.init(context.classLoader, true)) {
            HookLogUtils.error("Failed to initialize DexKit, falling back to direct class loading")
            hookWithoutDexKit(classLoader, context, versionCode)
            return
        }

        // 使用DexKit进行Hook
        try {
            // 使用共享的UUID作为jysessionid
            val uuid = MainHook.SHARED_UUID

            // 分别执行各个hook
            hookSauronReportWithDexKit(classLoader, context, uuid, versionCode)
            hookOrderDetailActivityWithDexKit(classLoader, context, versionCode)
            hookOrderModelsWithDexKit(classLoader, context, uuid, versionCode)

            // 完成后释放DexKit资源
            DexKitUtils.release()
        } catch (e: Exception) {
            HookLogUtils.error("Error using DexKit: ${e.message}")
            // 出错时回退到直接加载类的方式
            hookWithoutDexKit(classLoader, context, versionCode)
        }
    }

    /**
     * 不使用DexKit的备用Hook方法
     */
    private fun hookWithoutDexKit(
        classLoader: ClassLoader,
        context: Context,
        versionCode: Int
    ) {
        HookLogUtils.info("Falling back to direct class loading hook methods")

        // 分别执行各个hook
        hookSauronReport(classLoader, context, versionCode)
        hookOrderDetailActivity(classLoader, context, versionCode)
        hookOrderModels(classLoader, context, versionCode)
    }

    private fun hookSauronReport(
        classLoader: ClassLoader,
        context: Context,
        versionCode: Int
    ) {
        HookLogUtils.info("start hook sauron-report version $versionCode specific methods")

        // 使用共享的UUID作为jysessionid
        val uuid = MainHook.SHARED_UUID

        when (versionCode) {
            2023144000 -> {
                // 分别执行各个hook
                hookSauronReport202314400(classLoader, uuid)
            }

            else -> {
                Toast.makeText(context, "SauronReport hook failed", Toast.LENGTH_SHORT).show()
                HookLogUtils.error("Unsupported version: $versionCode")
            }
        }
    }

    private fun hookOrderDetailActivity(
        classLoader: ClassLoader,
        context: Context,
        versionCode: Int
    ) {
        HookLogUtils.info("start hook order-detail-activity version $versionCode specific methods")

        when (versionCode) {
            2023144000 -> {
                // 分别执行各个hook
                hookOrderDetailActivity202314400(classLoader, context)
            }

            else -> {
                Toast.makeText(context, "OrderDetailActivity hook failed", Toast.LENGTH_SHORT)
                    .show()
                HookLogUtils.error("Unsupported version: $versionCode")
            }
        }
    }

    private fun hookOrderModels(
        classLoader: ClassLoader,
        context: Context,
        versionCode: Int
    ) {
        HookLogUtils.info("start hook order-models version $versionCode specific methods")

        // 使用共享的UUID作为jysessionid
        val uuid = MainHook.SHARED_UUID

        when (versionCode) {
            2023144000 -> {
                // 分别执行各个hook
                hookOrderModels202314400(classLoader, uuid)
            }

            else -> {
                Toast.makeText(context, "OrderModels hook failed", Toast.LENGTH_SHORT).show()
                HookLogUtils.error("Unsupported version: $versionCode")
            }
        }
    }

    /**
     * 使用DexKit查找并Hook sauron-report类
     */
    private fun hookSauronReportWithDexKit(
        classLoader: ClassLoader,
        context: Context,
        uuid: String,
        versionCode: Int
    ) {
        val cacheKey = "sauron_report_methods"
        var hookSuccess = false

        // 1. 尝试从缓存加载方法
        var targetMethods =
            DexKitCacheUtils.loadMethodList(context, cacheKey, classLoader, versionCode)

        if (targetMethods.isNotEmpty()) {
            HookLogUtils.info("Loaded sauron-report methods from cache")
        } else {
            // 2. 缓存未命中，使用DexKit查找
            val sauronReportClass = DexKitUtils.use { bridge ->
                // 查找包含特定静态字段的类
                val matchedClasses = bridge.findClass(
                    FindClass.create().matcher {
                        fields {
                            // 匹配 public static final String v
                            add {
                                modifiers = Modifier.PUBLIC or Modifier.STATIC or Modifier.FINAL
                                type = "java.lang.String"
                                name = "v"
                            }
                            // 匹配 public static final String w
                            add {
                                modifiers = Modifier.PUBLIC or Modifier.STATIC or Modifier.FINAL
                                type = "java.lang.String"
                                name = "w"
                            }
                        }
                    }
                ).toList()

                // 筛选，要求 v 字段的值为指定 URL
                matchedClasses.firstOrNull { classData ->
                    try {
                        // 查找 v 字段
                        val fieldDataList = classData.fields.findField {
                            matcher {
                                modifiers = Modifier.PUBLIC or Modifier.STATIC or Modifier.FINAL
                                type = "java.lang.String"
                                name = "v"
                            }
                        }
                        // 检查字段值是否为目标 URL
                        fieldDataList.any { fieldData ->
                            val field = fieldData.getFieldInstance(classLoader)
                            field.isAccessible = true
                            val value = field.get(null)
                            if (value == "https://sauron-report.yonghuivip.com/collect/gzip") {
                                HookLogUtils.info("Matched class: ${classData.name}, field v value: $value")
                                true
                            } else {
                                false
                            }
                        }
                    } catch (e: Exception) {
                        HookLogUtils.error("Error checking class: ${classData.name}, error: ${e.message}")
                        false
                    }
                }
            }

            HookLogUtils.info("sauronReportClass: $sauronReportClass")

            if (sauronReportClass == null) {
                HookLogUtils.warn("No sauron-report class found with DexKit, falling back to direct method")
                hookSauronReport(classLoader, context, versionCode)
                return
            }

            // 查找返回JSONObject的方法
            val foundMethodsData = sauronReportClass.findMethod {
                matcher {
                    returnType("org.json.JSONObject")
                }
            }

            if (foundMethodsData.isNotEmpty()) {
                // 3. 保存到缓存
                DexKitCacheUtils.saveMethodDataList(
                    context,
                    cacheKey,
                    foundMethodsData,
                    versionCode
                )
                targetMethods = foundMethodsData.map { it.getMethodInstance(classLoader) }
            }
        }

        if (targetMethods.isEmpty()) {
            // 如果通过方法特征找不到，则回退到直接方法
            HookLogUtils.warn("No sauron-report methods found with DexKit, falling back to direct method")
            hookSauronReport(classLoader, context, versionCode)
            return
        }

        // 使用找到的方法进行Hook
        for (method in targetMethods) {
            try {
                XposedBridge.hookMethod(
                    method,
                    object : XC_MethodHook() {
                        override fun afterHookedMethod(param: MethodHookParam) {
                            // 获取最新配置
                            val config = MainHook.getCurrentConfig() ?: return

                            val result = param.result as? JSONObject ?: return
                            result.put("device_id", config.deviceId)
                            result.put("mobile_name", config.brand.replace(" ", ""))
                            result.put("os_version", config.osVersion)
                            result.put("sessionId", uuid)
                            param.result = result
                            HookLogUtils.info("hook ${method.declaringClass.name}.${method.name}() success with DexKit")
                        }
                    }
                )
                hookSuccess = true
            } catch (e: Exception) {
                HookLogUtils.debug("Failed to hook method ${method.name}: ${e.message}")
            }
        }

        if (!hookSuccess) {
            // 如果没有成功Hook，回退到直接方法
            HookLogUtils.warn("Failed to hook sauron-report with DexKit, falling back to direct method")
            hookSauronReport(classLoader, context, versionCode)
        }
    }

    /**
     * 使用DexKit查找并Hook订单详情页
     */
    private fun hookOrderDetailActivityWithDexKit(
        classLoader: ClassLoader,
        context: Context,
        versionCode: Int
    ) {
        val cacheKey = "order_detail_activity_methods"
        var hookSuccess = false

        // 1. 尝试从缓存加载方法
        var targetMethods =
            DexKitCacheUtils.loadMethodList(context, cacheKey, classLoader, versionCode)

        if (targetMethods.isNotEmpty()) {
            HookLogUtils.info("Loaded OrderDetailActivity methods from cache")
        } else {
            // 2. 缓存未命中，使用DexKit查找
            val orderDetailClass = DexKitUtils.use { bridge ->
                bridge.findClass(
                    FindClass.create()
                        .matcher {
                            className("cn.yonghui.hyd.order.detail.OrderDetailActivity")
                        }
                )
            }

            HookLogUtils.info("orderDetailClass: $orderDetailClass")

            if (orderDetailClass == null) {
                HookLogUtils.warn("OrderDetailActivity not found with DexKit, falling back to direct method")
                hookOrderDetailActivity(classLoader, context, versionCode)
                return
            }

            val foundMethodsData = orderDetailClass.findMethod {
                matcher {
                    paramTypes("cn.yonghui.hyd.order.detail.OrderDetailActivity")
                    returnType("cn.yonghui.hyd.order.base.RecommendBean")
                }
            }

            if (foundMethodsData.isNotEmpty()) {
                // 3. 保存到缓存
                DexKitCacheUtils.saveMethodDataList(
                    context,
                    cacheKey,
                    foundMethodsData,
                    versionCode
                )
                targetMethods = foundMethodsData.map { it.getMethodInstance(classLoader) }
            }
        }

        if (targetMethods.isEmpty()) {
            HookLogUtils.warn("No OrderDetailActivity methods found with DexKit, falling back to direct method")
            hookOrderDetailActivity(classLoader, context, versionCode)
            return
        } else {
            // 使用找到的方法进行Hook
            for (method in targetMethods) {
                try {
                    XposedBridge.hookMethod(
                        method,
                        object : XC_MethodHook() {
                            override fun afterHookedMethod(param: MethodHookParam) {
                                try {
                                    val wechaturlField =
                                        param.result::class.java.getDeclaredField("wechaturl")
                                    wechaturlField.isAccessible = true
                                    val wechaturl = wechaturlField.get(param.result)?.toString()
                                    HookLogUtils.debug("OrderDetailActivity wechaturl: $wechaturl")

                                    // 复制链接到剪贴板
                                    if (wechaturl != null) {
                                        HookCommonUtils.copyToClipboard(
                                            context,
                                            wechaturl,
                                            "订单分享链接已复制"
                                        )
                                    }
                                    HookLogUtils.info("hook OrderDetailActivity.${method.name} success with DexKit")
                                    return
                                } catch (e: Exception) {
                                    HookLogUtils.error("hook OrderDetailActivity.${method.name} error: ${e.message}")
                                }
                            }
                        }
                    )
                    hookSuccess = true
                } catch (e: Exception) {
                    HookLogUtils.debug("Failed to hook method ${method.name}: ${e.message}")
                }
            }
        }

        if (!hookSuccess) {
            // 如果没有成功Hook，回退到直接方法
            hookOrderDetailActivity(classLoader, context, versionCode)
        }
    }

    /**
     * 使用DexKit查找并Hook订单模型类
     */
    private fun hookOrderModelsWithDexKit(
        classLoader: ClassLoader,
        context: Context,
        uuid: String,
        versionCode: Int
    ) {
        val cacheKey = "order_place_repo_methods"
        var hookSuccess = false

        // 1. 尝试从缓存加载方法
        var targetMethods =
            DexKitCacheUtils.loadMethodList(context, cacheKey, classLoader, versionCode)

        if (targetMethods.isNotEmpty() && targetMethods.size == 2) {
            HookLogUtils.info("Loaded OrderPlaceRepo methods from cache")
        } else {
            // 2. 缓存未命中，使用DexKit查找
            // 根据smali代码中的特征，查找包含特定字符串的类
            val orderPlaceRepoClasses = DexKitUtils.use { bridge ->
                // 根据smali代码，查找包含特定特征的类
                // 查找包含特定字符串常量的类
                bridge.findClass(
                    FindClass.create().matcher {
                        // 查找包含这些字符串的类
                        usingStrings(
                            "cn/yonghui/hyd/order/confirm/newly/request/OrderPlaceRepo",
                            "orderConfirm",
                            "requestOrderPlace"
                        )
                    }
                ).toList()
            } ?: emptyList()

            HookLogUtils.info("orderPlaceRepoClasses: $orderPlaceRepoClasses")

            if (orderPlaceRepoClasses.isEmpty()) {
                HookLogUtils.warn("OrderPlaceRepo class not found with DexKit, falling back to direct method")
                hookOrderModels(classLoader, context, versionCode)
                return
            }

            // 查找处理订单确认和下单的方法
            val orderPlaceRepoClass = orderPlaceRepoClasses.first()

            val customerConfirmMethodsData = orderPlaceRepoClass.findMethod {
                matcher {
                    usingStrings("Lcn/yonghui/hyd/order/confirm/newly/model/CustomerConfirmOrderModel;")
                }
            }

            HookLogUtils.info("customerConfirmMethodsData: $customerConfirmMethodsData")

            val orderPlaceRequestMethodsData = orderPlaceRepoClass.findMethod {
                matcher {
                    usingStrings("Lcn/yonghui/hyd/order/confirm/newly/model/OrderPlaceRequestBean;")
                }
            }

            HookLogUtils.info("orderPlaceRequestMethodsData: $orderPlaceRequestMethodsData")

            val foundMethodsData = customerConfirmMethodsData + orderPlaceRequestMethodsData

            if (foundMethodsData.isNotEmpty()) {
                // 3. 保存到缓存
                DexKitCacheUtils.saveMethodDataList(
                    context,
                    cacheKey,
                    foundMethodsData,
                    versionCode
                )
                targetMethods = foundMethodsData.map { it.getMethodInstance(classLoader) }
            }
        }

        if (targetMethods.isEmpty()) {
            // 如果通过方法特征找不到，则回退到直接方法
            HookLogUtils.warn("No OrderPlaceRepo methods found with DexKit, falling back to direct method")
            hookOrderModels(classLoader, context, versionCode)
            return
        }

        // 使用找到的方法进行Hook
        for (method in targetMethods) {
            try {
                val methodName = method.name
                XposedBridge.hookMethod(
                    method,
                    object : XC_MethodHook() {
                        override fun beforeHookedMethod(param: MethodHookParam) {
                            try {
                                // 获取最新配置
                                val currentConfig = MainHook.getCurrentConfig() ?: return

                                if (param.args.isEmpty() || param.args[0] == null) return

                                val arg0 = param.args[0] // 获取第一个参数
                                val modelClass = arg0.javaClass // 获取arg0的类
                                val modelClassName = modelClass.name

                                HookLogUtils.info("Modifying parameters in $modelClassName via method $methodName")

                                // 尝试修改jysessionid
                                try {
                                    val jysessionidField =
                                        modelClass.getDeclaredField("jysessionid")
                                    jysessionidField.isAccessible = true
                                    jysessionidField.set(arg0, uuid)
                                    HookLogUtils.debug("Modified jysessionid to $uuid")
                                } catch (e: NoSuchFieldException) {
                                    // 忽略，可能没有这个字段
                                }

                                // 尝试修改device_info
                                try {
                                    val deviceInfoField = modelClass.getDeclaredField("device_info")
                                    deviceInfoField.isAccessible = true
                                    deviceInfoField.set(arg0, currentConfig.deviceId)
                                    HookLogUtils.debug("Modified device_info to ${currentConfig.deviceId}")
                                } catch (e: NoSuchFieldException) {
                                    // 忽略，可能没有这个字段
                                }

                                // 尝试修改cid
                                try {
                                    val cidField = modelClass.getDeclaredField("cid")
                                    cidField.isAccessible = true
                                    val channel = currentConfig.channel
                                    cidField.set(arg0, channel)
                                    HookLogUtils.debug("Modified cid to $channel")
                                } catch (e: NoSuchFieldException) {
                                    // 忽略，可能没有这个字段
                                }

                                // 尝试修改appdownloadchanel
                                try {
                                    val appdownloadchanelField =
                                        modelClass.getDeclaredField("appdownloadchanel")
                                    appdownloadchanelField.isAccessible = true
                                    val channel = currentConfig.channel
                                    appdownloadchanelField.set(arg0, channel)
                                    HookLogUtils.debug("Modified appdownloadchanel to $channel")
                                } catch (e: NoSuchFieldException) {
                                    // 忽略，可能没有这个字段
                                }
                            } catch (e: Exception) {
                                HookLogUtils.error("Error modifying parameters in method $methodName: ${e.message}")
                            }
                        }
                    }
                )
                hookSuccess = true
                HookLogUtils.info("hook ${method.declaringClass.name}.${method.name}() success with DexKit")
            } catch (e: Exception) {
                HookLogUtils.debug("Failed to hook method ${method.name}: ${e.message}")
            }
        }

        if (!hookSuccess) {
            // 如果没有成功Hook，回退到直接方法
            HookLogUtils.warn("Failed to hook OrderPlaceRepo with DexKit, falling back to direct method")
            hookOrderModels(classLoader, context, versionCode)
        }
    }

    /**
     * Hook sauron-report赋值版本
     */
    private fun hookSauronReport202314400(
        classLoader: ClassLoader,
        uuid: String
    ) {
        try {
            classLoader.loadClass("fv.i")?.let { clazz ->
                XposedBridge.hookAllMethods(
                    clazz,
                    "k",
                    object : XC_MethodHook() {
                        override fun afterHookedMethod(param: MethodHookParam) {
                            // 获取最新配置
                            val config = MainHook.getCurrentConfig() ?: return

                            val result = param.result as JSONObject
                            result.put("device_id", config.deviceId)
                            result.put("mobile_name", config.brand?.replace(" ", ""))
                            result.put("os_version", config.osVersion)
                            result.put("sessionId", uuid)
                            param.result = result
                            HookLogUtils.info("hook fv.i.k() success, for v11.4.0.4 version")
                        }
                    }
                )
                HookLogUtils.info("hook sauron-report success")
            }
        } catch (e: Exception) {
            HookLogUtils.error("hook fv.i.k() error: ${e.message}")
        }
    }

    /**
     * Hook订单详情页分享功能
     */
    private fun hookOrderDetailActivity202314400(classLoader: ClassLoader, context: Context) {
        try {
            classLoader.loadClass("cn.yonghui.hyd.order.detail.OrderDetailActivity")?.let { clazz ->
                // v11.4.0.4
                try {
                    XposedBridge.hookAllMethods(
                        clazz,
                        "pc",
                        object : XC_MethodHook() {
                            override fun afterHookedMethod(param: MethodHookParam) {
                                try {
                                    val wechaturlField =
                                        param.result::class.java.getDeclaredField("wechaturl")
                                    wechaturlField.isAccessible = true
                                    val wechaturl = wechaturlField.get(param.result)?.toString()
                                    HookLogUtils.debug("OrderDetailActivity wechaturl: $wechaturl")

                                    // 复制链接到剪贴板
                                    if (wechaturl != null) {
                                        HookCommonUtils.copyToClipboard(
                                            context,
                                            wechaturl,
                                            "订单分享链接已复制"
                                        )
                                    }
                                } catch (e: Exception) {
                                    HookLogUtils.error("hook OrderDetailActivity.pc error: ${e.message}")
                                }
                            }
                        }
                    )
                    HookLogUtils.info("hook OrderDetailActivity.pc success")
                } catch (e: Exception) {
                    HookLogUtils.error("hook OrderDetailActivity.pc error: ${e.message}")
                }
            }
        } catch (e: Exception) {
            HookLogUtils.error("hook OrderDetailActivity error: ${e.message}")
        }
    }

    /**
     * Hook订单相关模型类
     */
    private fun hookOrderModels202314400(classLoader: ClassLoader, uuid: String) {
        try {
            classLoader.loadClass("wn.a")?.let { clazz ->
                // Hook CustomerConfirmOrderModel
                XposedBridge.hookAllMethods(
                    clazz,
                    "o",
                    object : XC_MethodHook() {
                        override fun beforeHookedMethod(param: MethodHookParam) {
                            try {
                                // 获取最新配置
                                val currentConfig = MainHook.getCurrentConfig() ?: return

                                val arg0 = param.args[0] // 获取第一个参数，CustomerConfirmOrderModel实例
                                val modelClass = arg0.javaClass // 获取arg0的类

                                HookLogUtils.info("modify CustomerConfirmOrderModel parameters")

                                // 修改jysessionid
                                val jysessionidField = modelClass.getDeclaredField("jysessionid")
                                jysessionidField.isAccessible = true
                                jysessionidField.set(arg0, uuid)

                                // 修改device_info
                                val deviceInfoField = modelClass.getDeclaredField("device_info")
                                deviceInfoField.isAccessible = true
                                deviceInfoField.set(arg0, currentConfig.deviceId)

                                // 修改cid
                                val cidField = modelClass.getDeclaredField("cid")
                                cidField.isAccessible = true
                                val channel = currentConfig.channel
                                cidField.set(arg0, channel)

                                // 修改appdownloadchanel
                                val appdownloadchanelField =
                                    modelClass.getDeclaredField("appdownloadchanel")
                                appdownloadchanelField.isAccessible = true
                                appdownloadchanelField.set(arg0, channel)

                                param.args[0] = arg0
                                HookLogUtils.info("CustomerConfirmOrderModel parameters modified")
                            } catch (e: Exception) {
                                HookLogUtils.error("modify CustomerConfirmOrderModel parameters error: ${e.message}")
                            }
                        }
                    }
                )

                // Hook CustomerBuyGoodsModel
                XposedBridge.hookAllMethods(
                    clazz,
                    "q",
                    object : XC_MethodHook() {
                        override fun beforeHookedMethod(param: MethodHookParam) {
                            try {
                                val arg0 = param.args[0] // 获取第一个参数，CustomerBuyGoodsModel实例
                                val modelClass = arg0.javaClass // 获取arg0的类

                                // 修改jysessionid
                                val jysessionidField = modelClass.getDeclaredField("jysessionid")
                                jysessionidField.isAccessible = true
                                jysessionidField.set(arg0, uuid)

                                param.args[0] = arg0
                                HookLogUtils.info("CustomerBuyGoodsModel parameters modified")
                            } catch (e: Exception) {
                                HookLogUtils.error("modify CustomerBuyGoodsModel parameters error: ${e.message}")
                            }
                        }
                    }
                )

                HookLogUtils.info("hook order models classes success")
            }
        } catch (e: Exception) {
            HookLogUtils.error("hook order models classes error: ${e.message}")
        }
    }
}