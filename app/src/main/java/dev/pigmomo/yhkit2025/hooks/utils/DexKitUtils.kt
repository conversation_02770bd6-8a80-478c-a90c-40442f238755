package dev.pigmomo.yhkit2025.hooks.utils

import org.luckypray.dexkit.DexKitBridge
import org.luckypray.dexkit.query.FindClass
import org.luckypray.dexkit.query.FindMethod
import org.luckypray.dexkit.query.matchers.ClassMatcher
import org.luckypray.dexkit.query.matchers.MethodMatcher
import org.luckypray.dexkit.result.ClassData
import org.luckypray.dexkit.result.MethodData
import java.io.Closeable
import java.lang.reflect.Method

/**
 * DexKit工具类，用于查找混淆的类和方法
 */
object DexKitUtils {
    // 加载DexKit本地库
    init {
        try {
            System.loadLibrary("dexkit")
            HookLogUtils.info("DexKit library loaded successfully")
        } catch (e: Throwable) {
            HookLogUtils.error("Failed to load DexKit library: ${e.message}")
        }
    }

    private var bridge: DexKitBridge? = null

    /**
     * 初始化DexKit
     * @param classLoader 类加载器
     * @param useMemoryDexFile 是否使用内存Dex文件
     * @return 是否初始化成功
     */
    fun init(classLoader: ClassLoader, useMemoryDexFile: Boolean = true): Boolean {
        return try {
            if (bridge != null) {
                HookLogUtils.debug("DexKit already initialized")
                return true
            }

            bridge = DexKitBridge.create(classLoader, useMemoryDexFile)
            HookLogUtils.info("DexKit initialized successfully")
            true
        } catch (e: Throwable) {
            HookLogUtils.error("Failed to initialize DexKit: ${e.message}")
            false
        }
    }

    /**
     * 释放DexKit资源
     */
    fun release() {
        try {
            bridge?.close()
            bridge = null
            HookLogUtils.info("DexKit released successfully")
        } catch (e: Throwable) {
            HookLogUtils.error("Failed to release DexKit: ${e.message}")
        }
    }

    /**
     * 使用DexKit执行操作
     * @param block 操作代码块
     * @return 操作结果
     */
    fun <T> use(block: (DexKitBridge) -> T): T? {
        if (bridge == null) {
            HookLogUtils.error("DexKit not initialized")
            return null
        }

        return try {
            block(bridge!!)
        } catch (e: Throwable) {
            HookLogUtils.error("Error using DexKit: ${e.message}")
            null
        }
    }

    /**
     * 根据类名查找类
     * @param className 类名
     * @param classLoader 类加载器
     * @return 类对象
     */
    fun findClassByName(className: String, classLoader: ClassLoader): Class<*>? {
        return use { bridge ->
            val classData = bridge.findClass(
                FindClass.create()
                    .matcher(
                        ClassMatcher.create()
                            .className(className)
                    )
            ).singleOrNull()

            classData?.getInstance(classLoader)
        }
    }

    /**
     * 根据字符串查找类
     * @param strings 字符串列表
     * @param classLoader 类加载器
     * @return 类对象列表
     */
    fun findClassesByStrings(strings: List<String>, classLoader: ClassLoader): List<Class<*>> {
        return use { bridge ->
            val classDataList = bridge.findClass(
                FindClass.create()
                    .matcher(
                        ClassMatcher.create()
                            .usingStrings(*strings.toTypedArray())
                    )
            ).toList()

            classDataList.map { it.getInstance(classLoader) }
        } ?: emptyList()
    }

    /**
     * 根据字符串查找类并返回ClassData
     * @param strings 字符串列表
     * @param classLoader 类加载器
     * @return ClassData列表
     */
    fun findClassesDataByStrings(strings: List<String>, classLoader: ClassLoader): List<ClassData> {
        return use { bridge ->
            bridge.findClass(
                FindClass.create()
                    .matcher(
                        ClassMatcher.create()
                            .usingStrings(*strings.toTypedArray())
                    )
            ).toList()
        } ?: emptyList()
    }

    /**
     * 根据字符串查找方法
     * @param strings 字符串列表
     * @param classLoader 类加载器
     * @return 方法对象列表
     */
    fun findMethodsByStrings(strings: List<String>, classLoader: ClassLoader): List<Method> {
        return use { bridge ->
            val methodDataList = bridge.findMethod(
                FindMethod.create()
                    .matcher(
                        MethodMatcher.create()
                            .usingStrings(*strings.toTypedArray())
                    )
            ).toList()

            methodDataList.map { it.getMethodInstance(classLoader) }
        } ?: emptyList()
    }

    /**
     * 根据字符串查找方法并返回MethodData
     * @param strings 字符串列表
     * @param classLoader 类加载器
     * @return MethodData列表
     */
    fun findMethodsDataByStrings(
        strings: List<String>,
        classLoader: ClassLoader
    ): List<MethodData> {
        return use { bridge ->
            bridge.findMethod(
                FindMethod.create()
                    .matcher(
                        MethodMatcher.create()
                            .usingStrings(*strings.toTypedArray())
                    )
            ).toList()
        } ?: emptyList()
    }

    /**
     * 从Method对象获取MethodData
     * @param method 方法对象
     * @return MethodData对象
     */
    fun getMethodData(method: Method): MethodData? {
        val declaringClass = method.declaringClass
        val parameterTypes = method.parameterTypes.map { it.name }.toTypedArray()

        return use { bridge ->
            bridge.findMethod(
                FindMethod.create()
                    .matcher(
                        MethodMatcher.create()
                            .declaredClass(declaringClass.name)
                            .name(method.name)
                            .paramTypes(*parameterTypes)
                    )
            ).singleOrNull()
        }
    }

    /**
     * 根据包名和字符串查找类
     * @param packageName 包名
     * @param strings 字符串列表
     * @param classLoader 类加载器
     * @return 类对象列表
     */
    fun findClassesByPackageAndStrings(
        packageName: String,
        strings: List<String>,
        classLoader: ClassLoader
    ): List<Class<*>> {
        return use { bridge ->
            val classDataList = bridge.findClass(
                FindClass.create()
                    .searchPackages(packageName)
                    .matcher(
                        ClassMatcher.create()
                            .usingStrings(*strings.toTypedArray())
                    )
            ).toList()

            classDataList.map { it.getInstance(classLoader) }
        } ?: emptyList()
    }
} 