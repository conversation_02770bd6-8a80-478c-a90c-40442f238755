package dev.pigmomo.yhkit2025.hooks

import android.content.Context
import de.robv.android.xposed.IXposedHookLoadPackage
import de.robv.android.xposed.XC_MethodHook
import de.robv.android.xposed.XSharedPreferences
import de.robv.android.xposed.XposedHelpers
import de.robv.android.xposed.callbacks.XC_LoadPackage
import dev.pigmomo.yhkit2025.api.RequestConfig.AppVersion
import dev.pigmomo.yhkit2025.hooks.config.Config
import dev.pigmomo.yhkit2025.hooks.config.ConfigManager
import dev.pigmomo.yhkit2025.hooks.utils.HookCommonUtils
import dev.pigmomo.yhkit2025.hooks.utils.HookLogUtils
import java.util.UUID

/**
 * 主Hook类，实现IXposedHookLoadPackage接口
 */
class MainHook : IXposedHookLoadPackage {
    companion object {
        // 目标应用包名
        const val TARGET_PACKAGE = "cn.yonghui.hyd"

        // 日志标签
        const val LOG_TAG = "YHKit2025"

        // 共享的UUID，用于所有需要sessionId/jysessionid的地方
        val SHARED_UUID = UUID.randomUUID().toString()

        // 当前配置
        private var currentConfig: Config? = null

        /**
         * 更新配置并返回是否发生变化
         * @param newConfig 新配置
         * @return 配置是否发生变化
         */
        @Synchronized
        fun updateConfig(newConfig: Config?): Boolean {
            if (newConfig == null) return false

            val configChanged = currentConfig != newConfig
            if (configChanged) {
                HookLogUtils.info("global config has been updated")
                HookLogUtils.debug("new UID = ${newConfig.uid}")
                HookLogUtils.debug("new phone number = ${newConfig.phoneNumber}")

                currentConfig = newConfig
            }

            return configChanged
        }

        /**
         * 获取当前配置，如果不存在则尝试读取
         * @return 当前配置
         */
        @Synchronized
        fun getCurrentConfig(): Config? {
            if (currentConfig == null) {
                currentConfig = ConfigManager.readConfig()
                if (currentConfig != null) {
                    HookLogUtils.debug("已从ConfigManager读取配置")
                }
            }
            return currentConfig
        }
    }

    override fun handleLoadPackage(lpparam: XC_LoadPackage.LoadPackageParam) {
        if (lpparam.packageName != TARGET_PACKAGE) return

        HookLogUtils.info("find target app: $TARGET_PACKAGE")
        verifyXSharedPreferencesAccess()
        hookApplicationOnCreate(lpparam)
    }

    /**
     * 验证XSharedPreferences访问权限
     */
    private fun verifyXSharedPreferencesAccess() {
        val prefs = XSharedPreferences("dev.pigmomo.yhkit2025", "yhkit_config")
        if (!prefs.file.canRead()) {
            HookLogUtils.warn("warning: cannot read XSharedPreferences config file, please check permission settings")
        } else {
            HookLogUtils.debug("XSharedPreferences config file read permission is normal")
        }
    }

    /**
     * Hook应用的Application.onCreate方法
     */
    private fun hookApplicationOnCreate(lpparam: XC_LoadPackage.LoadPackageParam) {
        XposedHelpers.findAndHookMethod(
            "android.app.Application",
            lpparam.classLoader,
            "onCreate",
            object : XC_MethodHook() {
                override fun afterHookedMethod(param: MethodHookParam) {
                    HookLogUtils.info("Application onCreate has been called")

                    // 获取真实的ClassLoader
                    val realClassLoader = param.thisObject.javaClass.classLoader

                    // 获取应用上下文和版本信息
                    val context = HookCommonUtils.getContext()
                    val versionCode = HookCommonUtils.getAppVersionCode(param.thisObject)
                    HookLogUtils.info("target app version: $versionCode")

                    // 使用真实的ClassLoader进行后续hook
                    realClassLoader?.let { performHooks(it, versionCode, context) }
                }
            }
        )
    }

    /**
     * 使用真实的ClassLoader执行所有Hook操作
     */
    private fun performHooks(classLoader: ClassLoader, versionCode: Int, context: Context) {
        HookLogUtils.info("use real ClassLoader to perform Hook operations, app version: $versionCode")

        // 读取并初始化全局配置
        val config = ConfigManager.readConfig()
        if (config == null) {
            HookLogUtils.error("cannot read config file")
            return
        }

        // 更新全局配置
        updateConfig(config)

        val versionName = when (versionCode) {
            2023144000 -> "11.4.0.4"
            2023147000 -> "11.7.0.5"
            else -> AppVersion.VERSION
        }

        // 通用方法hook
        HookLogUtils.info("start hook common methods")
        CommonHooks.hookCommonMethods(classLoader, versionCode, versionName, context)

        // 版本特定方法hook
        VersionSpecificHooks.hookByVersion(classLoader, context, versionCode)
    }
}