package dev.pigmomo.yhkit2025.hooks.utils

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.widget.Toast
import de.robv.android.xposed.XposedHelpers
import java.util.regex.Pattern

/**
 * 工具类，提供各种通用方法
 */
object HookCommonUtils {
    /**
     * 获取应用上下文
     */
    fun getContext(): Context {
        return XposedHelpers.callStaticMethod(
            XposedHelpers.findClass("android.app.ActivityThread", null),
            "currentApplication"
        ) as Context
    }

    /**
     * 获取应用版本号
     */
    fun getAppVersionCode(appObject: Any): Int {
        val packageManager = XposedHelpers.callMethod(appObject, "getPackageManager")
        val packageInfo = XposedHelpers.callMethod(
            packageManager,
            "getPackageInfo",
            "cn.yonghui.hyd",
            0
        )
        return XposedHelpers.getIntField(packageInfo, "versionCode")
    }

    /**
     * 替换JSON字符串中的值
     */
    fun replaceValueInJson(
        key: String,
        newValue: String,
        arg: String
    ): String {
        val pattern = Pattern.compile("\"$key\":\"(.*?)\"")
        val matcher = pattern.matcher(arg)
        if (matcher.find()) {
            val oldValue = matcher.group(1)
            if (oldValue != null) {
                return if (oldValue.isEmpty()) {
                    arg.replace("\"$key\":\"\"", "\"$key\":\"$newValue\"")
                } else {
                    arg.replace(oldValue, newValue)
                }
            }
        }
        return arg
    }

    /**
     * 将文本复制到剪贴板并显示Toast提示
     */
    fun copyToClipboard(context: Context?, text: String, toastMessage: String) {
        try {
            val clipboardManager =
                context?.getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
            if (clipboardManager != null) {
                val clipData = ClipData.newPlainText("text", text)
                clipboardManager.setPrimaryClip(clipData)
                Toast.makeText(context, toastMessage, Toast.LENGTH_SHORT)
                    .show()
                HookLogUtils.info("copy to clipboard: $text")
            }
        } catch (e: Exception) {
            HookLogUtils.error("copy to clipboard error: ${e.message}")
        }
    }
}