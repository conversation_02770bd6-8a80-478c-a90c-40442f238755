package dev.pigmomo.yhkit2025.hooks

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.widget.Toast
import de.robv.android.xposed.XC_MethodHook
import de.robv.android.xposed.XSharedPreferences
import de.robv.android.xposed.XposedBridge
import de.robv.android.xposed.XposedHelpers
import dev.pigmomo.yhkit2025.hooks.config.ConfigManager
import dev.pigmomo.yhkit2025.hooks.utils.HookCommonUtils
import dev.pigmomo.yhkit2025.hooks.utils.HookLogUtils
import dev.pigmomo.yhkit2025.hooks.utils.HookViewUtils
import dev.pigmomo.yhkit2025.utils.HttpProxyUtils
import dev.pigmomo.yhkit2025.hooks.utils.HttpServerUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStream
import java.io.InputStreamReader
import java.net.URL

/**
 * 通用Hook类，处理各种常规Hook操作
 */
@SuppressLint("StaticFieldLeak")
object CommonHooks {
    // 用于标记Cookie是否已存在
    private var cookieParamExists = false

    // 用于存储jsString
    private var jsString: String? = null

    // JS文件名常量，方便统一修改 20250523 更新
    const val JS_FILE_NAME = "app.eebde31f.js"

    /**
     * 获取jsString值
     */
    fun getJsString(): String? {
        return jsString
    }

    /**
     * 检查配置更新并返回是否发生变化
     * @return 配置是否发生变化
     */
    fun checkConfigUpdate(): Boolean {
        // 从ConfigManager获取最新配置
        val newConfig = ConfigManager.readConfig() ?: return false

        // 更新全局配置
        return MainHook.updateConfig(newConfig)
    }

    /**
     * 通用方法hook
     */
    fun hookCommonMethods(
        classLoader: ClassLoader,
        versionCode: Int,
        versionName: String,
        context: Context
    ) {
        // 使用共享的UUID作为jysessionid
        val uuid = MainHook.SHARED_UUID

        // 拼团和永辉卡配置
        var skuCode = ""
        var grouponId = ""
        var activityCode = ""
        var yhPackageId = ""

        // 执行各种hook
        hookAuthManager(classLoader, context)
        hookRequestCommonParams(classLoader, uuid, context)
        blockDeepKnowDetection(classLoader, context)
        hookUserAgent(classLoader, versionCode, versionName, context)
        hookJsBridge(classLoader, context)
        hookDWebView(classLoader, uuid, skuCode, grouponId, activityCode, yhPackageId, context)
        hookTokenManager(classLoader, context)
        hookOkHttpRequestBody(classLoader, uuid, context)
        hookOkHttpProxy(classLoader, context)
        hookUpdateDialog(classLoader, context)
        hookParamsFormatter(classLoader, context)
        hookTokenBean(classLoader, context)
        hookRequestBuilder(classLoader, versionCode, versionName, context)
        hookWebViewClient(classLoader, context)
        //MainActivity的Hook和HTTP服务器
        hookMainActivity(classLoader, context)
    }

    /**
     * Hook AuthManager的login方法
     */
    private fun hookAuthManager(classLoader: ClassLoader, context: Context) {
        try {
            classLoader.loadClass("cn.yonghui.hyd.lib.utils.auth.AuthManager")?.let { clazz ->
                XposedBridge.hookAllMethods(
                    clazz,
                    "login",
                    object : XC_MethodHook() {
                        override fun afterHookedMethod(param: MethodHookParam) {
                            param.result = true
                            HookLogUtils.info("hook AuthManager.login, set result to true")
                        }
                    }
                )
                HookLogUtils.info("hook AuthManager.login success")
            }
        } catch (e: Exception) {
            Toast.makeText(context, "AuthManager hook failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook AuthManager error: ${e.message}")
        }
    }

    /**
     * Hook RequestCommonParams的getCommonParameters方法
     */
    private fun hookRequestCommonParams(classLoader: ClassLoader, uuid: String, context: Context) {
        try {
            classLoader.loadClass("cn.yonghui.hyd.lib.utils.http.RequestCommonParams")
                ?.let { clazz ->
                    XposedBridge.hookAllMethods(
                        clazz,
                        "getCommonParameters",
                        object : XC_MethodHook() {
                            override fun afterHookedMethod(param: MethodHookParam) {
                                val result = param.result as Map<*, *>
                                val newResult = result.toMutableMap()

                                // 使用当前最新配置
                                val currentConfig = MainHook.getCurrentConfig() ?: return

                                // 替换参数
                                with(currentConfig) {
                                    newResult["channel"] = channel
                                    newResult["model"] = model
                                    newResult["brand"] = brand
                                    newResult["osVersion"] = osVersion
                                    newResult["deviceid"] = deviceId
                                    newResult["screen"] = screen
                                    newResult["distinctId"] = distinctId
                                    newResult["access_token"] = accessToken
                                    newResult["jysessionid"] = uuid
                                }

                                param.result = newResult
                                HookLogUtils.info("hook RequestCommonParams.getCommonParameters, parameters modified")
                            }
                        }
                    )
                    HookLogUtils.info("hook RequestCommonParams.getCommonParameters success")
                }
        } catch (e: Exception) {
            Toast.makeText(context, "RequestCommonParams hook failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook RequestCommonParams error: ${e.message}")
        }
    }

    /**
     * 阻止dkapi.geetest.com deepknow请求
     */
    private fun blockDeepKnowDetection(classLoader: ClassLoader, context: Context) {
        try {
            classLoader.loadClass("com.geetest.deepknow.g\$a")?.let { clazz ->
                XposedBridge.hookAllMethods(
                    clazz,
                    "run",
                    object : XC_MethodHook() {
                        override fun beforeHookedMethod(param: MethodHookParam) {
                            HookLogUtils.info("deepknow detection blocked")
                            param.result = null
                        }
                    }
                )
            }
        } catch (e: Exception) {
            Toast.makeText(context, "block deepknow detection failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("block deepknow detection error: ${e.message}")
        }
    }

    /**
     * 修改User-Agent参数
     */
    private fun hookUserAgent(
        classLoader: ClassLoader,
        versionCode: Int,
        versionName: String,
        context: Context
    ) {
        try {
            classLoader.loadClass("cn.yonghui.hyd.appframe.http.UserAgentUtil")?.let { clazz ->
                XposedBridge.hookAllMethods(
                    clazz,
                    "generateUserAgent",
                    object : XC_MethodHook() {
                        override fun afterHookedMethod(param: MethodHookParam) {
                            // 使用当前最新配置
                            val currentConfig = MainHook.getCurrentConfig() ?: return

                            val osVersionFormatted =
                                currentConfig.osVersion?.replace("android", "Android ")
                                    ?: "Android 12"
                            param.result =
                                "YhStore/$versionName cn.yonghui.hyd/$versionCode (client/phone; $osVersionFormatted; ${currentConfig.model ?: "Redmi K70 Pro"}/${currentConfig.brand ?: "Xiaomi"})"
                            HookLogUtils.debug("hook UserAgentUtil.generateUserAgent, User-Agent set to: ${param.result}")
                        }
                    }
                )
                HookLogUtils.info("hook UserAgentUtil.generateUserAgent success")
            }
        } catch (e: Exception) {
            Toast.makeText(context, "hook UserAgentUtil failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook UserAgentUtil error: ${e.message}")
        }
    }

    /**
     * 处理jsBridge分享功能
     */
    private fun hookJsBridge(classLoader: ClassLoader, context: Context) {
        try {
            classLoader.loadClass("cn.yonghui.hyd.web.jsBridge.f")?.let { clazz ->
                // 礼品卡赠送
                hookShareToSceneSession(clazz, context)

                // 助力券分享
                hookShare(clazz, context)

                HookLogUtils.info("hook jsBridge.shareToSceneSession success")
                HookLogUtils.info("hook jsBridge.share success")
            }
        } catch (e: Exception) {
            Toast.makeText(context, "hook jsBridge failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook jsBridge error: ${e.message}")
        }
    }

    /**
     * Hook礼品卡赠送分享
     */
    private fun hookShareToSceneSession(clazz: Class<*>, context: Context) {
        try {
            XposedBridge.hookAllMethods(
                clazz,
                "shareToSceneSession",
                object : XC_MethodHook() {
                    override fun beforeHookedMethod(param: MethodHookParam) {
                        try {
                            // 参数类型:org.json.JSONObject
                            val jsonObject = param.args[0] as JSONObject
                            var url = jsonObject.getString("url")
                            val wechatminiprogramurl = jsonObject.getString("wechatminiprogramurl")
                            HookLogUtils.debug("jsBridge.shareToSceneSession url: $url")
                            HookLogUtils.debug("jsBridge.shareToSceneSession wechatminiprogramurl: $wechatminiprogramurl")

                            // 处理积分组队
                            if (url.contains("yh-point-exchange")) {
                                val regex = Regex("teamCode%3D(\\d+)")
                                val match = regex.find(wechatminiprogramurl)
                                if (match != null) {
                                    url = match.groupValues[1]
                                }
                            }

                            // 复制链接至剪切板
                            HookCommonUtils.copyToClipboard(context, url, "链接已复制")
                        } catch (e: Exception) {
                            HookLogUtils.error("hook shareToSceneSession error: ${e.message}")
                        }
                    }
                }
            )
        } catch (e: Exception) {
            Toast.makeText(context, "hook shareToSceneSession failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook shareToSceneSession error: ${e.message}")
        }
    }

    /**
     * Hook助力券分享
     */
    private fun hookShare(clazz: Class<*>, context: Context) {
        try {
            XposedBridge.hookAllMethods(
                clazz,
                "share",
                object : XC_MethodHook() {
                    override fun beforeHookedMethod(param: MethodHookParam) {
                        try {
                            // 参数类型:org.json.JSONObject
                            val jsonObject = param.args[0] as JSONObject
                            val url = jsonObject.getString("url").replace("\\", "")
                            HookLogUtils.debug("jsBridge.share url: $url")

                            // 复制链接至剪切板
                            HookCommonUtils.copyToClipboard(context, url, "链接已复制")
                        } catch (e: Exception) {
                            HookLogUtils.error("hook share error: ${e.message}")
                        }
                    }
                }
            )

        } catch (e: Exception) {
            Toast.makeText(context, "hook share failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook share error: ${e.message}")
        }
    }

    /**
     * 处理DWebView
     */
    private fun hookDWebView(
        classLoader: ClassLoader,
        uuid: String,
        skuCode: String,
        grouponId: String,
        activityCode: String,
        yhPackageId: String,
        context: Context
    ) {
        try {
            classLoader.loadClass("cn.yonghui.hyd.web.dweb.DWebView")?.let { clazz ->
                // Hook DWebView.C方法
                hookDWebViewC(clazz, uuid, context)

                // Hook DWebView.E方法
                hookDWebViewE(clazz, context)

                // Hook DWebView.loadUrl方法
                hookDWebViewLoadUrl(clazz, skuCode, grouponId, activityCode, yhPackageId, context)

                HookLogUtils.info("hook DWebView success")
            }
        } catch (e: Exception) {
            Toast.makeText(context, "hook DWebView failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook DWebView error: ${e.message}")
        }
    }

    /**
     * Hook DWebView.C方法
     */
    private fun hookDWebViewC(clazz: Class<*>, uuid: String, context: Context) {
        try {
            XposedBridge.hookAllMethods(
                clazz,
                "C",
                object : XC_MethodHook() {
                    override fun beforeHookedMethod(param: MethodHookParam) {
                        // 使用当前最新配置
                        val currentConfig = MainHook.getCurrentConfig() ?: return

                        //替换DWebView中的其他参数
                        val keysAndNewValues = mapOf(
                            "token" to "${currentConfig.userKey}-601933-${currentConfig.accessToken}",
                            "deviceId" to currentConfig.deviceId,
                            "phoneModel" to currentConfig.model,
                            "brand" to currentConfig.brand,
                            "model" to currentConfig.model,
                            "osVersion" to currentConfig.osVersion,
                            "screen" to currentConfig.screen,
                            "sessionId" to uuid
                        )

                        var arg = param.args[0].toString()

                        for ((key, newValue) in keysAndNewValues) {
                            if (arg.contains(key)) {
                                arg = HookCommonUtils.replaceValueInJson(key, newValue, arg)
                            }
                        }

                        param.args[0] = arg

                        HookLogUtils.info("DWebView.C parameters modified")
                    }
                }
            )
        } catch (e: Exception) {
            Toast.makeText(context, "hook DWebView.C failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook DWebView.C error: ${e.message}")
        }
    }

    /**
     * Hook DWebView.E方法
     */
    private fun hookDWebViewE(clazz: Class<*>, context: Context) {
        try {
            val userAgentStrAfterBuild =
                "TKQ1.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/117.0.0.0 Mobile Safari/537.36"
            XposedBridge.hookAllMethods(
                clazz,
                "E",
                object : XC_MethodHook() {
                    override fun afterHookedMethod(param: MethodHookParam) {
                        // 使用当前最新配置
                        val currentConfig = MainHook.getCurrentConfig() ?: return

                        val webView = param.thisObject

                        try {
                            val setWebContentsDebuggingEnabled =
                                webView.javaClass.getMethod(
                                    "setWebContentsDebuggingEnabled",
                                    Boolean::class.java
                                )
                            setWebContentsDebuggingEnabled.invoke(null, true)
                        } catch (e: Exception) {
                            HookLogUtils.error("enable webview debugging error: ${e.message}")
                        }

                        try {
                            //DWebView的getSettings
                            val getSettings = webView.javaClass.getMethod("getSettings")
                            //DWebView的getSettings的setUserAgentString
                            val userAgentString = getSettings.invoke(webView)
                                .javaClass.getMethod(
                                    "setUserAgentString",
                                    String::class.java
                                )
                            //设置DWebView的getSettings的setUserAgentString的值
                            val osVersionFormatted =
                                currentConfig.osVersion?.replace("android", "Android ")
                                    ?: "Android 13"
                            val userAgent =
                                "Mozilla/5.0 (Linux; $osVersionFormatted; ${currentConfig.brand ?: "K20 Pro"} Build/$userAgentStrAfterBuild)"

                            userAgentString.invoke(
                                getSettings.invoke(webView),
                                userAgent
                            )

                            HookLogUtils.debug("DWebView User-Agent: $userAgent")
                        } catch (e: Exception) {
                            HookLogUtils.error("set User-Agent error: ${e.message}")
                        }
                    }
                }
            )
        } catch (e: Exception) {
            Toast.makeText(context, "hook DWebView.E failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook DWebView.E error: ${e.message}")
        }
    }

    /**
     * Hook DWebView.loadUrl方法
     */
    private fun hookDWebViewLoadUrl(
        clazz: Class<*>,
        skuCode: String,
        grouponId: String,
        activityCode: String,
        yhPackageId: String,
        context: Context
    ) {
        try {
            XposedBridge.hookAllMethods(
                clazz,
                "loadUrl",
                object : XC_MethodHook() {
                    override fun beforeHookedMethod(param: MethodHookParam) {
                        // 使用当前最新配置
                        val currentConfig = MainHook.getCurrentConfig() ?: return

                        val url = param.args[0] as String
                        HookLogUtils.debug("DWebView loadUrl: $url")

                        //拼团配置
                        if (url.contains("yh-group-purchasing") && skuCode.isNotEmpty() && grouponId.isNotEmpty() && activityCode.isNotEmpty()) {
                            param.args[0] =
                                "https://m.yonghuivip.com/yh-m-site/yh-group-purchasing/index.html?skuCode=$skuCode&grouponId=$grouponId&activityCode=$activityCode#/detail"
                            try {
                                Toast.makeText(
                                    context,
                                    "拼团配置成功",
                                    Toast.LENGTH_SHORT
                                ).show()
                            } catch (e: Exception) {
                                HookLogUtils.error("show Toast error: ${e.message}")
                            }
                        }

                        //永辉卡配置
                        if (url.contains("yh-e-card") && yhPackageId.isNotEmpty()) {
                            param.args[0] =
                                "https://m.yonghuivip.com/yh-m-site/yh-e-card/index.html?yhPackageId=$yhPackageId&canShare=0&mid=YHcard_gift&sid=YHcard&cid=Default&source_traceid=&source_spanid=98&source_project=yh_life&source_apptype=APP&source_deviceid=${currentConfig.deviceId ?: ""}&source_userid=#/getCards"
                            try {
                                Toast.makeText(
                                    context,
                                    "永辉卡配置成功",
                                    Toast.LENGTH_SHORT
                                ).show()
                            } catch (e: Exception) {
                                HookLogUtils.error("show Toast error: ${e.message}")
                            }
                        }
                    }
                }
            )
        } catch (e: Exception) {
            Toast.makeText(context, "hook DWebView.loadUrl failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook DWebView.loadUrl error: ${e.message}")
        }
    }

    /**
     * Hook TokenManager的getTokenBean方法，确保token不为空
     */
    private fun hookTokenManager(classLoader: ClassLoader, context: Context) {
        try {
            classLoader.loadClass("cn.yonghui.hyd.lib.utils.token.TokenManager")?.let { clazz ->
                XposedBridge.hookAllMethods(
                    clazz,
                    "getTokenBean",
                    object : XC_MethodHook() {
                        override fun afterHookedMethod(param: MethodHookParam) {
                            // 使用当前最新配置
                            val currentConfig = MainHook.getCurrentConfig() ?: return

                            if (param.result == null) {
                                param.result = XposedHelpers.newInstance(
                                    classLoader.loadClass("cn.yonghui.hyd.lib.utils.token.TokenBean"),
                                    currentConfig.userKey,
                                    7200,
                                    1,
                                    currentConfig.refreshToken,
                                    currentConfig.uid,
                                    "signupcode"
                                )
                                HookLogUtils.info("created new TokenBean instance")
                            }
                        }
                    }
                )
                HookLogUtils.info("hook TokenManager.getTokenBean success")
            }
        } catch (e: Exception) {
            Toast.makeText(context, "hook TokenManager failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook TokenManager error: ${e.message}")
        }
    }

    /**
     * Hook OkHttp的RequestBody.create方法，修改请求体中的参数
     */
    private fun hookOkHttpRequestBody(classLoader: ClassLoader, uuid: String, context: Context) {
        try {
            classLoader.loadClass("okhttp3.RequestBody")?.let { clazz ->
                XposedBridge.hookAllMethods(
                    clazz,
                    "create",
                    object : XC_MethodHook() {
                        override fun beforeHookedMethod(param: MethodHookParam) {
                            try {
                                // 使用当前最新配置
                                val currentConfig = MainHook.getCurrentConfig() ?: return

                                // 获取请求体内容
                                if (param.args.size < 2 || param.args[1] == null) {
                                    return
                                }

                                HookLogUtils.debug("strBuffer1: ${param.args[1]}")
                                val method = param.args[1]::class.java.getMethod("u")
                                val strBuffer = method.invoke(param.args[1]) as String?
                                HookLogUtils.debug("str1: $strBuffer")

                                // 如果请求体不是JSON格式，直接返回
                                if (strBuffer.isNullOrEmpty() || !strBuffer.trim()
                                        .startsWith("{")
                                ) {
                                    return
                                }

                                val jsonObject = JSONObject(strBuffer)
                                var isModified = false

                                // 处理sessionId
                                if (jsonObject.has("sessionId")) {
                                    jsonObject.put("sessionId", uuid)
                                    isModified = true
                                    HookLogUtils.info("modified sessionId in request body")
                                }

                                // 处理jysessionid
                                if (jsonObject.has("jysessionid")) {
                                    jsonObject.put("jysessionid", uuid)
                                    isModified = true
                                    HookLogUtils.info("modified jysessionid in request body")
                                }

                                // 处理cid
                                if (jsonObject.has("cid")) {
                                    jsonObject.put("cid", currentConfig.channel)
                                    isModified = true
                                    HookLogUtils.info("modified cid in request body")
                                }

                                // 处理appdownloadchanel
                                if (jsonObject.has("appdownloadchanel")) {
                                    jsonObject.put("appdownloadchanel", currentConfig.channel)
                                    isModified = true
                                    HookLogUtils.info("modified appdownloadchanel in request body")
                                }

                                // 处理device_info
                                if (jsonObject.has("device_info")) {
                                    jsonObject.put("device_info", currentConfig.deviceId)
                                    isModified = true
                                    HookLogUtils.info("modified device_info in request body")
                                }

                                // 如果有修改，则更新请求体
                                if (isModified) {
                                    val newJsonString = jsonObject.toString()
                                    HookLogUtils.debug("modified JSON: $newJsonString")
                                    val method2 = param.args[1]::class.java.getMethod(
                                        "n",
                                        String::class.java
                                    )
                                    val newBuffer = method2.invoke(param.args[1], newJsonString)
                                    HookLogUtils.debug("strBuffer2: $newBuffer")
                                    param.args[1] = newBuffer
                                }
                            } catch (e: Exception) {
                                HookLogUtils.error("handle RequestBody error: ${e.message}")
                            }
                        }
                    }
                )
                HookLogUtils.info("hook OkHttp RequestBody.create success")
            }
        } catch (e: Exception) {
            Toast.makeText(context, "hook OkHttp RequestBody failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook OkHttp RequestBody error: ${e.message}")
        }
    }

    /**
     * Hook OkHttpClient的proxy方法，实现HTTP代理功能
     */
    private fun hookOkHttpProxy(classLoader: ClassLoader, context: Context) {
        try {
            classLoader.loadClass("okhttp3.OkHttpClient")?.let { clazz ->
                XposedBridge.hookAllMethods(
                    clazz,
                    "proxy",
                    object : XC_MethodHook() {
                        override fun afterHookedMethod(param: MethodHookParam) {
                            // 使用ProxyUtils中的当前代理
                            HttpProxyUtils.currentProxy?.let {
                                param.result = it
                                HookLogUtils.info("set HTTP proxy: $it")
                            }
                        }
                    }
                )
                HookLogUtils.info("hook OkHttpClient.proxy success")
            }
        } catch (e: Exception) {
            Toast.makeText(context, "hook OkHttpClient.proxy failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook OkHttpClient.proxy error: ${e.message}")
        }
    }

    /**
     * Hook更新弹窗，禁用应用更新提示
     */
    private fun hookUpdateDialog(classLoader: ClassLoader, context: Context) {
        try {
            classLoader.loadClass("cn.yonghui.hyd.main.helper.update.updateorsale.UpdateDialog")
                ?.let { clazz ->
                    XposedBridge.hookAllMethods(
                        clazz,
                        "show",
                        object : XC_MethodHook() {
                            override fun beforeHookedMethod(param: MethodHookParam) {
                                try {
                                    // 设置第一个参数为null，阻止弹窗显示
                                    if (param.args.isNotEmpty()) {
                                        param.args[0] = null
                                    }
                                } catch (e: Exception) {
                                    HookLogUtils.error("disable update dialog error: ${e.message}")
                                }

                                // 显示Toast提示
                                Toast.makeText(
                                    context,
                                    "已禁用更新",
                                    Toast.LENGTH_SHORT
                                ).show()

                                // 阻止原方法执行
                                param.result = null
                            }
                        }
                    )
                    HookLogUtils.info("hook UpdateDialog.show success, update dialog disabled")
                }
        } catch (e: Exception) {
            Toast.makeText(context, "hook UpdateDialog failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook UpdateDialog error: ${e.message}")
        }
    }

    /**
     * Hook ParamsFormatter的getAccessToken方法
     */
    private fun hookParamsFormatter(classLoader: ClassLoader, context: Context) {
        try {
            classLoader.loadClass("cn.yonghui.hyd.lib.utils.http.ParamsFormatter")?.let { clazz ->
                XposedBridge.hookAllMethods(
                    clazz,
                    "getAccessToken",
                    object : XC_MethodHook() {
                        override fun afterHookedMethod(param: MethodHookParam) {
                            // 使用当前最新配置
                            val currentConfig = MainHook.getCurrentConfig() ?: return
                            param.result = currentConfig.accessToken
                            HookLogUtils.info("ParamsFormatter.getAccessToken return value replaced with: ${currentConfig.accessToken}")
                        }
                    }
                )
                HookLogUtils.info("hook ParamsFormatter.getAccessToken success")
            }
        } catch (e: Exception) {
            Toast.makeText(context, "hook ParamsFormatter failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook ParamsFormatter error: ${e.message}")
        }
    }

    /**
     * Hook TokenBean的getUid方法
     */
    private fun hookTokenBean(classLoader: ClassLoader, context: Context) {
        try {
            classLoader.loadClass("cn.yonghui.hyd.lib.utils.token.TokenBean")?.let { clazz ->
                XposedBridge.hookAllMethods(
                    clazz,
                    "getUid",
                    object : XC_MethodHook() {
                        override fun afterHookedMethod(param: MethodHookParam) {
                            // 使用当前最新配置
                            val currentConfig = MainHook.getCurrentConfig() ?: return
                            param.result = currentConfig.uid
                            HookLogUtils.info("TokenBean.getUid return value replaced with: ${currentConfig.uid}")
                        }
                    }
                )
                HookLogUtils.info("hook TokenBean.getUid success")
            }
        } catch (e: Exception) {
            Toast.makeText(context, "hook TokenBean failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook TokenBean error: ${e.message}")
        }
    }

    /**
     * Hook Request.Builder的addHeader和removeHeader方法
     */
    private fun hookRequestBuilder(
        classLoader: ClassLoader,
        versionCode: Int,
        versionName: String,
        context: Context
    ) {
        try {
            // 使用XSharedPreferences判断是否首次加载
            var firstLoad = try {
                XSharedPreferences(
                    "cn.yonghui.hyd",
                    "ad_auth"
                ).getInt("ad_key", -1).let {
                    it == -1
                }
            } catch (e: Exception) {
                HookLogUtils.error("read XSharedPreferences error: ${e.message}")
                true
            }
            HookLogUtils.info("firstLoad: $firstLoad")

            classLoader.loadClass("okhttp3.Request\$Builder")?.let { clazz ->
                // Hook addHeader方法
                XposedBridge.hookAllMethods(
                    clazz,
                    "addHeader",
                    object : XC_MethodHook() {
                        override fun beforeHookedMethod(param: MethodHookParam) {
                            try {
                                // 使用当前最新配置
                                val currentConfig = MainHook.getCurrentConfig() ?: return

                                // 处理Cookie头
                                if (param.args[0] == "Cookie") {
                                    param.args[1] = "userKey=${currentConfig.userKey}"
                                    cookieParamExists = true
                                    HookLogUtils.info("Request.Builder.addHeader set Cookie: userKey=${currentConfig.userKey}")
                                    return
                                }

                                // 如果没有Cookie头，在DNT头处添加Cookie
                                if (param.args[0] == "DNT" && !cookieParamExists) {
                                    param.args[0] = "Cookie"
                                    param.args[1] = "userKey=${currentConfig.userKey}"
                                    cookieParamExists = true
                                    HookLogUtils.info("Request.Builder.addHeader 替换DNT为Cookie: userKey=${currentConfig.userKey}")
                                    return
                                }

                                // 处理User-Agent头
                                if (param.args[0] == "User-Agent") {
                                    val osVersionFormatted =
                                        currentConfig.osVersion?.replace("android", "Android ")
                                            ?: "Android 13"
                                    val userAgent =
                                        "YhStore/$versionName cn.yonghui.hyd/$versionCode (client/phone; $osVersionFormatted; ${currentConfig.model ?: "K20 Pro"}/${currentConfig.brand ?: "Xiaomi"})"
                                    param.args[1] = userAgent
                                    HookLogUtils.info("Request.Builder.addHeader set User-Agent: $userAgent")
                                }
                            } catch (e: Exception) {
                                HookLogUtils.error("handle Request.Builder.addHeader error: ${e.message}")
                            }
                        }

                        override fun afterHookedMethod(param: MethodHookParam) {
                            try {
                                // 使用当前最新配置
                                val currentConfig = MainHook.getCurrentConfig() ?: return

                                // 在User-Agent后添加Cookie头
                                if (param.args[0] == "User-Agent") {
                                    val result = XposedHelpers.callMethod(
                                        param.thisObject,
                                        "addHeader",
                                        "Cookie",
                                        "userKey=${currentConfig.userKey}"
                                    )
                                    param.result = result
                                    HookLogUtils.info("add Cookie header after User-Agent: userKey=${currentConfig.userKey}")
                                }
                            } catch (e: Exception) {
                                HookLogUtils.error("afterHookedMethod handle Request.Builder.addHeader error: ${e.message}")
                            }
                        }
                    }
                )

                // Hook removeHeader方法
                XposedBridge.hookAllMethods(
                    clazz,
                    "removeHeader",
                    object : XC_MethodHook() {
                        override fun beforeHookedMethod(param: MethodHookParam) {
                            try {
                                // 防止移除Cookie头
                                if (firstLoad && param.args[0] == "Cookie") {
                                    firstLoad = false
                                    param.args[0] = "0000" // 替换为一个不存在的头
                                    HookLogUtils.info("prevent remove Cookie header")
                                }
                            } catch (e: Exception) {
                                HookLogUtils.error("handle Request.Builder.removeHeader error: ${e.message}")
                            }
                        }
                    }
                )

                HookLogUtils.info("hook Request.Builder success")
            }
        } catch (e: Exception) {
            Toast.makeText(context, "hook Request.Builder failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook Request.Builder error: ${e.message}")
        }
    }

    /**
     * Hook MainActivity，添加代理配置功能和JS注入功能
     */
    private fun hookMainActivity(classLoader: ClassLoader, context: Context) {
        try {
            val httpSecurityClass =
                classLoader.loadClass("cn.yonghui.hyd.lib.utils.http.httpmiddware.HttpSecurity")
            val asciiUtilsInstance =
                XposedHelpers.newInstance(classLoader.loadClass("cn.yonghui.hyd.lib.helper.util.AsciiUtils"))

            classLoader.loadClass("cn.yonghui.hyd.MainActivity")?.let { mainActivityClass ->
                HookLogUtils.info("load cn.yonghui.hyd.MainActivity success")

                // Hook onDestroy方法
                XposedBridge.hookAllMethods(
                    mainActivityClass,
                    "onDestroy",
                    object : XC_MethodHook() {
                        override fun afterHookedMethod(param: MethodHookParam) {
                            HookLogUtils.info("execute cn.yonghui.hyd.MainActivity.onDestroy")
                            // 停止HTTP服务器
                            HttpServerUtils.stopHttpServer()
                        }
                    }
                )

                // Hook onCreate方法
                XposedBridge.hookAllMethods(
                    mainActivityClass,
                    "onCreate",
                    object : XC_MethodHook() {
                        override fun afterHookedMethod(param: MethodHookParam) {
                            // 初始提示
                            val currentConfig = MainHook.getCurrentConfig() ?: return
                            Toast.makeText(
                                context,
                                "参数加载成功 ${currentConfig.phoneNumber}",
                                Toast.LENGTH_SHORT
                            ).show()

                            // 加载JS文件
                            CoroutineScope(Dispatchers.IO).launch {
                                // 加载JS文件
                                try {
                                    val jsStream =
                                        URL("https://m.yonghuivip.com/yh-m-site/yh-e-card/js/$JS_FILE_NAME").openStream()
                                    val reader =
                                        BufferedReader(InputStreamReader(jsStream, "UTF-8"))
                                    jsString = reader.readText()
                                    jsString = jsString?.replace(
                                        "getCardList(t).then((function(t){",
                                        "getCardList(t).then((function(t){if(t.code===0){for(let index=0;index<t.data.list.length;index++){t.data.list[index].canGift=true}}"
                                    )
                                    reader.close()
                                    HookLogUtils.info("load and modify JS file success")
                                } catch (e: Exception) {
                                    HookLogUtils.error("load JS file error: ${e.message}")
                                }
                            }
                        }
                    }
                )

                // Hook onResume方法
                XposedBridge.hookAllMethods(
                    mainActivityClass,
                    "onResume",
                    object : XC_MethodHook() {
                        @SuppressLint("ResourceType", "SetTextI18n")
                        override fun afterHookedMethod(param: MethodHookParam) {
                            HookLogUtils.info("execute cn.yonghui.hyd.MainActivity.onResume")

                            val activity = param.thisObject as Activity

                            // 检测配置更新
                            CoroutineScope(Dispatchers.IO).launch {
                                try {
                                    // 检查配置更新
                                    val configChanged = checkConfigUpdate()

                                    // 如果配置已更新，显示提示
                                    if (configChanged) {
                                        val newConfig = MainHook.getCurrentConfig()
                                        if (newConfig != null) {
                                            withContext(Dispatchers.Main) {
                                                Toast.makeText(
                                                    context,
                                                    "配置已更新 ${newConfig.phoneNumber}",
                                                    Toast.LENGTH_SHORT
                                                ).show()
                                                HookLogUtils.info("配置已成功更新至最新版本")
                                            }
                                        }
                                    }
                                } catch (e: Exception) {
                                    HookLogUtils.error("检查配置更新时出错: ${e.message}")
                                }
                            }

                            // 启动HTTP服务器
                            HttpServerUtils.launchHttpServer(asciiUtilsInstance, httpSecurityClass)

                            CoroutineScope(Dispatchers.IO).launch {
                                delay(3000)

                                try {
                                    withContext(Dispatchers.Main) {
                                        // 使用ViewUtils配置代理按钮
                                        HookViewUtils.setupProxyButton(
                                            activity
                                        )
                                    }
                                } catch (e: Exception) {
                                    HookLogUtils.error("handle proxy button error: $e")
                                }
                            }
                        }
                    }
                )

                HookLogUtils.info("hook MainActivity success")
            }
        } catch (e: Exception) {
            Toast.makeText(context, "hook MainActivity failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook MainActivity error: ${e.message}")
        }
    }

    /**
     * Hook WebViewClient以修改js
     */
    private fun hookWebViewClient(classLoader: ClassLoader, context: Context) {
        try {
            val webResourceResponse = classLoader.loadClass("android.webkit.WebResourceResponse")

            classLoader.loadClass("android.webkit.WebViewClient")?.let { clazz ->
                XposedBridge.hookAllMethods(
                    clazz,
                    "shouldInterceptRequest",
                    object : XC_MethodHook() {
                        //修改WebViewClient shouldInterceptRequest参数以修改WebView中的js
                        override fun afterHookedMethod(param: MethodHookParam) {
                            if (param.args.size > 1 && param.args[1] != null && param.args[1]::class.java.name == "java.lang.String") {
                                val url = param.args[1].toString()
                                if (url.contains(CommonHooks.JS_FILE_NAME)) {
                                    // 获取最新的jsString，而不是使用传入的可能为null的值
                                    val currentJsString = CommonHooks.getJsString()

                                    if (currentJsString == null) {
                                        HookLogUtils.warn("JS content not loaded yet, using original resource")
                                        return
                                    }

                                    //实例化WebResourceResponse
                                    val webResourceResponseInstance =
                                        webResourceResponse.getConstructor(
                                            String::class.java,
                                            String::class.java,
                                            InputStream::class.java
                                        ).newInstance(
                                            "application/javascript",
                                            "UTF-8",
                                            currentJsString.byteInputStream()
                                        )
                                    HookLogUtils.info("intercept and modify js resource: $url")
                                    param.result = webResourceResponseInstance
                                }
                            }
                        }
                    }
                )
                HookLogUtils.info("hook WebViewClient.shouldInterceptRequest success")
            }
        } catch (e: Exception) {
            Toast.makeText(context, "hook WebViewClient failed", Toast.LENGTH_SHORT).show()
            HookLogUtils.error("hook WebViewClient error: ${e.message}")
        }
    }
} 