package dev.pigmomo.yhkit2025.data.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import java.util.Date

/**
 * 进程日志记录的实体类
 * 用于存储ProcessRecorder记录的所有日志
 */
@Entity(
    tableName = "process_logs",
    indices = [
        Index("token_uid"), // 加快基于token_uid的查询
        Index("timestamp"), // 加快基于时间的查询
        Index("phone_number") // 加快基于手机号的查询
    ]
)
data class LogEntity(
    /**
     * 日志记录的唯一标识符
     */
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    /**
     * 关联的令牌唯一标识符
     */
    @ColumnInfo(name = "token_uid")
    val tokenUid: String,
    
    /**
     * 关联的手机号码
     */
    @ColumnInfo(name = "phone_number")
    val phoneNumber: String = "",
    
    /**
     * 日志消息内容
     */
    @ColumnInfo(name = "message")
    val message: String,
    
    /**
     * 日志记录的时间戳
     */
    @ColumnInfo(name = "timestamp")
    val timestamp: Date,
    
    /**
     * 日志标签，用于分类
     */
    @ColumnInfo(name = "tag")
    val tag: String,
    
    /**
     * 日志级别（INFO, WARNING, ERROR等）
     */
    @ColumnInfo(name = "log_level", defaultValue = "INFO")
    val logLevel: String = "INFO"
) 