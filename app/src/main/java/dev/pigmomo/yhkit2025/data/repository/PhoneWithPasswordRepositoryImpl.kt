package dev.pigmomo.yhkit2025.data.repository

import dev.pigmomo.yhkit2025.data.dao.PhoneWithPasswordDao
import dev.pigmomo.yhkit2025.data.model.PhoneWithPasswordEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

/**
 * 手机号和密码仓库实现类
 * 实现PhoneWithPasswordRepository接口定义的所有操作
 */
class PhoneWithPasswordRepositoryImpl(
    private val phoneWithPasswordDao: PhoneWithPasswordDao? = null
) : PhoneWithPasswordRepository {
    
    /**
     * 获取所有手机号和密码记录
     * @return 手机号和密码实体列表的Flow
     */
    override fun getAllPhoneWithPasswords(): Flow<List<PhoneWithPasswordEntity>> =
        phoneWithPasswordDao?.getAllPhoneWithPasswords() ?: flowOf(emptyList())
    
    /**
     * 根据手机号获取记录
     * @param phoneNumber 手机号码
     * @return 手机号和密码实体的Flow，如果不存在则为null
     */
    override fun getPhoneWithPasswordByPhoneNumber(phoneNumber: String): Flow<PhoneWithPasswordEntity?> =
        phoneWithPasswordDao?.getPhoneWithPasswordByPhoneNumber(phoneNumber) ?: flowOf(null)
    
    /**
     * 根据手机号获取记录（同步方法）
     * @param phoneNumber 手机号码
     * @return 手机号和密码实体，如果不存在则为null
     */
    override suspend fun getPhoneWithPasswordByPhoneNumberSync(phoneNumber: String): PhoneWithPasswordEntity? =
        phoneWithPasswordDao?.getPhoneWithPasswordByPhoneNumberSync(phoneNumber)
    
    /**
     * 插入手机号和密码记录
     * @param phoneWithPassword 手机号和密码实体
     */
    override suspend fun insertPhoneWithPassword(phoneWithPassword: PhoneWithPasswordEntity) {
        phoneWithPasswordDao?.insertPhoneWithPassword(phoneWithPassword)
            ?: throw UnsupportedOperationException("PhoneWithPasswordDao is not initialized")
    }
    
    /**
     * 更新手机号和密码记录
     * @param phoneWithPassword 手机号和密码实体
     */
    override suspend fun updatePhoneWithPassword(phoneWithPassword: PhoneWithPasswordEntity) {
        phoneWithPasswordDao?.updatePhoneWithPassword(phoneWithPassword)
            ?: throw UnsupportedOperationException("PhoneWithPasswordDao is not initialized")
    }
    
    /**
     * 删除手机号和密码记录
     * @param phoneWithPassword 手机号和密码实体
     */
    override suspend fun deletePhoneWithPassword(phoneWithPassword: PhoneWithPasswordEntity) {
        phoneWithPasswordDao?.deletePhoneWithPassword(phoneWithPassword)
            ?: throw UnsupportedOperationException("PhoneWithPasswordDao is not initialized")
    }
    
    /**
     * 根据手机号删除记录
     * @param phoneNumber 手机号码
     */
    override suspend fun deletePhoneWithPasswordByPhoneNumber(phoneNumber: String) {
        phoneWithPasswordDao?.deletePhoneWithPasswordByPhoneNumber(phoneNumber)
            ?: throw UnsupportedOperationException("PhoneWithPasswordDao is not initialized")
    }
    
    /**
     * 删除所有手机号和密码记录
     */
    override suspend fun deleteAllPhoneWithPasswords() {
        phoneWithPasswordDao?.deleteAllPhoneWithPasswords()
            ?: throw UnsupportedOperationException("PhoneWithPasswordDao is not initialized")
    }
    
    /**
     * 获取手机号和密码记录总数
     * @return 记录数
     */
    override suspend fun getPhoneWithPasswordCount(): Int =
        phoneWithPasswordDao?.getPhoneWithPasswordCount() ?: 0
    
    /**
     * 插入或更新手机号和密码记录
     * @param phoneNumber 手机号码
     * @param password 密码
     * @return Pair<Boolean, String> 第一个值表示是否为新插入记录，第二个值为操作结果消息
     */
    override suspend fun insertOrUpdatePhoneWithPassword(phoneNumber: String, password: String): Pair<Boolean, String> {
        if (phoneWithPasswordDao == null) {
            return Pair(false, "PhoneWithPasswordDao is not initialized")
        }
        
        val entity = PhoneWithPasswordEntity(phoneNumber = phoneNumber, password = password)
        val existingEntity = phoneWithPasswordDao.getPhoneWithPasswordByPhoneNumberSync(phoneNumber)
        
        return if (existingEntity != null) {
            phoneWithPasswordDao.updatePhoneWithPassword(entity)
            Pair(false, "Password updated for phone number: $phoneNumber")
        } else {
            phoneWithPasswordDao.insertPhoneWithPassword(entity)
            Pair(true, "New phone number with password added: $phoneNumber")
        }
    }

    /**
     * 更新指定手机号的登录状态
     * @param phoneNumber 手机号码
     * @param isLogin 是否已登录
     */
    override suspend fun updateIsLogin(phoneNumber: String, isLogin: Boolean) {
        phoneWithPasswordDao?.updateIsLogin(phoneNumber, isLogin)
            ?: throw UnsupportedOperationException("PhoneWithPasswordDao is not initialized")
    }

    /**
     * 更新所有记录的登录状态
     * @param isLogin 是否已登录
     */
    override suspend fun updateIsLoginForAll(isLogin: Boolean) {
        phoneWithPasswordDao?.updateIsLoginForAll(isLogin)
            ?: throw UnsupportedOperationException("PhoneWithPasswordDao is not initialized")
    }
} 