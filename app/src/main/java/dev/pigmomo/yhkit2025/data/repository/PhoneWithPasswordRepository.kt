package dev.pigmomo.yhkit2025.data.repository

import dev.pigmomo.yhkit2025.data.model.PhoneWithPasswordEntity
import kotlinx.coroutines.flow.Flow

/**
 * 手机号和密码仓库接口
 * 定义对手机号和密码数据的操作
 */
interface PhoneWithPasswordRepository {
    /**
     * 获取所有手机号和密码记录
     * @return 手机号和密码实体列表的Flow
     */
    fun getAllPhoneWithPasswords(): Flow<List<PhoneWithPasswordEntity>>
    
    /**
     * 根据手机号获取记录
     * @param phoneNumber 手机号码
     * @return 手机号和密码实体的Flow，如果不存在则为null
     */
    fun getPhoneWithPasswordByPhoneNumber(phoneNumber: String): Flow<PhoneWithPasswordEntity?>
    
    /**
     * 根据手机号获取记录（同步方法）
     * @param phoneNumber 手机号码
     * @return 手机号和密码实体，如果不存在则为null
     */
    suspend fun getPhoneWithPasswordByPhoneNumberSync(phoneNumber: String): PhoneWithPasswordEntity?
    
    /**
     * 插入手机号和密码记录
     * @param phoneWithPassword 手机号和密码实体
     */
    suspend fun insertPhoneWithPassword(phoneWithPassword: PhoneWithPasswordEntity)
    
    /**
     * 更新手机号和密码记录
     * @param phoneWithPassword 手机号和密码实体
     */
    suspend fun updatePhoneWithPassword(phoneWithPassword: PhoneWithPasswordEntity)
    
    /**
     * 删除手机号和密码记录
     * @param phoneWithPassword 手机号和密码实体
     */
    suspend fun deletePhoneWithPassword(phoneWithPassword: PhoneWithPasswordEntity)
    
    /**
     * 根据手机号删除记录
     * @param phoneNumber 手机号码
     */
    suspend fun deletePhoneWithPasswordByPhoneNumber(phoneNumber: String)
    
    /**
     * 删除所有手机号和密码记录
     */
    suspend fun deleteAllPhoneWithPasswords()
    
    /**
     * 获取手机号和密码记录总数
     * @return 记录数
     */
    suspend fun getPhoneWithPasswordCount(): Int
    
    /**
     * 插入或更新手机号和密码记录
     * @param phoneNumber 手机号码
     * @param password 密码
     * @return Pair<Boolean, String> 第一个值表示是否为新插入记录，第二个值为操作结果消息
     */
    suspend fun insertOrUpdatePhoneWithPassword(phoneNumber: String, password: String): Pair<Boolean, String>

    /**
     * 更新指定手机号的登录状态
     * @param phoneNumber 手机号码
     * @param isLogin 是否已登录
     */
    suspend fun updateIsLogin(phoneNumber: String, isLogin: Boolean)

    /**
     * 更新所有记录的登录状态
     * @param isLogin 是否已登录
     */
    suspend fun updateIsLoginForAll(isLogin: Boolean)
} 