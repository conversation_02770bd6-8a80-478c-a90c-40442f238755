package dev.pigmomo.yhkit2025.data.repository

import dev.pigmomo.yhkit2025.data.dao.ConfigTokenDao
import dev.pigmomo.yhkit2025.data.dao.LoginTokenDao
import dev.pigmomo.yhkit2025.data.dao.OrderTokenDao
import dev.pigmomo.yhkit2025.data.model.ConfigTokenEntity
import dev.pigmomo.yhkit2025.data.model.LoginTokenEntity
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

class TokenRepositoryImpl(
    private val configTokenDao: ConfigTokenDao? = null,
    private val loginTokenDao: LoginTokenDao? = null,
    private val orderTokenDao: OrderTokenDao? = null
) : TokenRepository {
    // 配置令牌相关操作实现
    override fun getAllConfigTokens(): Flow<List<ConfigTokenEntity>> =
        configTokenDao?.getAllConfigTokens() ?: flowOf(emptyList())

    override fun getAllConfigTokensOrderByUpdateDate(): Flow<List<ConfigTokenEntity>> =
        configTokenDao?.getAllConfigTokensOrderByUpdateDate() ?: flowOf(emptyList())

    override fun getConfigTokenByUid(uid: String): Flow<ConfigTokenEntity?> =
        configTokenDao?.getConfigTokenByUid(uid) ?: flowOf(null)

    override suspend fun insertConfigToken(configToken: ConfigTokenEntity) {
        configTokenDao?.insertConfigToken(configToken)
            ?: throw UnsupportedOperationException("ConfigTokenDao is not initialized")
    }

    override suspend fun updateConfigToken(configToken: ConfigTokenEntity) {
        configTokenDao?.updateConfigToken(configToken)
            ?: throw UnsupportedOperationException("ConfigTokenDao is not initialized")
    }

    override suspend fun deleteConfigToken(configToken: ConfigTokenEntity) {
        configTokenDao?.deleteConfigToken(configToken)
            ?: throw UnsupportedOperationException("ConfigTokenDao is not initialized")
    }

    override suspend fun deleteConfigTokenByUid(uid: String) {
        configTokenDao?.deleteConfigTokenByUid(uid)
            ?: throw UnsupportedOperationException("ConfigTokenDao is not initialized")
    }

    override suspend fun deleteAllConfigTokens() {
        configTokenDao?.deleteAllConfigTokens()
            ?: throw UnsupportedOperationException("ConfigTokenDao is not initialized")
    }

    override suspend fun getConfigTokenCount(): Int =
        configTokenDao?.getConfigTokenCount() ?: 0

    // 登录令牌相关操作实现
    override fun getAllLoginTokens(): Flow<List<LoginTokenEntity>> =
        loginTokenDao?.getAllLoginTokens() ?: flowOf(emptyList())

    override fun getLoginTokenByUid(uid: String): Flow<LoginTokenEntity?> =
        loginTokenDao?.getLoginTokenByUid(uid) ?: flowOf(null)

    override suspend fun insertLoginToken(loginToken: LoginTokenEntity) {
        loginTokenDao?.insertLoginToken(loginToken)
            ?: throw UnsupportedOperationException("LoginTokenDao is not initialized")
    }

    override suspend fun updateLoginToken(loginToken: LoginTokenEntity) {
        loginTokenDao?.updateLoginToken(loginToken)
            ?: throw UnsupportedOperationException("LoginTokenDao is not initialized")
    }

    override suspend fun deleteLoginToken(loginToken: LoginTokenEntity) {
        loginTokenDao?.deleteLoginToken(loginToken)
            ?: throw UnsupportedOperationException("LoginTokenDao is not initialized")
    }

    override suspend fun deleteLoginTokenByUid(uid: String) {
        loginTokenDao?.deleteLoginTokenByUid(uid)
            ?: throw UnsupportedOperationException("LoginTokenDao is not initialized")
    }

    override suspend fun deleteAllLoginTokens() {
        loginTokenDao?.deleteAllLoginTokens()
            ?: throw UnsupportedOperationException("LoginTokenDao is not initialized")
    }

    override suspend fun getLoginTokenCount(): Int =
        loginTokenDao?.getLoginTokenCount() ?: 0

    // 订单令牌相关操作实现
    override fun getAllOrderTokens(): Flow<List<OrderTokenEntity>> =
        orderTokenDao?.getAllOrderTokens() ?: flowOf(emptyList())

    override fun getOrderTokenByUid(uid: String): Flow<OrderTokenEntity?> =
        orderTokenDao?.getOrderTokenByUid(uid) ?: flowOf(null)

    override fun getOrderTokensByPhoneNumber(phoneNumber: String): Flow<List<OrderTokenEntity>> =
        orderTokenDao?.getOrderTokensByPhoneNumber(phoneNumber) ?: flowOf(emptyList())

    override suspend fun insertOrderToken(orderToken: OrderTokenEntity) {
        orderTokenDao?.insertOrderToken(orderToken)
            ?: throw UnsupportedOperationException("OrderTokenDao is not initialized")
    }

    override suspend fun updateOrderToken(orderToken: OrderTokenEntity) {
        orderTokenDao?.updateOrderToken(orderToken)
            ?: throw UnsupportedOperationException("OrderTokenDao is not initialized")
    }

    override suspend fun deleteOrderToken(orderToken: OrderTokenEntity) {
        orderTokenDao?.deleteOrderToken(orderToken)
            ?: throw UnsupportedOperationException("OrderTokenDao is not initialized")
    }

    override suspend fun deleteOrderTokenByUid(uid: String) {
        orderTokenDao?.deleteOrderTokenByUid(uid)
            ?: throw UnsupportedOperationException("OrderTokenDao is not initialized")
    }

    override suspend fun deleteOrderTokensByPhoneNumber(phoneNumber: String) {
        orderTokenDao?.deleteOrderTokensByPhoneNumber(phoneNumber)
            ?: throw UnsupportedOperationException("OrderTokenDao is not initialized")
    }

    override suspend fun deleteAllOrderTokens() {
        orderTokenDao?.deleteAllOrderTokens()
            ?: throw UnsupportedOperationException("OrderTokenDao is not initialized")
    }

    override suspend fun getOrderTokenCount(): Int =
        orderTokenDao?.getOrderTokenCount() ?: 0

    override suspend fun deleteOrderTokensRange(offset: Int, count: Int): Int =
        orderTokenDao?.deleteOrderTokensRange(offset, count) ?: 0

    /**
     * 根据类型插入不同的Token
     * @param token Token实体对象
     * @return Pair<Boolean, String> 第一个值表示是否为新插入记录，第二个值为操作结果消息
     */
    override suspend fun insertToken(token: Any): Pair<Boolean, String> {
        return when (token) {
            is ConfigTokenEntity -> {
                if (configTokenDao == null) {
                    return Pair(false, "ConfigTokenDao is not initialized")
                }
                if(token.updateTimestamp == 0L){
                    return Pair(false, "ConfigToken updateTimestamp is 0L")
                }
                val existingToken = configTokenDao.getConfigTokenByUidSync(token.uid)
                if (existingToken != null) {
                    configTokenDao.updateConfigToken(token)
                    Pair(false, "ConfigToken updated")
                } else {
                    configTokenDao.insertConfigToken(token)
                    Pair(true, "ConfigToken insert success")
                }
            }

            is LoginTokenEntity -> {
                if (loginTokenDao == null) {
                    return Pair(false, "LoginTokenDao is not initialized")
                }
                val existingToken = loginTokenDao.getLoginTokenByUidSync(token.uid)
                if (existingToken != null) {
                    loginTokenDao.updateLoginToken(token)
                    Pair(false, "LoginToken updated")
                } else {
                    loginTokenDao.insertLoginToken(token)
                    Pair(true, "LoginToken insert success")
                }
            }

            is OrderTokenEntity -> {
                if (orderTokenDao == null) {
                    return Pair(false, "OrderTokenDao is not initialized")
                }
                val existingToken = orderTokenDao.getOrderTokenByUidSync(token.uid)
                if (existingToken != null) {
                    orderTokenDao.updateOrderToken(token)
                    Pair(false, "OrderToken updated")
                } else {
                    orderTokenDao.insertOrderToken(token)
                    Pair(true, "OrderToken insert success")
                }
            }

            else -> Pair(false, "Unknown token type")
        }
    }
} 