package dev.pigmomo.yhkit2025.data.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import dev.pigmomo.yhkit2025.data.model.LogEntity
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * 日志数据访问对象接口
 * 提供对日志数据的CRUD操作
 */
@Dao
interface LogDao {
    /**
     * 插入一条新日志记录
     * @param log 要插入的日志实体
     * @return 插入记录的ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertLog(log: LogEntity): Long
    
    /**
     * 插入多条日志记录
     * @param logs 要插入的日志实体列表
     * @return 插入记录的ID列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertLogs(logs: List<LogEntity>): List<Long>
    
    /**
     * 获取指定令牌的所有日志记录
     * @param tokenUid 令牌唯一标识符
     * @return 日志实体列表的Flow
     */
    @Query("SELECT * FROM process_logs WHERE token_uid = :tokenUid ORDER BY timestamp DESC")
    fun getLogsByToken(tokenUid: String): Flow<List<LogEntity>>
    
    /**
     * 获取某个时间范围内的所有日志
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 日志实体列表的Flow
     */
    @Query("SELECT * FROM process_logs WHERE timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp DESC")
    fun getLogsByTimeRange(startTime: Date, endTime: Date): Flow<List<LogEntity>>
    
    /**
     * 获取指定令牌和时间范围的日志
     * @param tokenUid 令牌唯一标识符
     * @param startTime 开始时间
     * @param endTime 结束时间 
     * @return 日志实体列表的Flow
     */
    @Query("SELECT * FROM process_logs WHERE token_uid = :tokenUid AND timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp DESC")
    fun getLogsByTokenAndTimeRange(tokenUid: String, startTime: Date, endTime: Date): Flow<List<LogEntity>>
    
    /**
     * 获取指定标签的所有日志
     * @param tag 日志标签
     * @return 日志实体列表的Flow
     */
    @Query("SELECT * FROM process_logs WHERE tag = :tag ORDER BY timestamp DESC")
    fun getLogsByTag(tag: String): Flow<List<LogEntity>>
    
    /**
     * 获取指定日志级别的所有日志
     * @param logLevel 日志级别
     * @return 日志实体列表的Flow
     */
    @Query("SELECT * FROM process_logs WHERE log_level = :logLevel ORDER BY timestamp DESC")
    fun getLogsByLevel(logLevel: String): Flow<List<LogEntity>>
    
    /**
     * 根据关键字搜索日志内容
     * @param keyword 搜索关键字
     * @return 日志实体列表的Flow
     */
    @Query("SELECT * FROM process_logs WHERE message LIKE '%' || :keyword || '%' OR token_uid LIKE '%' || :keyword || '%' OR phone_number LIKE '%' || :keyword || '%' ORDER BY timestamp DESC")
    fun searchLogs(keyword: String): Flow<List<LogEntity>>
    
    /**
     * 删除指定令牌的所有日志
     * @param tokenUid 令牌唯一标识符
     * @return 删除的记录数
     */
    @Query("DELETE FROM process_logs WHERE token_uid = :tokenUid")
    suspend fun deleteLogsByToken(tokenUid: String): Int
    
    /**
     * 删除指定时间之前的日志
     * @param timestamp 时间戳
     * @return 删除的记录数
     */
    @Query("DELETE FROM process_logs WHERE timestamp < :timestamp")
    suspend fun deleteLogsBefore(timestamp: Date): Int
    
    /**
     * 获取所有日志记录
     * @return 日志实体列表的Flow
     */
    @Query("SELECT * FROM process_logs ORDER BY timestamp DESC")
    fun getAllLogs(): Flow<List<LogEntity>>
    
    /**
     * 清空日志表
     * @return 删除的记录数
     */
    @Query("DELETE FROM process_logs")
    suspend fun clearAllLogs(): Int
    
    /**
     * 分页获取所有日志
     * @param offset 起始偏移量
     * @param limit 限制数量
     * @return 日志实体列表
     */
    @Query("SELECT * FROM process_logs ORDER BY timestamp DESC LIMIT :limit OFFSET :offset")
    suspend fun getLogsWithPagination(offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 分页获取指定级别的日志
     * @param logLevel 日志级别
     * @param offset 起始偏移量
     * @param limit 限制数量
     * @return 日志实体列表
     */
    @Query("SELECT * FROM process_logs WHERE log_level = :logLevel ORDER BY timestamp DESC LIMIT :limit OFFSET :offset")
    suspend fun getLogsByLevelWithPagination(logLevel: String, offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 分页获取指定令牌的日志
     * @param tokenUid 令牌唯一标识符
     * @param offset 起始偏移量
     * @param limit 限制数量
     * @return 日志实体列表
     */
    @Query("SELECT * FROM process_logs WHERE token_uid = :tokenUid ORDER BY timestamp DESC LIMIT :limit OFFSET :offset")
    suspend fun getLogsByTokenWithPagination(tokenUid: String, offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 分页获取指定令牌和级别的日志
     * @param tokenUid 令牌唯一标识符
     * @param logLevel 日志级别
     * @param offset 起始偏移量
     * @param limit 限制数量
     * @return 日志实体列表
     */
    @Query("SELECT * FROM process_logs WHERE token_uid = :tokenUid AND log_level = :logLevel ORDER BY timestamp DESC LIMIT :limit OFFSET :offset")
    suspend fun getLogsByTokenAndLevelWithPagination(tokenUid: String, logLevel: String, offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 分页获取指定标签的日志
     * @param tag 日志标签
     * @param offset 起始偏移量
     * @param limit 限制数量
     * @return 日志实体列表
     */
    @Query("SELECT * FROM process_logs WHERE tag = :tag ORDER BY timestamp DESC LIMIT :limit OFFSET :offset")
    suspend fun getLogsByTagWithPagination(tag: String, offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 分页搜索日志内容
     * @param keyword 搜索关键字
     * @param offset 起始偏移量
     * @param limit 限制数量
     * @return 日志实体列表
     */
    @Query("SELECT * FROM process_logs WHERE message LIKE '%' || :keyword || '%' OR token_uid LIKE '%' || :keyword || '%' OR phone_number LIKE '%' || :keyword || '%' ORDER BY timestamp DESC LIMIT :limit OFFSET :offset")
    suspend fun searchLogsWithPagination(keyword: String, offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 按日志级别和关键词分页搜索日志
     * @param logLevel 日志级别
     * @param keyword 搜索关键字
     * @param offset 起始偏移量
     * @param limit 限制数量
     * @return 日志实体列表
     */
    @Query("SELECT * FROM process_logs WHERE log_level = :logLevel AND (message LIKE '%' || :keyword || '%' OR token_uid LIKE '%' || :keyword || '%' OR phone_number LIKE '%' || :keyword || '%') ORDER BY timestamp DESC LIMIT :limit OFFSET :offset")
    suspend fun searchLogsByLevelWithPagination(logLevel: String, keyword: String, offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 获取指定手机号的所有日志
     * @param phoneNumber 手机号码
     * @return 日志实体列表的Flow
     */
    @Query("SELECT * FROM process_logs WHERE phone_number = :phoneNumber ORDER BY timestamp DESC")
    fun getLogsByPhoneNumber(phoneNumber: String): Flow<List<LogEntity>>
    
    /**
     * 删除指定手机号的所有日志
     * @param phoneNumber 手机号码
     * @return 删除的记录数
     */
    @Query("DELETE FROM process_logs WHERE phone_number = :phoneNumber")
    suspend fun deleteLogsByPhoneNumber(phoneNumber: String): Int
    
    /**
     * 分页获取指定手机号的日志
     * @param phoneNumber 手机号码
     * @param offset 起始偏移量
     * @param limit 限制数量
     * @return 日志实体列表
     */
    @Query("SELECT * FROM process_logs WHERE phone_number = :phoneNumber ORDER BY timestamp DESC LIMIT :limit OFFSET :offset")
    suspend fun getLogsByPhoneNumberWithPagination(phoneNumber: String, offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 获取指定令牌的最新一条日志记录
     * @param tokenUid 令牌唯一标识符
     * @return 最新的日志实体，如果没有则返回null
     */
    @Query("SELECT * FROM process_logs WHERE token_uid = :tokenUid ORDER BY timestamp DESC LIMIT 1")
    suspend fun getLatestLogByToken(tokenUid: String): LogEntity?

    /**
     * 获取日志总数
     * @return 日志总数
     */
    @Query("SELECT COUNT(*) FROM process_logs")
    suspend fun getLogCount(): Int
}