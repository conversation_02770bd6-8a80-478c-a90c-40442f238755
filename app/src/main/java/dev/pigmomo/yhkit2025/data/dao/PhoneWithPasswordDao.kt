package dev.pigmomo.yhkit2025.data.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import dev.pigmomo.yhkit2025.data.model.PhoneWithPasswordEntity
import kotlinx.coroutines.flow.Flow

/**
 * 手机号和密码数据访问对象
 * 提供对phone_with_password表的CRUD操作
 */
@Dao
interface PhoneWithPasswordDao {
    /**
     * 获取所有手机号和密码记录
     * @return 手机号和密码实体列表的Flow
     */
    @Query("SELECT * FROM phone_with_password")
    fun getAllPhoneWithPasswords(): Flow<List<PhoneWithPasswordEntity>>
    
    /**
     * 根据手机号获取记录
     * @param phoneNumber 手机号码
     * @return 手机号和密码实体的Flow，如果不存在则为null
     */
    @Query("SELECT * FROM phone_with_password WHERE phoneNumber = :phoneNumber")
    fun getPhoneWithPasswordByPhoneNumber(phoneNumber: String): Flow<PhoneWithPasswordEntity?>
    
    /**
     * 根据手机号获取记录（同步方法）
     * @param phoneNumber 手机号码
     * @return 手机号和密码实体，如果不存在则为null
     */
    @Query("SELECT * FROM phone_with_password WHERE phoneNumber = :phoneNumber")
    suspend fun getPhoneWithPasswordByPhoneNumberSync(phoneNumber: String): PhoneWithPasswordEntity?
    
    /**
     * 插入手机号和密码记录
     * @param phoneWithPassword 手机号和密码实体
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPhoneWithPassword(phoneWithPassword: PhoneWithPasswordEntity)
    
    /**
     * 更新手机号和密码记录
     * @param phoneWithPassword 手机号和密码实体
     */
    @Update
    suspend fun updatePhoneWithPassword(phoneWithPassword: PhoneWithPasswordEntity)
    
    /**
     * 删除手机号和密码记录
     * @param phoneWithPassword 手机号和密码实体
     */
    @Delete
    suspend fun deletePhoneWithPassword(phoneWithPassword: PhoneWithPasswordEntity)
    
    /**
     * 根据手机号删除记录
     * @param phoneNumber 手机号码
     */
    @Query("DELETE FROM phone_with_password WHERE phoneNumber = :phoneNumber")
    suspend fun deletePhoneWithPasswordByPhoneNumber(phoneNumber: String)
    
    /**
     * 删除所有手机号和密码记录
     */
    @Query("DELETE FROM phone_with_password")
    suspend fun deleteAllPhoneWithPasswords()
    
    /**
     * 获取手机号和密码记录总数
     * @return 记录数
     */
    @Query("SELECT COUNT(*) FROM phone_with_password")
    suspend fun getPhoneWithPasswordCount(): Int

    /**
     * 更新指定手机号的登录状态
     * @param phoneNumber 手机号码
     * @param isLogin 是否已登录
     */
    @Query("UPDATE phone_with_password SET isLogin = :isLogin WHERE phoneNumber = :phoneNumber")
    suspend fun updateIsLogin(phoneNumber: String, isLogin: Boolean)

    /**
     * 更新所有记录的登录状态
     * @param isLogin 是否已登录
     */
    @Query("UPDATE phone_with_password SET isLogin = :isLogin")
    suspend fun updateIsLoginForAll(isLogin: Boolean)
} 