package dev.pigmomo.yhkit2025.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Token实体基础接口
 */
interface TokenEntity {
    val uid: String
    val phoneNumber: String
    val userKey: String
    val accessToken: String
    val refreshToken: String
    val expiresIn: Long
    val updateDate: String
    val appParam: String
    val extraNote: String
}

@Entity(tableName = "config_tokens")
data class ConfigTokenEntity(
    @PrimaryKey
    //memberId/uid/userId
    override val uid: String,
    override val phoneNumber: String,
    override val userKey: String,
    override val accessToken: String,
    override val refreshToken: String,
    override val expiresIn: Long,
    //mm.dd.yy
    //05.08.25
    override val updateDate: String,
    //是否有新人特权（新人价）
    var isNew: Boolean,
    //是否首次砍价
    var bargainFirst: Boolean,
    //是否参加活动火爆
    var activityLimited: Boolean,
    //是否使用/购买永辉卡风险
    var yhCardLimited: Boolean,
    //channel,screen,deviceid,distinctId,osVersion,model,networkType,brand,version
    //vivo,1080x2400,b1aa9816-bdca-4abc-b342-0b613aad431f,38219ba1-7015-4e2f-925b-0343f516b380,android33,vivo,WIFI,V2343A,appVersion
    override val appParam: String,
    override var extraNote: String,
    // 额外属性
    var updateTimestamp: Long
) : TokenEntity

@Entity(tableName = "login_tokens")
data class LoginTokenEntity(
    @PrimaryKey
    override val uid: String,
    override val phoneNumber: String,
    override val userKey: String,
    override val accessToken: String,
    override val refreshToken: String,
    override val expiresIn: Long,
    override val updateDate: String,
    var isNew: Boolean,
    var bargainFirst: Boolean,
    var activityLimited: Boolean,
    var yhCardLimited: Boolean,
    override val appParam: String,
    override var extraNote: String,
    // 额外属性
    var smsCode: String = "",
    var isLogin: Boolean = true,
    var isTimeOut: Boolean = false,
    var loginIndex: Int = 0
) : TokenEntity

@Entity(tableName = "order_tokens")
data class OrderTokenEntity(
    @PrimaryKey
    override val uid: String,
    override val phoneNumber: String,
    override val userKey: String,
    override val accessToken: String,
    override val refreshToken: String,
    override val expiresIn: Long,
    override val updateDate: String,
    var isNew: Boolean,
    var bargainFirst: Boolean,
    var activityLimited: Boolean,
    var yhCardLimited: Boolean,
    override val appParam: String,
    override var extraNote: String,
    // 额外属性
    var sortOrder: Long = System.currentTimeMillis(),
    var isLogin: Boolean = true,
    var credit: Int = 0,
    var cardBalance: Float = 0f
) : TokenEntity