package dev.pigmomo.yhkit2025.data.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import dev.pigmomo.yhkit2025.data.model.ConfigTokenEntity
import dev.pigmomo.yhkit2025.data.model.LoginTokenEntity
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface ConfigTokenDao {
    @Query("SELECT * FROM config_tokens")
    fun getAllConfigTokens(): Flow<List<ConfigTokenEntity>>
    
    @Query("SELECT * FROM config_tokens ORDER BY updateTimestamp DESC")
    fun getAllConfigTokensOrderByUpdateDate(): Flow<List<ConfigTokenEntity>>
    
    @Query("SELECT * FROM config_tokens WHERE uid = :uid")
    fun getConfigTokenByUid(uid: String): Flow<ConfigTokenEntity?>
    
    @Query("SELECT * FROM config_tokens WHERE uid = :uid")
    suspend fun getConfigTokenByUidSync(uid: String): ConfigTokenEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertConfigToken(configToken: ConfigTokenEntity)
    
    @Update
    suspend fun updateConfigToken(configToken: ConfigTokenEntity)
    
    @Delete
    suspend fun deleteConfigToken(configToken: ConfigTokenEntity)
    
    @Query("DELETE FROM config_tokens WHERE uid = :uid")
    suspend fun deleteConfigTokenByUid(uid: String)
    
    @Query("DELETE FROM config_tokens")
    suspend fun deleteAllConfigTokens()
    
    @Query("SELECT COUNT(*) FROM config_tokens")
    suspend fun getConfigTokenCount(): Int
}

@Dao
interface LoginTokenDao {
    @Query("SELECT * FROM login_tokens ORDER BY loginIndex ASC")
    fun getAllLoginTokens(): Flow<List<LoginTokenEntity>>
    
    @Query("SELECT * FROM login_tokens WHERE uid = :uid")
    fun getLoginTokenByUid(uid: String): Flow<LoginTokenEntity?>
    
    @Query("SELECT * FROM login_tokens WHERE uid = :uid")
    suspend fun getLoginTokenByUidSync(uid: String): LoginTokenEntity?
    
    @Query("SELECT * FROM login_tokens WHERE phoneNumber = :phoneNumber")
    suspend fun getLoginTokenByPhoneNumberSync(phoneNumber: String): LoginTokenEntity?
    
    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertLoginToken(loginToken: LoginTokenEntity)
    
    @Update
    suspend fun updateLoginToken(loginToken: LoginTokenEntity)
    
    @Delete
    suspend fun deleteLoginToken(loginToken: LoginTokenEntity)
    
    @Query("DELETE FROM login_tokens WHERE uid = :uid")
    suspend fun deleteLoginTokenByUid(uid: String)
    
    @Query("DELETE FROM login_tokens")
    suspend fun deleteAllLoginTokens()
    
    @Query("SELECT COUNT(*) FROM login_tokens")
    suspend fun getLoginTokenCount(): Int
}

@Dao
interface OrderTokenDao {
    @Query("SELECT * FROM order_tokens ORDER BY sortOrder ASC")
    fun getAllOrderTokens(): Flow<List<OrderTokenEntity>>
    
    @Query("SELECT * FROM order_tokens WHERE uid = :uid")
    fun getOrderTokenByUid(uid: String): Flow<OrderTokenEntity?>
    
    @Query("SELECT * FROM order_tokens WHERE uid = :uid")
    suspend fun getOrderTokenByUidSync(uid: String): OrderTokenEntity?
    
    @Query("SELECT * FROM order_tokens WHERE phoneNumber = :phoneNumber")
    fun getOrderTokensByPhoneNumber(phoneNumber: String): Flow<List<OrderTokenEntity>>
    
    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertOrderToken(orderToken: OrderTokenEntity)
    
    @Update
    suspend fun updateOrderToken(orderToken: OrderTokenEntity)
    
    @Delete
    suspend fun deleteOrderToken(orderToken: OrderTokenEntity)
    
    @Query("DELETE FROM order_tokens WHERE uid = :uid")
    suspend fun deleteOrderTokenByUid(uid: String)
    
    @Query("DELETE FROM order_tokens WHERE phoneNumber = :phoneNumber")
    suspend fun deleteOrderTokensByPhoneNumber(phoneNumber: String)
    
    @Query("DELETE FROM order_tokens")
    suspend fun deleteAllOrderTokens()
    
    @Query("SELECT COUNT(*) FROM order_tokens")
    suspend fun getOrderTokenCount(): Int

    @Query("""
        DELETE FROM order_tokens 
        WHERE uid IN (
            SELECT uid FROM order_tokens 
            ORDER BY sortOrder ASC 
            LIMIT :count OFFSET :offset
        )
    """)
    suspend fun deleteOrderTokensRange(offset: Int, count: Int): Int
}
