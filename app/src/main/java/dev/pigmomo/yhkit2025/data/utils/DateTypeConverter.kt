package dev.pigmomo.yhkit2025.data.utils

import androidx.room.TypeConverter
import java.util.Date

/**
 * 日期类型转换器
 * 用于在Room数据库中存储和检索Date类型
 */
class DateTypeConverter {
    /**
     * 将时间戳转换为Date对象
     * @param value 时间戳（毫秒）
     * @return Date对象，如果输入为空则返回null
     */
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }

    /**
     * 将Date对象转换为时间戳
     * @param date Date对象
     * @return 时间戳（毫秒），如果输入为空则返回null
     */
    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }
}