package dev.pigmomo.yhkit2025.data.repository

import dev.pigmomo.yhkit2025.data.model.ConfigTokenEntity
import dev.pigmomo.yhkit2025.data.model.LoginTokenEntity
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import kotlinx.coroutines.flow.Flow

interface TokenRepository {
    // 配置令牌相关操作
    fun getAllConfigTokens(): Flow<List<ConfigTokenEntity>>
    fun getAllConfigTokensOrderByUpdateDate(): Flow<List<ConfigTokenEntity>>
    fun getConfigTokenByUid(uid: String): Flow<ConfigTokenEntity?>
    suspend fun insertConfigToken(configToken: ConfigTokenEntity)
    suspend fun updateConfigToken(configToken: ConfigTokenEntity)
    suspend fun deleteConfigToken(configToken: ConfigTokenEntity)
    suspend fun deleteConfigTokenByUid(uid: String)
    suspend fun deleteAllConfigTokens()
    suspend fun getConfigTokenCount(): Int

    // 登录令牌相关操作
    fun getAllLoginTokens(): Flow<List<LoginTokenEntity>>
    fun getLoginTokenByUid(uid: String): Flow<LoginTokenEntity?>
    suspend fun insertLoginToken(loginToken: LoginTokenEntity)
    suspend fun updateLoginToken(loginToken: LoginTokenEntity)
    suspend fun deleteLoginToken(loginToken: LoginTokenEntity)
    suspend fun deleteLoginTokenByUid(uid: String)
    suspend fun deleteAllLoginTokens()
    suspend fun getLoginTokenCount(): Int
    
    // 订单令牌相关操作
    fun getAllOrderTokens(): Flow<List<OrderTokenEntity>>
    fun getOrderTokenByUid(uid: String): Flow<OrderTokenEntity?>
    fun getOrderTokensByPhoneNumber(phoneNumber: String): Flow<List<OrderTokenEntity>>
    suspend fun insertOrderToken(orderToken: OrderTokenEntity)
    suspend fun updateOrderToken(orderToken: OrderTokenEntity)
    suspend fun deleteOrderToken(orderToken: OrderTokenEntity)
    suspend fun deleteOrderTokenByUid(uid: String)
    suspend fun deleteOrderTokensByPhoneNumber(phoneNumber: String)
    suspend fun deleteAllOrderTokens()
    suspend fun getOrderTokenCount(): Int
    suspend fun deleteOrderTokensRange(offset: Int, count: Int): Int
    
    /**
     * 根据类型插入不同的Token
     * @param token Token实体对象
     * @return Pair<Boolean, String> 第一个值表示是否为新插入记录，第二个值为操作结果消息
     */
    suspend fun insertToken(token: Any): Pair<Boolean, String>
} 