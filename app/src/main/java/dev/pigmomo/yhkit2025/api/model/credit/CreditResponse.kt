package dev.pigmomo.yhkit2025.api.model.credit

/**
 * 积分详情响应模型
 * 包含用户当前积分、即将过期积分、积分明细列表等信息
 */
data class CreditResponse(
    val code: Int = 0,
    val message: String = "",
    val data: CreditData? = null,
    val now: Long = 0
)

/**
 * 积分详情数据
 * @property credit 当前总积分
 * @property expiringcredit 即将过期的积分
 * @property page 当前页码
 * @property pagecount 总页数
 * @property count 总记录数
 * @property details 积分明细列表
 * @property tip 提示信息
 * @property orderdesc1 订单积分说明1
 * @property orderdesc2 订单积分说明2
 * @property commentdesc1 评论积分说明1
 * @property commentdesc2 评论积分说明2
 */
data class CreditData(
    val credit: Int = 0,
    val expiringcredit: Double = 0.0,
    val page: Int = 0,
    val pagecount: Int = 0,
    val count: Int = 0,
    val details: List<CreditDetail>? = emptyList(),
    val tip: String? = null,
    val orderdesc1: String = "",
    val orderdesc2: String = "",
    val commentdesc1: String = "",
    val commentdesc2: String = ""
)

/**
 * 积分明细记录
 * @property type 积分变动类型
 * @property desc 积分变动描述
 * @property addition 积分变动数量（正数为增加，负数为减少）
 * @property date 积分变动时间戳（毫秒）
 */
data class CreditDetail(
    val type: Int = 0,
    val desc: String = "",
    val addition: Double = 0.0,
    val date: Long = 0
) 