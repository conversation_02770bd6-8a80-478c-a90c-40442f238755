package dev.pigmomo.yhkit2025.api.service

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestHelper
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.SignType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 邀请服务类
 * 提供邀请相关的API调用方法
 */
class InvitationService(requestHelper: RequestHelper) : BaseService(requestHelper) {

    private val tag = "InvitationService"

    private val serviceType = requestHelper.getServiceType()

    /**
     * 获取邀请活动信息
     * @return 邀请活动信息响应结果
     */
    suspend fun invitationV2ActivityInfo(): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "invitationV2ActivityInfo: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建业务参数
                val businessParams = buildAppBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId

                // 构建URL
                val urlWithParams = buildAppApiUrl(
                    RequestConfig.Path.INVITATION_V2_ACTIVITY_INFO_PATH,
                    businessParams,
                    commonParams
                )

                // 构建请求体
                val requestBody = "{}"

                // 计算签名
                val needSignStr =
                    urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                        .replace("=", "")
                        .replace("&", "") + requestBody

                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(
                        tag,
                        "invitationV2ActivityInfo: sign empty, service may not be initialized"
                    )
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "invitationV2ActivityInfo: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                // 发送请求
                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "invitationV2ActivityInfo: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.INVITATION_V2_ACTIVITY_INFO_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                // 构建请求体
                val requestBody = "{}"

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "invitationV2ActivityInfo: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                // 发送请求
                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 绑定邀请关系
     * @param inviterID 邀请人ID
     * @param activityCode 活动代码
     * @return 绑定结果响应
     */
    suspend fun invitationV2Bind(
        inviterID: String,
        activityCode: String,
        shopId: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val cityId = requestHelper.getCityId()
                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "invitationV2Bind: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()

                // 添加额外参数
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["cityid"] = cityId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.INVITATION_V2_BIND_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建请求体
                val requestBody =
                    """{"activityCode":"$activityCode","inviter":"$inviterID","shopId":"$shopId","source":"3"}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(urlWithParams, requestBody, SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val webXyhBizParams = requestHelper.getWebXyhBizParams()
                if (webXyhBizParams.isEmpty()) {
                    Log.e(tag, "invitationV2Bind: webXyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("webXyhBizParams not set"))
                }

                val headers = buildAppStandardHeaders(webXyhBizParams, true, true)

                // 发送请求
                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val cityId = requestHelper.getCityId()
                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "invitationV2Bind: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramWebBusinessParams()

                // 添加额外参数
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["cityid"] = cityId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.INVITATION_V2_BIND_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                // 构建请求体
                val requestBody =
                    """{"activityCode":"$activityCode","inviter":"$inviterID","shopId":"$shopId","source":"3"}"""

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM_WEB_ACTIVITY.value
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "invitationV2Bind: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                // 发送请求
                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 获取成功邀请列表
     * @param pageIndex 页码，默认从1开始
     * @param pageSize 每页数量，默认10条
     * @return 成功邀请列表响应结果
     */
    suspend fun successInviteList(pageIndex: Int = 1, pageSize: Int = 10): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    val cityId = requestHelper.getCityId()
                    if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                        Log.e(tag, "successInviteList: sellerId or shopId or cityId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getAppCommonParams()

                    // 构建Web业务参数
                    val businessParams = buildAppWebBusinessParams()

                    // 添加额外参数
                    businessParams["sellerid"] = sellerId
                    businessParams["shopid"] = shopId
                    businessParams["cityid"] = cityId
                    businessParams["memberid"] = requestHelper.getUid()
                    businessParams["shopId"] = shopId
                    businessParams["pageindex"] = pageIndex.toString()
                    businessParams["pagesize"] = pageSize.toString()

                    // 构建URL
                    val urlWithParams = buildAppApiUrl(
                        RequestConfig.Path.SUCCESS_INVITE_LIST_PATH,
                        businessParams,
                        commonParams
                    )

                    // 生成签名
                    val sign =
                        requestHelper.generateSign(urlWithParams, "", SignType.APP_WEB.value)
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val webXyhBizParams = requestHelper.getWebXyhBizParams()
                    if (webXyhBizParams.isEmpty()) {
                        Log.e(tag, "successInviteList: webXyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("webXyhBizParams not set"))
                    }

                    val headers = buildAppStandardHeaders(webXyhBizParams, true, false)

                    // 发送请求
                    requestHelper.get(fullUrl, headers)
                }

                "mini" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    val cityId = requestHelper.getCityId()
                    if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                        Log.e(tag, "successInviteList: sellerId or shopId or cityId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getMiniProgramCommonParams()

                    // 构建Web业务参数
                    val businessParams = buildMiniProgramWebBusinessParams()

                    // 添加额外参数
                    businessParams["sellerid"] = sellerId
                    businessParams["shopid"] = shopId
                    businessParams["cityid"] = cityId
                    businessParams["memberid"] = requestHelper.getUid()
                    businessParams["shopId"] = shopId
                    businessParams["pageindex"] = pageIndex.toString()
                    businessParams["pagesize"] = pageSize.toString()

                    // 构建URL
                    val urlWithParams = buildMiniApiUrl(
                        RequestConfig.Path.SUCCESS_INVITE_LIST_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                    // 生成签名
                    val sign = requestHelper.generateSign(
                        urlWithParams,
                        "",
                        SignType.MINIPROGRAM_WEB_ACTIVITY.value
                    )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getWebXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "successInviteList: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }

                    val headers = buildMiniStandardHeaders(xyhBizParams, true)

                    // 发送请求
                    requestHelper.get(fullUrl, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }

    /**
     * 获取总奖励列表
     * @param pageIndex 页码，默认从1开始
     * @param pageSize 每页数量，默认10条
     * @return 总奖励列表响应结果
     */
    suspend fun totalRewardList(pageIndex: Int = 1, pageSize: Int = 10): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    val cityId = requestHelper.getCityId()
                    if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                        Log.e(tag, "totalRewardList: sellerId or shopId or cityId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getAppCommonParams()

                    // 构建Web业务参数
                    val businessParams = buildAppWebBusinessParams()

                    // 添加额外参数
                    businessParams["sellerid"] = sellerId
                    businessParams["shopid"] = shopId
                    businessParams["cityid"] = cityId
                    businessParams["memberid"] = requestHelper.getUid()
                    businessParams["pageindex"] = pageIndex.toString()
                    businessParams["pagesize"] = pageSize.toString()

                    // 构建URL
                    val urlWithParams =
                        buildAppApiUrl(
                            RequestConfig.Path.TOTAL_REWARD_LIST_PATH,
                            businessParams,
                            commonParams
                        )

                    // 生成签名
                    val sign =
                        requestHelper.generateSign(urlWithParams, "", SignType.APP_WEB.value)
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val webXyhBizParams = requestHelper.getWebXyhBizParams()
                    if (webXyhBizParams.isEmpty()) {
                        Log.e(tag, "totalRewardList: webXyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("webXyhBizParams not set"))
                    }

                    val headers = buildAppStandardHeaders(webXyhBizParams, true, false)

                    // 发送请求
                    requestHelper.get(fullUrl, headers)
                }

                "mini" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    val cityId = requestHelper.getCityId()
                    if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                        Log.e(tag, "totalRewardList: sellerId or shopId or cityId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getMiniProgramCommonParams()

                    // 构建Web业务参数
                    val businessParams = buildMiniProgramWebBusinessParams()

                    // 添加额外参数
                    businessParams["sellerid"] = sellerId
                    businessParams["shopid"] = shopId
                    businessParams["cityid"] = cityId
                    businessParams["memberid"] = requestHelper.getUid()
                    businessParams["pageindex"] = pageIndex.toString()
                    businessParams["pagesize"] = pageSize.toString()

                    // 构建URL
                    val urlWithParams =
                        buildMiniApiUrl(
                            RequestConfig.Path.TOTAL_REWARD_LIST_PATH,
                            businessParams,
                            commonParams,
                            "api"
                        )

                    // 生成签名
                    val sign = requestHelper.generateSign(
                        urlWithParams,
                        "",
                        SignType.MINIPROGRAM_WEB_ACTIVITY.value
                    )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getWebXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "totalRewardList: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }

                    val headers = buildMiniStandardHeaders(xyhBizParams, true)

                    // 发送请求
                    requestHelper.get(fullUrl, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }
}