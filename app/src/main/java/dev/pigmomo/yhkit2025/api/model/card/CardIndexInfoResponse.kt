package dev.pigmomo.yhkit2025.api.model.card

data class CardIndexInfoResponse(
    val code: Int = 0,
    val message: String = "",
    val data: CardIndexInfoData? = null,
    val now: Long = 0
)

data class CardIndexInfoData(
    val balance: String = "",
    val code: Int = 0,
    val message: String = "",
    val cardCnt: Int = 0,
    val preSentCardCnt: Int = 0,
    val themeVOS: List<ThemeVO> = emptyList(),
    val categoryThemeVOS: List<Any> = emptyList(),
    val physicalCardVOS: List<Any> = emptyList(),
    val giveFriendsDesc: String = "",
    val giveCardsMsgList: List<GiveCardsMsg> = emptyList(),
    val tagList: List<CardIndexInfoTag> = emptyList(),
    val tagInfo: CardIndexInfoTagInfo? = null
)

data class ThemeVO(
    val id: String = "",
    val coverUrl: String = "",
    val cardTypeScope: Int = 0,
    val title: String = "",
    val coverList: List<CoverItem> = emptyList(),
    val amount: Double = 0.0,
    val maxAmount: Double = 0.0,
    val amountScope: String = "",
    val recommend: String = "",
    val themeSort: Int = 0
)

data class CoverItem(
    val id: String = "",
    val url: String = ""
)

data class GiveCardsMsg(
    val userName: String = "",
    val cardTitle: String = "",
    val themeId: String = "",
    val cardTypeScope: Int = 0,
    val type: Int = 0
)

data class CardIndexInfoTag(
    val tag: String = ""
)

data class CardIndexInfoTagInfo(
    val tag: String = "",
    val categoryTag: String = "",
    val physicalTag: String = ""
) 