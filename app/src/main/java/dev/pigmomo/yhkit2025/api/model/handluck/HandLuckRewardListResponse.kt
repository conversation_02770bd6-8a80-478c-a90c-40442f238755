package dev.pigmomo.yhkit2025.api.model.handluck

/**
 * 手气抽奖奖励列表响应数据类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 奖励列表数据
 * @property now 当前时间戳
 */
data class HandLuckRewardListResponse(
    val code: Int = 0,
    val message: String = "",
    val data: List<HandLuckReward>? = null,
    val now: Long = 0
)

/**
 * 手气抽奖奖励数据类
 * @property nickName 用户昵称
 * @property receiveTime 领取时间戳
 * @property luckiest 是否手气最佳(0-否，1-是)
 * @property rewardType 奖励类型
 * @property couponType 优惠券类型
 * @property couponName 优惠券名称
 * @property couponDesc 优惠券描述
 * @property rewardValue 奖励值/金额
 * @property couponLatitude 优惠券纬度
 * @property superposeType 是否可叠加
 * @property swelled 是否已膨胀
 */
data class HandLuckReward(
    val nickName: String = "",
    val receiveTime: Long = 0,
    val luckiest: Int = 0,
    val rewardType: Int = 0,
    val couponType: String = "",
    val couponName: String = "",
    val couponDesc: String = "",
    val rewardValue: String = "",
    val couponLatitude: String = "",
    val superposeType: Boolean = false,
    val swelled: Boolean = false
) 