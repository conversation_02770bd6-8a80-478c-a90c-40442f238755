package dev.pigmomo.yhkit2025.api.model.card

data class CardListResponse(
    val code: Int = 0,
    val message: String = "",
    val data: CardListData? = null,
    val now: Long = 0
)

data class CardListData(
    val total: Int = 0,
    val list: List<CardItem> = emptyList(),
    val pageNum: Int = 0,
    val pageSize: Int = 0,
    val size: Int = 0,
    val startRow: Int = 0,
    val endRow: Int = 0,
    val pages: Int = 0,
    val prePage: Int = 0,
    val nextPage: Int = 0,
    val isFirstPage: Boolean = false,
    val isLastPage: Boolean = false,
    val hasPreviousPage: Boolean = false,
    val hasNextPage: Boolean = false,
    val navigatePages: Int = 0,
    val navigatepageNums: List<Int> = emptyList(),
    val navigateFirstPage: Int = 0,
    val navigateLastPage: Int = 0
)

data class CardItem(
    val id: String = "",
    val cardBalance: String = "",
    val userId: String = "",
    val userPhone: String = "",
    val cancelLimit: String = "",
    val cardNo: String = "",
    val physicalCardNo: String = "",
    val custSeq: String = "",
    val cardCover: String = "",
    val cardAmount: Double = 0.0,
    val cardTitle: String = "",
    val resources: String = "",
    val state: Int = 0,
    val storeInfo: String = "",
    val purchaseTime: String = "",
    val receiveTime: String = "",
    val receiverPhone: String = "",
    val sendTime: String = "",
    val sendTimestamp: Long = 0,
    val createTime: String = "",
    val lastUpdatedAt: String = "",
    val purchaseChannel: Int = 0,
    val orderId: String = "",
    val coverId: String = "",
    val giveType: Int = 1,
    val background: String? = null,
    val canGift: Boolean = false,
    val bindResource: Int = 0,
    val categoryCard: Int = 0,
    val groupBuyingId: Long = 0,
    val description: String? = null,
    val receivePhone: String = ""
) 