package dev.pigmomo.yhkit2025.api.utils.login

import android.util.Log
import java.util.UUID
import kotlin.random.Random

/**
 * 提供设备相关信息的工具对象
 */
object DeviceInfoProvider {
    private val phones = mapOf(
        "Redmi" to listOf(
            "24129RT7CC", "24122RKC7C", "24117RK2CC", "2407FRK8EC", "23113RKC6C",
            "23117RK66C", "23013RK75C", "M2012K11AC", "22127RK46C", "23078RKD5C"
        ),
        "Samsung" to listOf(
            "SM-S9380", "SM-S9180", "SM-S9280", "SM-S9110", "SM-S9310", "SM-S9210",
            "SM-F9360", "SM-F7210", "SM-F9560", "SM-F7410"
        ),
        "OnePlus" to listOf(
            "PKG110", "PKR110", "PJZ110", "PJX110", "PJF110", "PJE110",
            "PJA110", "PHB110", "PHK110", "PGP110"
        ),
        "HUAWEI" to listOf(
            "PLR-AL00", "HBN-AL10", "HBP-AL00", "BRE-AL00", "LYA-AL00",
            "EVR-AL00", "CET-AL00", "VTR-AL00", "CLT-AL00", "ELS-AN00"
        ),
        "Xiaomi" to listOf(
            "25019PNF3C", "2201123C", "2201122C", "M2011K2G", "23127PN0CC",
            "24129PN74C", "MCE8", "M2001J2E", "M2002J9E", "M2007J1SC", "2410DPN6CC"
        ),
        "OPPO" to listOf(
            "PKB110", "PHY110", "PKH110", "PJV110", "PHU110", "PGX110",
            "PDHM00", "PAAM00", "PJS110", "PJC110", "PGJM10"
        ),
        "vivo" to listOf(
            "V2366GA", "V2227A", "V2183A", "V2323A", "V2283A", "V2343A",
            "V1813A", "V1732A", "V1911A", "V1818A"
        ),
        "realme" to listOf(
            "RMX2176", "RMX2051", "RMX3800", "RMX3820", "RMX5060", "RMX3852",
            "RMX3478", "RMX3952", "RMX3751", "RMX3992"
        ),
        "Motorola" to listOf("XT2125-4", "XT1970-5", "XT2301-5"),
        "MEIZU" to listOf("M575", "U685Q", "U680A", "M2111")
    )

    private val screens = listOf(
        "720x1280", "1080x1920", "1440x2560", "1080x2340", "1080x2400",
        "1440x2960", "1440x3200", "1170x2532", "1284x2778", "1125x2436",
        "1242x2688", "828x1792"
    )

    fun generate(deviceId: String = ""): BaseLoginUtils.DeviceInfo {
        val brand = phones.keys.random()
        val model = phones[brand]!!.random()
        val osVersion = "android${Random.Default.nextInt(30, 35)}"
        return BaseLoginUtils.DeviceInfo(
            brand = brand,
            model = model,
            channel = brand.lowercase(),
            deviceId = deviceId.ifEmpty { UUID.randomUUID().toString() },
            screen = screens.random(),
            osVersion = osVersion,
            uaOsVersion = osVersion.replace("android", "Android ")
        )
    }

    /**
     * 解析appParam
     * @param param 参数，格式为：$channel,$screen,$deviceId,$distinctId,$osVersion,$model,${Constants.NETWORK_TYPE},${brand},${Constants.APP_VERSION}
     * @return 返回jysessionid,distinctId,deviceInfo
     */
    fun praseAppParam(param: String): Triple<String, String, BaseLoginUtils.DeviceInfo> {
        val parts = param.split(",")
        if (parts.size < 9) {
            Log.e("DeviceInfoProvider", "Invalid appParam format: $param")
            throw IllegalArgumentException("Invalid appParam format")
        }

        val jysessionid = UUID.randomUUID().toString()
        val distinctId = parts[3]
        val deviceInfo = BaseLoginUtils.DeviceInfo(
            brand = parts[7],
            model = parts[5],
            channel = parts[0],
            deviceId = parts[2],
            screen = parts[1],
            osVersion = parts[4],
            uaOsVersion = "Android ${parts[4].replace("android", "")}"
        )
        return Triple(jysessionid, distinctId, deviceInfo)
    }
}