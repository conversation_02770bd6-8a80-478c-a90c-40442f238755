package dev.pigmomo.yhkit2025.api.model.order

/**
 * 售后订单列表响应
 * 用于解析获取售后订单列表接口返回数据
 */
data class AfterSalesListResponse(
    val code: Int = 0,
    val message: String = "",
    val data: AfterSalesListData? = null,
    val now: Long = 0
)

/**
 * 售后订单列表数据
 * 包含分页信息和售后订单列表
 */
data class AfterSalesListData(
    val page: Int = 0,
    val pagecount: Int = 0,
    val totalcount: Int = 0,
    val list: List<AfterSalesOrder> = emptyList(),
    val message: String = ""
)

/**
 * 售后订单信息
 * 包含售后单基本信息和商品列表
 */
data class AfterSalesOrder(
    val id: String = "",
    val statusdesc: String = "",
    val applytime: Long = 0,
    val amount: Int = 0,
    val type: String = "",
    val typedesc: String = "",
    val actionurl: String = "",
    val list: List<AfterSalesOrderItem> = emptyList(),
    val count: Double = 0.0,
    val isOfflineOrder: Boolean = false
)

/**
 * 售后订单商品信息
 * 包含商品基本信息和图片
 */
data class AfterSalesOrderItem(
    val title: String = "",
    val imageurl: String = ""
) 