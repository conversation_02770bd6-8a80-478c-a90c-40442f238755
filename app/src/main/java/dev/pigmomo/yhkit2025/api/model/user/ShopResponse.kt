package dev.pigmomo.yhkit2025.api.model.user

data class ShopResponse(
    val code: Int = 0,
    val message: String = "",
    val data: ShopData? = null,
    val now: Long = 0
)

data class ShopData(
    val city: CityInfo? = null,
    val seller: List<ShopInfo> = emptyList(),
    val address: List<AddressInfo> = emptyList(),
    val popuptype: PopupType? = null,
    val area: AreaInfo? = null,
    val fallbackFlag: Int = 0
)

data class CityInfo(
    val id: String = "",
    val name: String = "",
    val isopen: Int = 0,
    val ismulticity: Int = 0,
    val location: LocationInfo? = null,
    val isordercity: Int = 0,
    val isgroupbuy: Int = 0,
    val cityuicontrol: Int = 0
)

data class ShopInfo(
    val sellerid: Int = 0,
    val shopid: String = "",
    val shopname: String = "",
    val sellername: String = "",
    val title: String = "",
    val longitude: String = "",
    val latitude: String = "",
    val imgurl: String = "",
    val isdelivery: Int = 0,
    val distance: Int = 0,
    val slogan: String = "",
    val inverseimgurl: String = "",
    val issimilarsearch: Int = 0,
    val serviceenter: ServiceInfo? = null,
    val width: Int = 0,
    val height: Int = 0,
    val businessstart: String = "",
    val businessend: String = "",
    val deliverystart: String = "",
    val placeorderend: String = "",
    val pickCutOffTime: String = "",
    val pickBusinessTime: String = "",
    val showPromptTextType: Int = 0,
    val deliveryPromptText: List<String> = emptyList(),
    val showdeliverytime: String = "",
    val isDeliveryToday: Int = 0
)

data class AddressInfo(
    val id: String = "",
    val name: String = "",
    val gender: String = "",
    val phone: String = "",
    val address: AddressInfoDetail = AddressInfoDetail(),
    val location: LocationInfo? = null,
    val isdefault: Int = 0
)

data class AddressInfoDetail(
    val city: String = "",
    val cityid: Int = 0,
    val area: String = "",
    val detail: String = "",
    val district: String = ""
)

data class LocationInfo(
    val lat: String = "",
    val lng: String = ""
)

data class PopupType(
    val type: Int = 0,
    val desc: String = ""
)

data class AreaInfo(
    val name: String = ""
)

data class ServiceInfo(
    val action: String = ""
) 