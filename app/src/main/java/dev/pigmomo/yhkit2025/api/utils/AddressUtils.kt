package dev.pigmomo.yhkit2025.api.utils

import android.annotation.SuppressLint
import android.util.Log
import java.util.UUID

/**
 * 地址工具类
 * 用于生成和处理地址相关的信息
 */
object AddressUtils {
    private const val TAG = "AddressUtils"

    /**
     * 生成随机中文姓名
     * @return 随机生成的中文姓名
     */
    fun randomName(): String {
        val firstName = listOf(
            "李", "张", "王", "刘", "陈", "黄", "周", "吴", "徐", "孙",
            "何", "郭", "赵", "田", "冯", "廖", "唐", "郑", "林", "蔡",
            "彭", "蒋", "吕", "苏", "丁", "沈", "朱", "许", "邓", "潘",
            "韦", "贾", "倪", "邱", "陶", "姜", "程", "洪", "卓", "曾",
            "袁", "肖", "杭", "连", "万", "于", "钟", "曹", "卫", "龙",
            "郎", "谭", "安", "殷", "戚", "庞", "崔", "叶", "范", "纪",
            "邵", "高", "舒", "梅", "任", "钱", "华", "贺", "尹", "荣",
            "霍", "白", "毛", "段", "甘", "时", "甄", "龚", "陆", "涂",
            "饶", "游", "马", "沈", "吕", "伍", "温"
        )
        
        val lastName = listOf(
            "伟", "刚", "勇", "辉", "敏", "杰", "超", "丽", "静", "洋",
            "乐", "婷", "雪", "霞", "丹", "媛", "娜", "欣", "婉", "武",
            "栋", "健", "斌", "东", "明", "志", "豪", "铭", "翔", "毅",
            "涛", "鹏", "雄", "宇", "浩", "舟", "莉", "颖", "晓", "珊",
            "莹", "雅", "文", "凤", "璐", "梅", "楠", "璇", "彤", "妍",
            "雯", "晨", "小伟", "晓冉", "金伟", "建华", "明辉", "志强", "伟强", "丽华", 
            "雅婷", "俊杰"
        )
        
        // 返回单姓或者姓+名的组合
        return if ((0..1).random() == 0) {
            firstName.random()
        } else {
            firstName.random() + lastName.random()
        }
    }

    /**
     * 从地址字符串中提取单元号、楼栋号和楼层号
     * @param input 包含地址信息的字符串
     * @return 包含单元号、楼栋号和楼层号的Triple对象
     */
    fun extractDetails(input: String): Triple<String?, String?, String?> {
        // 提取单元号
        val unitNumber = Regex("(\\d*)单元").find(input)?.groups?.get(1)?.value.orEmpty()
        
        // 提取楼栋号
        val buildingNumber = Regex("(\\d*)栋").find(input)?.groups?.get(1)?.value.orEmpty()
        
        // 提取楼层号和门牌号
        val floorAndRoomMatch = Regex("(\\d+)-(\\d+)号").find(input)
        val floorNumber = if (floorAndRoomMatch != null) {
            "${floorAndRoomMatch.groups[1]?.value}-${floorAndRoomMatch.groups[2]?.value}"
        } else {
            ""
        }
        
        return Triple(unitNumber, buildingNumber, floorNumber)
    }

    /**
     * 替换地址详情
     * @param original 原始JSON字符串
     * @param randomDetailsRegex 包含地址格式信息的字符串
     * @return 替换了地址详情的JSON字符串
     */
    fun replaceDetail(original: String, randomDetailsRegex: String): String {
        // 定义匹配地址详情的正则表达式
        val detailPattern = "\"detail\":\"[^\"]*\"".toRegex()
        
        // 提取原有地址中的信息
        val detailsTriple = extractDetails(randomDetailsRegex)
        val unitNumber = detailsTriple.first
        val buildingNumber = detailsTriple.second
        val floorNumber = detailsTriple.third
        
        // 生成随机数据
        val unitNum = (1..15).random()
        val poleNum = (1..10).random()
        val floor = (1..15).random()
        val houseNum = (1..10).random()
        
        // 构建新的地址详情
        val newDetailParts = mutableListOf<String>()
        
        // 添加单元信息
        if (randomDetailsRegex.contains("单元")) {
            newDetailParts.add(
                if (unitNumber?.isNotEmpty() == true) "${unitNumber}单元" 
                else "${unitNum}单元"
            )
        }
        
        // 添加楼栋信息
        if (randomDetailsRegex.contains("栋")) {
            newDetailParts.add(
                if (buildingNumber?.isNotEmpty() == true) "${buildingNumber}栋" 
                else "${poleNum}栋"
            )
        }
        
        // 添加楼层和房间号信息
        if (randomDetailsRegex.contains("号")) {
            newDetailParts.add(
                if (floorNumber?.isNotEmpty() == true && floorNumber != "-") "${floorNumber}号" 
                else "${floor}-${houseNum}号"
            )
            
            // 添加固定后缀
            val suffixMatch = Regex("号(\\w*)").find(randomDetailsRegex)
            if (suffixMatch?.groups?.get(1)?.value?.isNotEmpty() == true) {
                newDetailParts.add(suffixMatch.groups[1]?.value ?: "")
            }
        }
        
        // 如果没有匹配到任何标记，则直接使用原始字符串
        if (newDetailParts.isEmpty() && 
            !randomDetailsRegex.contains("单元") && 
            !randomDetailsRegex.contains("栋") && 
            !randomDetailsRegex.contains("号")) {
            newDetailParts.add(randomDetailsRegex)
        }
        
        val newDetail = newDetailParts.joinToString("")
        Log.d(TAG, "Generated new address detail: $newDetail")
        
        // 替换原字符串中的详情
        return original.replace(detailPattern, "\"detail\":\"$newDetail\"")
    }

    /**
     * 调整小数点后三位数字
     * @param value 原始数值字符串
     * @return 调整后的数值字符串
     */
    @SuppressLint("DefaultLocale")
    fun adjustLastThreeDigits(value: String): String {
        // 将字符串分割为整数部分和小数部分
        val parts = value.split(".")
        val integerPart = parts[0]
        
        // 确保小数部分至少有6位，不足则补0
        val decimalPart = if (parts.size > 1) parts[1].padEnd(6, '0') else "000000"
        
        // 生成随机的三位数，用于替换小数点后的最后三位数字
        val adjustment = (0..999).random().toString().padStart(3, '0')
        
        // 生成新的小数部分：保留前(length-3)位，追加随机生成的3位数
        val adjustedDecimalPart = decimalPart.substring(0, decimalPart.length - 3) + adjustment
        
        // 格式化并返回结果
        return String.format("%s.%s", integerPart, adjustedDecimalPart)
    }

    /**
     * 替换JSON字符串中的UUID值
     * @param jsonStr JSON字符串
     * @return 替换UUID后的JSON字符串
     */
     fun replaceUUID(jsonStr: String): String {
        val uuidPattern = "\"_uuid\":\"[^\"]*\"".toRegex()
        val newUuid = UUID.randomUUID().toString()
        return jsonStr.replace(uuidPattern, "\"_uuid\":\"$newUuid\"")
    }

    /**
     * 将XYHBizParams转换为WebXYHBizParams
     *
     * @param xyhBizParams 原始XYH业务参数
     * @return Web格式的XYH业务参数
     */
    fun matchAndAssignParamsByXYHBizParamsCommon(xyhBizParams: String): String {
        // 定义正则表达式模式以提取每个参数的值
        val ncjkdyPattern = "ncjkdy=([^&]*)".toRegex()
        val nzggzmdyPattern = "nzggzmdy=([^&]*)".toRegex()
        val xdotdyPattern = "xdotdy=([^&]*)".toRegex()
        val gibPattern = "gib=([^&]*)".toRegex()
        val gvoPattern = "gvo=([^&]*)".toRegex()
        //val vkkdyPattern = "vkkdy=([^&]*)".toRegex()

        // 从XYHBizParamsCommon字符串中提取值
        val ncjkdy = ncjkdyPattern.find(xyhBizParams)?.groupValues?.get(1) ?: ""
        val nzggzmdy = nzggzmdyPattern.find(xyhBizParams)?.groupValues?.get(1) ?: ""
        val xdotdy = xdotdyPattern.find(xyhBizParams)?.groupValues?.get(1) ?: ""
        val gib = gibPattern.find(xyhBizParams)?.groupValues?.get(1) ?: ""
        val gvo = gvoPattern.find(xyhBizParams)?.groupValues?.get(1) ?: ""
        //val vkkdy = vkkdyPattern.find(xyhBizParams)?.groupValues?.get(1) ?: ""

        // 创建一个映射来保存值
        val webXYHBizParamsCommon = mapOf(
            "ncjkdy" to ncjkdy,
            "nzggzmdy" to nzggzmdy,
            "xdotdy" to xdotdy,
            "gib" to gib,
            "gvo" to gvo,
            //"vkkdy" to vkkdy
        )

        // 将映射连接成字符串格式
        return webXYHBizParamsCommon.entries.joinToString("&") { "${it.key}=${it.value}" }
    }
} 