package dev.pigmomo.yhkit2025.api.model.order

/**
 * 小部件帮助类
 * 封装处理小部件的常用功能
 */
object WidgetHelper {
    
    /**
     * 查找指定名称的小部件
     * @param orderPlaceWidgets 小部件列表
     * @param name 小部件名称
     * @return 找到的小部件，如果不存在则返回null
     */
    private fun findWidgetByName(orderPlaceWidgets: List<OrderPlaceWidget>, name: String): OrderPlaceWidget? {
        return orderPlaceWidgets.firstOrNull { it.name == name }
    }
    
    /**
     * 查找指定类型的小部件
     * @param orderPlaceWidgets 小部件列表
     * @param type 小部件类型
     * @return 找到的小部件列表
     */
    fun findWidgetsByType(orderPlaceWidgets: List<OrderPlaceWidget>, type: String): List<OrderPlaceWidget> {
        return orderPlaceWidgets.filter { it.widgettype == type }
    }
    
    /**
     * 获取地址小部件数据
     * @param orderData 订单数据
     * @return 地址小部件数据，如果不存在则返回null
     */
    fun getAddressData(orderData: OrderPlaceData): AddressWidgetData? {
        val addressWidget = findWidgetByName(orderData.widgets, "address")
        return addressWidget?.let { 
            WidgetDataParser.parseWidgetData(it) as? AddressWidgetData
        }
    }
    
    /**
     * 获取包裹小部件数据
     * @param orderData 订单数据
     * @return 包裹小部件数据，如果不存在则返回null
     */
    fun getPackagesData(orderData: OrderPlaceData): PackagesWidgetData? {
        val packagesWidget = findWidgetByName(orderData.widgets, "packages")
        return packagesWidget?.let { 
            WidgetDataParser.parseWidgetData(it) as? PackagesWidgetData
        }
    }

    /**
     * 获取组合支付小部件数据
     * @param orderData 订单数据
     * @return 组合支付小部件数据，如果不存在则返回null
     */
    fun getCombinPayData(orderData: OrderPlaceData): CombinPayWidgetData? {
        val combinpayWidget = findWidgetByName(orderData.widgets, "combinpay")
        return combinpayWidget?.let {
            WidgetDataParser.parseWidgetData(it) as? CombinPayWidgetData
        }
    }
    
    /**
     * 获取价格信息组小部件
     * @param orderData 订单数据
     * @return 价格信息组小部件数据，如果不存在则返回null
     */
    fun getPriceInfoGroupData(orderData: OrderPlaceData): GroupWidgetData? {
        val priceInfoWidget = findWidgetByName(orderData.widgets, "priceinfo")
        return priceInfoWidget?.let {
            WidgetDataParser.parseWidgetData(it) as? GroupWidgetData
        }
    }
    
    /**
     * 从组小部件中获取子小部件
     * @param groupData 组小部件数据
     * @param name 子小部件名称
     * @return 子小部件，如果不存在则返回null
     */
    private fun getSubWidgetFromGroup(groupData: GroupWidgetData?, name: String): OrderPlaceWidget? {
        return groupData?.data?.firstOrNull { it.name == name }
    }
    
    /**
     * 从组小部件中获取子小部件数据
     * @param groupData 组小部件数据
     * @param name 子小部件名称
     * @return 子小部件数据，如果不存在则返回null
     */
    fun <T : BaseWidgetData> getSubWidgetDataFromGroup(groupData: GroupWidgetData?, name: String): T? {
        val subWidget = getSubWidgetFromGroup(groupData, name)
        return subWidget?.let {
            @Suppress("UNCHECKED_CAST")
            WidgetDataParser.parseWidgetData(it) as? T
        }
    }
} 