package dev.pigmomo.yhkit2025.api.model.boostcoupon

data class BoostCouponListResponse(
    val code: Int = 0,
    val message: String? = null,
    val data: BoostCouponData? = null,
    val now: Long = 0
)

data class BoostCouponData(
    val records: List<BoostCouponRecord> = emptyList(),
    val total: Int = 0,
    val size: Int = 0,
    val current: Int = 0,
    val pages: Int = 0
)

data class BoostCouponRecord(
    val prizeGameDTO: PrizeGameDTO? = null,
    val boostCouponVO: BoostCouponVO? = null,
    val wechatUserInfoVOS: List<Any>? = null
)

data class PrizeGameDTO(
    val prizeId: Int = 0,
    val activityCode: String = "",
    val enableFrom: Long = 0,
    val enableTo: Long = 0,
    val couponActivityCode: String = "",
    val promotionCode: String = "",
    val boosterType: Int = 0,
    val gameCode: String? = null,
    val prizeGameStatus: Int = 0,
    val rewardSendStatus: Int = 0,
    val rewardSendResult: String? = null,
    val needBoostNum: Int = 0,
    val remainBoostNum: Int = 0,
    val boostExpireAt: Long? = null,
    val weight: Int = 0,
    val gameItemDTOS: Any? = null,
    val activityTitle: String = "",
    val boostShareUrl: String = "",
    val shareTitle: String = "",
    val initLimitNum: Int = 0,
    val showType: Int = 0,
    val canApply: Int? = null
)

data class BoostCouponVO(
    val promotionCode: String = "",
    val couponCode: String? = null,
    val name: String = "",
    val amount: String = "",
    val enabled: Boolean = false,
    val couponDescription: String = "",
    val couponDescription2: String? = null,
    val catalogCode: String = "",
    val applicationScope: String = "",
    val applicationPlatform: String? = null,
    val couponStatus: String? = null,
    val deliveryLimit: Int = 0,
    val actionUrl: String = "",
    val startTime: Long = 0,
    val endTime: Long = 0,
    val remark: String? = null,
    val skus: Any? = null,
    val deliveryTime: String? = null,
    val detailList: List<String> = emptyList(),
    val skuUrl: String? = null,
    val skuTitle: String? = null,
    val currPrice: String? = null,
    val underlinePrice: String? = null,
    val receivedCount: Int = 0,
    val availableCount: Int = 0,
    val isRedEnvelope: Boolean = false,
    val expireAfterXDay: String? = null,
    val unit: String = "",
    val canApply: Int = 0,
    val couponImageUrl: String = ""
) 