package dev.pigmomo.yhkit2025.api.service

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestHelper
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.SignType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 用户服务类
 * 提供用户相关的API调用方法
 */
class UserService(requestHelper: RequestHelper) : BaseService(requestHelper) {

    private val tag = "UserService"

    private val serviceType = requestHelper.getServiceType()

    /**
     * 获取用户信息
     * @return 用户信息响应结果
     */
    suspend fun getUserInfo(): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val commonParams = requestHelper.getAppCommonParams()

                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getUserInfo: xyhBizParams empty, config may not be set")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                val businessParams = buildAppBusinessParams()
                // 可以不添加 abdata 参数
                //businessParams["abdata"] = "{\"couponbag\":\"b\",\"couponbagA\":\"b\"}"

                val urlWithParams =
                    buildAppApiUrl(RequestConfig.Path.USER_INFO_PATH, businessParams, commonParams)
                val needSignStr =
                    urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                        .replace("=", "")
                        .replace("&", "")

                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "getUserInfo: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"
                val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                requestHelper.get(fullUrl, headers)
            }

            "mini" -> {
                val commonParams = requestHelper.getMiniProgramCommonParams()

                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getUserInfo: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                val businessParams = buildMiniProgramBusinessParams()
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.USER_INFO_PATH,
                        businessParams,
                        commonParams,
                        "activity"
                    )

                val sign = requestHelper.generateSign(
                    urlWithParams,
                    "",
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.get(fullUrl, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 获取账户类型信息
     * @return 账户类型信息响应结果
     */
    suspend fun checkAccountType(): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val cityid = RequestConfig.CheckAccountType.CITYID
                val sellerid = RequestConfig.CheckAccountType.SELLERID
                val shopid = RequestConfig.CheckAccountType.SHOPID
                val commonParams = requestHelper.getAppCommonParams()

                // 构建业务参数
                val businessParams = buildAppBusinessParams()
                businessParams.put("cityid", cityid)
                businessParams.put("sellerid", sellerid)
                businessParams.put("shopid", shopid)
                businessParams.put("received", "0")
                businessParams.put("pickself", "0")
                businessParams.put("proportion", "2.88")
                businessParams.put("navTabType", "1")
                businessParams.put("showmultiseller", "{\"$sellerid\":\"$shopid\"}")
                businessParams.put("elderly", "0")
                businessParams.put(
                    "abdata",
                    "{\"home_page_new_people_850\":\"A\",\"homepage-slice-activityid-1060\":\"1\"}"
                )

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.FP_HOMEPAGE_PATH,
                        businessParams,
                        commonParams
                    )

                val needSignStr =
                    urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                        .replace("=", "")
                        .replace("&", "").replace(
                            "%7B%22$sellerid%22%3A%22$shopid%22%7D",
                            "{\"$sellerid\":\"$shopid\"}"
                        ).replace(
                            "%7B%22home_page_new_people_850%22%3A%22A%22%2C%22homepage-slice-activityid-1060%22%3A%221%22%7D",
                            "{\"home_page_new_people_850\":\"A\",\"homepage-slice-activityid-1060\":\"1\"}"
                        )

                // 生成签名
                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "accountType: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams =
                    requestHelper.getXyhBizParams()
                        .ifEmpty { "gvo=&gib=&xdotdy=&vkkdy=dpnrxsquou_*a_++ix" }
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "accountType: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                requestHelper.get(fullUrl, headers)
            }

            "mini" -> {
                val cityid = RequestConfig.CheckAccountType.CITYID
                val sellerid = RequestConfig.CheckAccountType.SELLERID
                val shopid = RequestConfig.CheckAccountType.SHOPID
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["cityid"] = cityid
                businessParams["sellerid"] = sellerid
                businessParams["shopid"] = shopid
                businessParams["received"] = "0"
                businessParams["pickself"] = "0"
                businessParams["proportion"] = "2.88"
                businessParams["navTabType"] = "1"
                businessParams["showmultiseller"] = "{\"$sellerid\":\"$shopid\"}"
                businessParams["elderly"] = "0"
                businessParams["abdata"] =
                    "{\"home_page_new_people_850\":\"A\",\"homepage-slice-activityid-1060\":\"1\"}"
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.FP_HOMEPAGE_PATH_MINI,
                        businessParams,
                        commonParams,
                        "activity"
                    )

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    "",
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                    .ifEmpty { "gvo=gib=&vyymznnMy=&vkkdy=rsx,xa(x,*!,,zz'\$!&xdotdy=&ncjkdy=&nzggzmdy=&vmzv=" }
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "accountType: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.get(fullUrl, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 利用获取优惠券检查活动是否能参与
     * @return 活动检查响应结果
     */
    suspend fun checkActivity(): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()

                // 添加额外参数
                businessParams["proportion"] = "3.5"
                businessParams["pagesize"] = "6"
                businessParams["aid"] = "67ab00743dc0c20007aba4ce"
                businessParams["versionpro"] = "2"
                businessParams["salesChannel"] = ""
                businessParams["uid"] = requestHelper.getUid()
                businessParams["showmultiseller"] = ""
                businessParams["shopName"] = ""
                businessParams["isOldEdition"] = "false"
                businessParams["userid"] = requestHelper.getUid()
                businessParams["longitude"] = ""
                businessParams["latitude"] = ""

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.CMS_ACTIVITY_REST_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建请求体
                val requestBody =
                    """{"promotioncode":"encrypt@%1E%0Co%1Cpoh%04%05po%04%04%07%1F%17%0Fph%07%0Cpe","securityversion":"tc_v3","captchaticket":"","captcharandstr":"","assemblyid":"67ada6e7236ec20007b1726d","resourceid":"VJeKy6OL_","couponamount":-100,"couponcatalog":6,"coupondesc":"打折券","orderminamount":800}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(urlWithParams, requestBody, SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val webXyhBizParams = requestHelper.getWebXyhBizParams()
                if (webXyhBizParams.isEmpty()) {
                    Log.e(tag, "checkActivity: webXyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("webXyhBizParams not set"))
                }

                val headers = buildAppStandardHeaders(webXyhBizParams, true, false)

                // 发送请求
                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramWebBusinessParams()

                // 添加额外参数
                businessParams["proportion"] = "3.5"
                businessParams["pagesize"] = "6"
                businessParams["aid"] = "67ab00743dc0c20007aba4ce"
                businessParams["versionpro"] = "2"
                businessParams["salesChannel"] = ""
                businessParams["uid"] = requestHelper.getUid()
                businessParams["showmultiseller"] = ""
                businessParams["shopName"] = ""
                businessParams["isOldEdition"] = "false"
                businessParams["userid"] = requestHelper.getUid()
                businessParams["longitude"] = ""
                businessParams["latitude"] = ""
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.CMS_ACTIVITY_REST_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                // 构建请求体
                val requestBody =
                    """{"promotioncode":"encrypt@%1E%0Co%1Cpoh%04%05po%04%04%07%1F%17%0Fph%07%0Cpe","securityversion":"tc_v3","captchaticket":"","captcharandstr":"","assemblyid":"67ada6e7236ec20007b1726d","resourceid":"VJeKy6OL_","couponamount":-100,"couponcatalog":6,"coupondesc":"打折券","orderminamount":800}"""

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM_WEB_ACTIVITY.value
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "checkActivity: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                // 发送请求
                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }
}