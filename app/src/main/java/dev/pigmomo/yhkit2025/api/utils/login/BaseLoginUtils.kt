package dev.pigmomo.yhkit2025.api.utils.login

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.utils.HttpProxyUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.Credentials
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.net.InetSocketAddress
import java.net.Proxy
import java.util.concurrent.TimeUnit

/**
 * 登录功能的基础工具类，提供共享的登录相关方法
 */
abstract class BaseLoginUtils {

    /**
     * 公共常量
     */
    protected object Constants {
        const val BASE_URL = "https://api.yonghuivip.com"
        const val TICKET_SERVICE_URL = "http://uersedfo.enhancer.cn/fa916dbc479a799b"
        const val APP_VERSION = RequestConfig.AppVersion.VERSION
        const val APP_VERSION_CODE = RequestConfig.AppVersion.VERSION_NUMBER
        const val NETWORK_TYPE = "WIFI"
        const val UA_TEMPLATE = "YhStore/%s cn.yonghui.hyd/%s (client/phone; %s; %s/%s)"
        const val X_YH_CONTEXT = "origin=android&morse=1"
        const val CONTENT_TYPE = "application/json; charset=UTF-8"
        const val PRODUCT_LINE = "YhStore"
        const val PLATFORM = "Android"
        const val CHANNEL_MAIN = "official"
    }

    /**
     * 内部数据类，用于封装设备信息
     */
    data class DeviceInfo(
        val brand: String,
        val model: String,
        val channel: String,
        val deviceId: String,
        val screen: String,
        val osVersion: String,
        val uaOsVersion: String
    )

    /**
     * 内部数据类，用于封装从远程服务获取的安全票据
     */
    protected data class SecurityTicket(val ticket: String, val randstr: String)

    /**
     * 从2Captcha获取安全票据
     *
     * @return RequestResult 包含SecurityTicket或错误信息
     */
    protected suspend fun fetchSecurityTicketForm2Captcha(): RequestResult<SecurityTicket> =
        withContext(Dispatchers.IO) {
            try {
                // 2Captcha API配置
                val apiKey = "47cdca8f460615ec957b6fffb02c8f80" // 需要替换为实际的2Captcha API密钥
                val baseUrl = "https://2captcha.com/createTask"
                val resultUrl = "https://2captcha.com/getTaskResult"

                // 创建OkHttpClient实例
                val client = OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .build()

                // 构建创建任务的请求体
                val createTaskJson = JSONObject().apply {
                    put("clientKey", apiKey)
                    put("task", JSONObject().apply {
                        put("type", "TencentTaskProxyless")
                        put("websiteURL", "https://turing.captcha.qcloud.com")
                        put("appId", "2003848717") // 永辉生活APP的腾讯验证码appId
                    })
                }

                // 发送创建任务请求
                val createTaskRequest = Request.Builder()
                    .url(baseUrl)
                    .post(
                        createTaskJson.toString()
                            .toRequestBody("application/json; charset=utf-8".toMediaType())
                    )
                    .build()

                val createTaskResponse = client.newCall(createTaskRequest).execute()
                val createTaskResponseBody = createTaskResponse.body?.string()

                if (!createTaskResponse.isSuccessful || createTaskResponseBody.isNullOrEmpty()) {
                    Log.e(
                        "fetchSecurityTicket2Captcha",
                        "Failed to create task, response code: ${createTaskResponse.code}"
                    )
                    return@withContext RequestResult.Error(Exception("Failed to create task, response code: ${createTaskResponse.code}"))
                }

                // 解析任务ID
                val createTaskResult = JSONObject(createTaskResponseBody)
                if (createTaskResult.optInt("errorId", -1) != 0) {
                    val errorMessage =
                        createTaskResult.optString("errorDescription", "Unknown error")
                    Log.e("fetchSecurityTicket2Captcha", "Error creating task: $errorMessage")
                    return@withContext RequestResult.Error(Exception("Error creating task: $errorMessage"))
                }

                val taskId = createTaskResult.optString("taskId")
                if (taskId.isEmpty()) {
                    Log.e("fetchSecurityTicket2Captcha", "No task ID in response")
                    return@withContext RequestResult.Error(Exception("No task ID in response"))
                }

                // 轮询获取结果
                val maxAttempts = 10
                var attempts = 0

                while (attempts < maxAttempts) {
                    // 等待一段时间再查询结果
                    Thread.sleep(6000)
                    attempts++

                    // 构建获取结果的请求体
                    val getResultJson = JSONObject().apply {
                        put("clientKey", apiKey)
                        put("taskId", taskId)
                    }

                    // 发送获取结果请求
                    val getResultRequest = Request.Builder()
                        .url(resultUrl)
                        .post(
                            getResultJson.toString()
                                .toRequestBody("application/json; charset=utf-8".toMediaType())
                        )
                        .build()

                    val getResultResponse = client.newCall(getResultRequest).execute()
                    val getResultResponseBody = getResultResponse.body?.string()

                    if (!getResultResponse.isSuccessful || getResultResponseBody.isNullOrEmpty()) {
                        continue
                    }

                    // 解析结果
                    val getResultResult = JSONObject(getResultResponseBody)
                    if (getResultResult.optInt("errorId", -1) != 0) {
                        val errorMessage =
                            getResultResult.optString("errorDescription", "Unknown error")
                        Log.e("fetchSecurityTicket2Captcha", "Error getting result: $errorMessage")
                        return@withContext RequestResult.Error(Exception("Error getting result: $errorMessage"))
                    }

                    val status = getResultResult.optString("status")
                    if (status == "processing") {
                        // 任务仍在处理中，继续等待
                        continue
                    } else if (status == "ready") {
                        // 任务已完成，获取结果
                        val solution = getResultResult.optJSONObject("solution")
                        if (solution != null) {
                            val ticket = solution.optString("ticket")
                            val randstr = solution.optString("randstr")

                            if (ticket.isNotEmpty() && randstr.isNotEmpty()) {
                                Log.d(
                                    "fetchSecurityTicket2Captcha",
                                    "Successfully got ticket from 2Captcha"
                                )
                                return@withContext RequestResult.Success(
                                    SecurityTicket(
                                        ticket,
                                        randstr
                                    )
                                )
                            }
                        }

                        Log.e("fetchSecurityTicket2Captcha", "Invalid solution format")
                        return@withContext RequestResult.Error(Exception("Invalid solution format"))
                    }
                }

                // 超过最大尝试次数
                Log.e("fetchSecurityTicket2Captcha", "Max attempts reached, task still processing")
                return@withContext RequestResult.Error(Exception("Max attempts reached, task still processing"))

            } catch (e: Exception) {
                Log.e("fetchSecurityTicket2Captcha", "Error getting ticket from 2Captcha", e)
                return@withContext RequestResult.Error(e)
            }
        }

    /**
     * 创建OkHttpClient实例，可选择性配置代理
     * @param useProxy 是否使用代理
     * @param proxyType 代理类型
     * @return 配置好的OkHttpClient实例
     */
    protected suspend fun createHttpClient(useProxy: Boolean, proxyType: String): OkHttpClient {
        val clientBuilder = OkHttpClient.Builder()
            .connectTimeout(15, TimeUnit.SECONDS)
            .readTimeout(15, TimeUnit.SECONDS)
            .writeTimeout(15, TimeUnit.SECONDS)

        if (useProxy) {
            Log.d("BaseLoginUtils", "Getting proxy from $proxyType")
            val proxyConfig = HttpProxyUtils.getProxyIp(selectProxyConfig = proxyType)
            val (host, port) = proxyConfig.proxyInfo
            val (username, password) = proxyConfig.proxyAccount ?: Pair(null, null)

            if (host != null && port != null) {
                Log.d("BaseLoginUtils", "Using proxy: $host:$port")
                val proxy = Proxy(Proxy.Type.HTTP, InetSocketAddress(host, port))
                clientBuilder.proxy(proxy)

                if (username != null && password != null) {
                    clientBuilder.proxyAuthenticator { _, response ->
                        val credential = Credentials.basic(username, password)
                        response.request.newBuilder()
                            .header("Proxy-Authorization", credential)
                            .build()
                    }
                }
            } else {
                Log.e("BaseLoginUtils", "Failed to get a valid proxy, aborting login.")
                throw IllegalStateException("Failed to get a valid proxy, aborting login.")
            }
        }
        return clientBuilder.build()
    }

    /**
     * 构建通用的URL参数
     */
    protected fun buildCommonUrlParams(
        jysessionid: String,
        deviceInfo: DeviceInfo,
        distinctId: String,
        timestamp: Long
    ): Map<String, String> {
        return mapOf(
            "jysessionid" to jysessionid,
            "os" to "android",
            "elderly" to "0",
            "channel" to deviceInfo.channel,
            "screen" to deviceInfo.screen,
            "deviceid" to deviceInfo.deviceId,
            "platform" to Constants.PLATFORM,
            "channelMain" to Constants.CHANNEL_MAIN,
            "productLine" to Constants.PRODUCT_LINE,
            "distinctId" to distinctId,
            "osVersion" to deviceInfo.osVersion,
            "v" to Constants.APP_VERSION,
            "channelSub" to "",
            "model" to deviceInfo.model,
            "networkType" to Constants.NETWORK_TYPE,
            "brand" to deviceInfo.brand,
            "timestamp" to timestamp.toString()
        )
    }

    /**
     * 构建通用的请求头
     */
    protected fun buildCommonHeaders(deviceInfo: DeviceInfo): Map<String, String> {
        val userAgent = String.format(
            Constants.UA_TEMPLATE, Constants.APP_VERSION, Constants.APP_VERSION_CODE,
            deviceInfo.uaOsVersion, deviceInfo.brand, deviceInfo.model
        )

        return mapOf(
            "X-YH-Biz-Params" to RequestConfig.XYH_BIZ_PARAMS_ORIGIN_APP,
            "X-YH-Context" to Constants.X_YH_CONTEXT,
            "User-Agent" to userAgent,
            "DNT" to "0",
            "Content-Type" to Constants.CONTENT_TYPE
        )
    }

    /**
     * 构建URL并添加参数
     */
    protected fun buildUrlWithParams(
        baseUrl: String,
        path: String,
        params: Map<String, String>
    ): String {
        return "$baseUrl$path?" + params.entries.joinToString("&") { "${it.key}=${it.value}" }
    }
} 