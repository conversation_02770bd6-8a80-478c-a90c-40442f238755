package dev.pigmomo.yhkit2025.api.model.coupon

/**
 * 新人专享数据响应类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 新人专享数据
 * @property now 当前时间戳
 */
data class NewPersonAssemblyDataResponse(
    val code: Int = 0,
    val message: String = "",
    val data: NewPersonAssemblyData? = null,
    val now: Long = 0
)

/**
 * 新人专享数据类
 * @property couponResult 优惠券结果
 * @property skusInfo 商品信息列表
 */
data class NewPersonAssemblyData(
    val couponResult: CouponResult? = null,
    val skusInfo: List<SkuInfo>? = null
)

/**
 * 优惠券结果类
 * @property couponVos 优惠券列表
 * @property totalAmount 总金额
 */
data class CouponResult(
    val couponVos: List<CouponVo>? = null,
    val totalAmount: Int = 0
)

/**
 * 优惠券信息类
 * @property promotionCode 促销码
 * @property couponCode 优惠券码
 * @property name 优惠券名称
 * @property amount 优惠券金额
 * @property enabled 是否启用
 * @property couponDescription 优惠券描述
 * @property couponDescription2 优惠券描述2
 * @property catalogCode 目录代码
 * @property applicationScope 适用范围
 * @property applicationPlatform 应用平台
 * @property couponStatus 优惠券状态
 * @property acquiredStatus 获取状态
 * @property deliveryLimit 发放限制
 * @property actionUrl 操作URL
 * @property startTime 开始时间
 * @property endTime 结束时间
 * @property remark 备注
 * @property skus 商品列表
 * @property deliveryTime 发放时间
 * @property detailList 详情列表
 * @property couponLatitude 优惠券纬度
 * @property superposeType 叠加类型
 */
data class CouponVo(
    val promotionCode: String = "",
    val couponCode: String? = null,
    val name: String = "",
    val amount: String? = null,
    val enabled: Boolean = true,
    val couponDescription: String = "",
    val couponDescription2: String? = null,
    val catalogCode: String = "",
    val applicationScope: String = "",
    val applicationPlatform: String? = null,
    val couponStatus: String? = null,
    val acquiredStatus: String = "",
    val deliveryLimit: Int = 0,
    val actionUrl: String = "",
    val startTime: Long = 0,
    val endTime: Long = 0,
    val remark: String? = null,
    val skus: List<String>? = null,
    val deliveryTime: String? = null,
    val detailList: List<String>? = null,
    val couponLatitude: String = "",
    val superposeType: Boolean = false
)

/**
 * 商品信息类
 * @property blockId 区块ID
 * @property skuBlock 商品区块信息
 */
data class SkuInfo(
    val blockId: String = "",
    val skuBlock: SkuBlock? = null
)

/**
 * 商品区块信息类
 * @property categoryInfo 分类信息
 * @property type 类型
 * @property action 操作链接
 * @property skuCode 商品编码
 * @property skuType 商品类型
 * @property skuSaleType 销售类型
 * @property cover 封面信息
 * @property price 价格信息
 * @property title 标题
 * @property subTitle 副标题
 * @property recAttribute 推荐属性
 * @property recSlogan 推荐标语
 * @property inStock 库存状态
 * @property isOnSale 是否在售
 * @property brandId 品牌ID
 * @property brandName 品牌名称
 */
data class SkuBlock(
    val categoryInfo: NewPersonAssemblyDataCategoryInfo? = null,
    val type: Int = 0,
    val action: String = "",
    val skuCode: String = "",
    val skuType: Int = 0,
    val skuSaleType: Int = 0,
    val cover: NewPersonAssemblyDataCover? = null,
    val price: NewPersonAssemblyDataPrice? = null,
    val title: String = "",
    val subTitle: String = "",
    val recAttribute: List<String>? = null,
    val recSlogan: String = "",
    val inStock: Int = 0,
    val isOnSale: Boolean = true,
    val brandId: String = "",
    val brandName: String = ""
)

/**
 * 分类信息类
 * @property categoryId 分类ID
 * @property topicId 主题ID
 * @property modelId 模型ID
 * @property topicName 主题名称
 */
data class NewPersonAssemblyDataCategoryInfo(
    val categoryId: String = "",
    val topicId: String? = null,
    val modelId: String? = null,
    val topicName: String? = null
)

/**
 * 封面信息类
 * @property imageUrl 图片URL
 * @property videoUrl 视频URL
 * @property videoCoverImageUrl 视频封面图片URL
 */
data class NewPersonAssemblyDataCover(
    val imageUrl: String = "",
    val videoUrl: String? = null,
    val videoCoverImageUrl: String? = null
)

/**
 * 价格信息类
 * @property priceMap 价格映射
 * @property price 价格
 * @property marketPrice 市场价格
 */
data class NewPersonAssemblyDataPrice(
    val priceMap: Map<String, String>? = null,
    val price: String = "",
    val marketPrice: String = ""
)