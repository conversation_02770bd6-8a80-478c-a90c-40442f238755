package dev.pigmomo.yhkit2025.api.model.order

import dev.pigmomo.yhkit2025.api.model.user.AddressLocation

/**
 * 订单下单请求响应数据
 */
data class OrderPlaceResponse(
    val code: Int = 0,
    val message: String = "",
    val data: OrderPlaceData? = null,
    val now: Long = 0
)

/**
 * 订单数据
 */
data class OrderPlaceData(
    // 订单基本信息
    val title: String = "", // 订单标题
    val description: String = "", // 订单描述
    val subtitle: String = "", // 订单副标题
    val seller: OrderPlaceSeller? = null, // 商家信息
    val balancepayoption: Int = 0, // 余额支付选项
    val pointpayoption: Int = 0, // 积分支付选项
    val availablebalance: Int = 0, // 可用余额
    val totalbalance: Int = 0, // 总余额
    val totalpoints: Int = 0, // 总积分
    val cashierswitch: Int = 0, // 收银开关
    val obtainedCouponPackage: Int = 0, // 获得券包
    val discountmsg: String = "", // 优惠信息
    val discountamount: String = "", // 优惠金额
    val totalpayment: Int = 0, // 总支付金额（整数，单位为分）
    val totalpaymentNew: String = "", // 总支付金额（格式化字符串）
    val cartType: String = "", // 购物车类型
    val useNewCashierPayChannel: Boolean = false, // 是否使用新收银支付渠道
    val cashierPayChannelGroup: CashierPayChannelGroup? = null, // 收银支付渠道组
    val bottomMsgModelList: List<Any> = emptyList(), // 底部消息模型列表
    val cashierResponseInfo: CashierResponseInfo? = null, // 收银响应信息
    val hasPickupCoupon: Boolean = false, // 是否有领取的优惠券
    val widgets: List<OrderPlaceWidget> = emptyList(), // 小部件列表
    val tproducts: List<OrderPlaceProduct> = emptyList(), // 商品列表
    val pricedetail: List<OrderPlacePriceDetail> = emptyList(), // 价格详情
    val ispickself: Int = 0, // 是否自提
    val shopid: String = "", // 店铺ID
    val shopname: String = "", // 店铺名称
    val orderamount: Int = 0, // 订单金额（整数，单位为分）
    val placeorderid: String = "", // 下单ID
    val isbalancegiftcardflag: Int = 0, // 是否余额礼品卡标志
    val couponcoverpay: Int = 0, // 优惠券覆盖支付
    val discountamountmsg: String = "", // 优惠金额信息
    val deliveryshopid: String = "", // 配送店铺ID
    val cansynchomeaddress: Int = 0, // 是否可同步家庭地址
    val overweight: OrderPlaceOverWeight? = null, // 超重信息
    val realshopid: String = "", // 实际店铺ID
    val placeKey: String = "", // 下单键
    val orderType: Int = 0, // 订单类型
    val orderTypeText: String = "", // 订单类型文本
    val ordersubtype: String = "" // 订单子类型
)

/**
 * 商家信息
 */
data class OrderPlaceSeller(
    val id: String = "", // 商家ID
    val title: String = "", // 商家名称
    val icon: String = "", // 商家图标
    val action: String = "" // 商家动作链接
)

/**
 * 收银支付渠道组
 */
data class CashierPayChannelGroup(
    val baseElementModel: BaseElementModel? = null, // 基础元素模型
    val payChannelList: List<PayChannel> = emptyList() // 支付渠道列表
)

/**
 * 基础元素模型
 */
data class BaseElementModel(
    val sort: Int = 0, // 排序
    val name: String = "", // 名称
    val isshow: Int = 0, // 是否显示
    val widgettype: String = "", // 小部件类型
    val data: ElementModelData? = null, // 数据
    val isenable: Int = 0, // 是否启用
    val ischecked: Int = 0 // 是否选中
)

/**
 * 元素模型数据
 */
data class ElementModelData(
    val balancemodel: BalanceModel? = null // 余额模型
)

/**
 * 余额模型
 */
data class BalanceModel(
    val logo: String = "", // Logo
    val title: String = "", // 标题
    val desc: String = "" // 描述
)

/**
 * 支付渠道
 */
data class PayChannel(
    val channelLogo: String = "", // 渠道Logo
    val channelName: String = "", // 渠道名称
    val channelValue: String = "", // 渠道值
    val checkedStatus: Int = 0, // 选中状态
    val channelNameLogo: String = "", // 渠道名称Logo
    val channelPromoText: String = "", // 渠道促销文本
    val promoMsg: String = "" // 促销信息
)

/**
 * 收银响应信息
 */
data class CashierResponseInfo(
    val payMsgBefore: String = "", // 支付前信息
    val payMsgAfter: String = "" // 支付后信息
)

/**
 * 小部件
 */
data class OrderPlaceWidget(
    val sort: Int = 0, // 排序
    val name: String = "", // 名称
    val isshow: Int = 0, // 是否显示
    val widgettype: String = "", // 小部件类型
    val data: Any? = null, // 小部件数据，根据widgettype可能是不同的类型
    val isenable: Int = 0, // 是否启用
    val ischecked: Int = 0 // 是否选中
)

/**
 * 小部件数据基类
 */
sealed class BaseWidgetData

/**
 * 通用行小部件数据
 */
data class CommonRowWidgetData(
    val title: String? = null, // 标题
    val desc: String? = null, // 描述
    val icon: String? = null, // 图标
    val subTitle: String? = null // 副标题
) : BaseWidgetData()

/**
 * 地址小部件数据
 */
data class AddressWidgetData(
    val addressSwitchingPopUp: AddressSwitchingPopUp? = null, // 地址切换弹出窗口
    val deliverytip: String? = null, // 配送提示
    val hasaddress: Int? = null, // 是否有地址
    val deliverytype: Int? = null, // 配送类型
    val recvinfo: OrderPlaceRecvInfo? = null, // 接收信息
    val pickself: OrderPlacePickSelf? = null, // 自提信息
    val selfdeliveryagreement: SelfDeliveryAgreement? = null, // 自提协议
    val canSelectAddress: Boolean? = null // 是否可选择地址
) : BaseWidgetData()

/**
 * 包裹小部件数据
 */
data class PackagesWidgetData(
    val packages: List<OrderPlacePackage>? = null // 包裹列表
) : BaseWidgetData()

/**
 * 优惠券小部件数据
 */
data class CouponsWidgetData(
    val promomsg: String? = null, // 促销信息
    val couponpackagemsg: String? = null, // 优惠券包信息
    val icon: String? = null, // 图标
    val selectedcouponsmsg: String? = null, // 已选优惠券金额信息（例如 " -¥10"）
    val availablecoupons: List<OrderPlaceCoupon>? = null, // 可用优惠券列表
    val unavailablecoupons: List<OrderPlaceCoupon>? = null, // 不可用优惠券列表
    val selectedcoupons: List<String>? = null, // 已选择的优惠券代码列表
    val optimalmsg: String? = null // 最佳优惠券提示信息
) : BaseWidgetData()

/**
 * 优惠券信息
 */
data class OrderPlaceCoupon(
    val catalog: Int = 0, // 分类
    val name: String = "", // 优惠券名称
    val amount: Int = 0, // 金额(单位为分)
    val realm: String = "", // 使用范围
    val label: Int = 0, // 标签
    val loginType: String = "", // 登录类型
    val showDescription: String = "", // 展示描述
    val couponImageUrl: String = "", // 优惠券图片URL
    val deliveryLimits: List<Int> = emptyList(), // 配送限制
    val couponSendScene: String = "", // 优惠券发送场景
    val code: String = "", // 优惠券代码
    val status: Int = 0, // 状态(0:未使用, 1:已使用)
    val startDate: String = "", // 开始日期
    val endDate: String = "", // 结束日期
    val expirationDesc: String = "", // 到期描述
    val deliveredDate: String = "", // 发放日期
    val ext: String = "", // 扩展信息
    val expirationDescColor: String = "", // 到期描述颜色
    val desc3: String = "", // 描述3
    val promotioncode: String = "", // 促销代码
    val desc: String = "", // 描述
    val orderminamount: Int = 0, // 最小订单金额(单位为分)
    val minmemberlevel: Int = 0, // 最小会员等级
    val actionurl: String = "", // 动作URL
    val realmid: Int = 0, // 范围ID
    val conditiondesc: String = "", // 条件描述
    val shoprealm: String = "", // 适用店铺范围
    val descriptions: List<String> = emptyList(), // 描述列表
    val scope: String = "", // 作用范围
    val usescenario: Int = 0, // 使用场景
    val iscurrentavailable: Int = 0, // 是否当前可用
    val salechannel: Int = 0, // 销售渠道
    val taglist: List<Map<String, Any>> = emptyList(), // 标签列表
    val newcustomer: Int = 0, // 是否新客户专享
    val delaydays: Int = 0, // 延迟天数
    val expirationdays: Int = 0, // 过期天数
    val availablefrom: Long = 0, // 可用开始时间戳
    val availableto: Long = 0, // 可用结束时间戳
    val couponlatitude: String = "", // 优惠券纬度
    val combine: Int = 0, // 是否可叠加
    val desc2: String = "", // 描述2
    val date: String = "", // 日期
    val expireddate: String = "", // 过期日期
    val reason: String = "", // 不可用原因
    val subdeliverytype: Int = 0, // 子配送类型
    val isappmemberexclusivecoupon: Boolean = false // 是否APP会员专享券
)

/**
 * 红包小部件数据
 */
data class RedPacketWidgetData(
    val selectedredpacketsmsg: String? = null, // 已选红包信息
    val optimalmsg: String? = null, // 最佳红包提示信息
    val availableredpackets: List<OrderPlaceRedPacket>? = null, // 可用红包
    val unavailableredpackets: List<OrderPlaceRedPacket>? = null, // 不可用红包
    val selectedredpackets: List<String>? = null, // 已选择的红包代码列表
    val icon: String? = null // 图标
) : BaseWidgetData()

/**
 * 红包数据
 */
data class OrderPlaceRedPacket(
    val catalog: Int = 0, // 分类
    val name: String = "", // 名称
    val amount: Int = 0, // 金额(单位为分)
    val realm: String = "", // 使用范围
    val label: Int = 0, // 标签
    val loginType: String = "", // 登录类型
    val showDescription: String = "", // 展示描述
    val couponImageUrl: String = "", // 红包图片URL
    val deliveryLimits: List<Int> = emptyList(), // 配送限制
    val couponSendScene: String = "", // 红包发送场景
    val code: String = "", // 红包代码
    val status: Int = 0, // 状态(0:未使用, 1:已使用)
    val startDate: String = "", // 开始日期
    val endDate: String = "", // 结束日期
    val expirationDesc: String = "", // 到期描述
    val deliveredDate: String = "", // 发放日期
    val ext: String = "", // 扩展信息
    val expirationDescColor: String = "", // 到期描述颜色
    val desc3: String = "", // 描述3
    val promotioncode: String = "", // 促销代码
    val desc: String = "", // 描述
    val orderminamount: Int = 0, // 最小订单金额(单位为分)
    val minmemberlevel: Int = 0, // 最小会员等级
    val actionurl: String = "", // 动作URL
    val realmid: Int = 0, // 范围ID
    val conditiondesc: String = "", // 条件描述
    val shoprealm: String = "", // 适用店铺范围
    val descriptions: List<String> = emptyList(), // 描述列表
    val scope: String = "", // 作用范围
    val usescenario: Int = 0, // 使用场景
    val iscurrentavailable: Int = 0, // 是否当前可用
    val salechannel: Int = 0, // 销售渠道
    val taglist: List<Map<String, Any>> = emptyList(), // 标签列表
    val newcustomer: Int = 0, // 是否新客户专享
    val delaydays: Int = 0, // 延迟天数
    val expirationdays: Int = 0, // 过期天数
    val availablefrom: Long = 0, // 可用开始时间戳
    val availableto: Long = 0, // 可用结束时间戳
    val couponlatitude: String = "", // 红包纬度
    val combine: Int = 0, // 是否可叠加
    val desc2: String = "", // 描述2
    val date: String = "", // 日期
    val expireddate: String = "", // 过期日期
    val reason: String = "", // 不可用原因
    val subdeliverytype: Int = 0, // 子配送类型
    val isappmemberexclusivecoupon: Boolean = false // 是否APP会员专享
) : BaseWidgetData()

/**
 * 组合支付小部件数据
 */
data class CombinPayWidgetData(
    val balancemodel: BalanceModel? = null // 余额模型
) : BaseWidgetData()

/**
 * 缺货备注小部件数据
 */
data class StockLackRemarkWidgetData(
    val title: String? = null, // 标题
    val templates: List<OrderPlaceTemplate>? = null, // 模板
    val canSelect: Int? = null // 是否可选择
) : BaseWidgetData()

/**
 * 放置指定位置备注小部件数据
 */
data class PutDoorWayRemarkWidgetData(
    val title: String? = null, // 标题
    val subTitle: String? = null, // 副标题
    val templates: List<String>? = null, // 模板
    val scope: List<String>? = null, // 范围
    val canSelect: Int? = null // 是否可选择
) : BaseWidgetData()

/**
 * 备注小部件数据
 */
data class RemarkWidgetData(
    val title: String? = null, // 标题
    val subTitle: String? = null, // 副标题
    val remarks: List<OrderPlaceRemark>? = null // 备注列表
) : BaseWidgetData()

/**
 * 发票小部件数据
 */
data class InvoiceWidgetData(
    val payertype: Int? = null, // 支付者类型
    val invoicetype: Int? = null, // 发票类型
    val showflag: Int? = null, // 显示标志
    val returncode: Int? = null, // 返回码
    val returnmsg: String? = null, // 返回消息
    val notSupportInvoice: String? = null, // 不支持发票
) : BaseWidgetData()

/**
 * 商品总金额小部件数据
 */
data class ProductsTotalAmountWidgetData(
    val title: String? = null, // 标题
    val desc: String? = null // 描述
) : BaseWidgetData()

/**
 * 商品总金额小部件数据
 */
data class DiscountWidgetData(
    val title: String? = null, // 标题
    val desc: String? = null // 描述
) : BaseWidgetData()

/**
 * 积分小部件数据
 */
data class PointsWidgetData(
    val title: String? = null, // 标题
    val subtitle: String? = null, // 副标题
    val availablepointsmsg: String? = null, // 可用积分信息
    val icon: String? = null, // 图标
    val stylizeddesc: StylizedDesc? = null, // 样式化描述
    val tipinfos: List<OrderPlaceTipInfo>? = null // 提示信息
) : BaseWidgetData()

/**
 * 小计金额小部件数据
 */
data class SubtotalAmountWidgetData(
    val title: String? = null, // 标题
    val desc: String? = null, // 描述
    val descnew: String? = null // 新描述
) : BaseWidgetData()

/**
 * 购物袋配送小部件数据
 */
data class ShoppingBagsDeliveryWidgetData(
    val packBagModel: Int? = null, // 打包模型类型
    val packBagText: String? = null, // 打包说明文字
    val packBagButtonTip: String? = null, // 打包按钮提示
    val markbagpriceNew: String? = null, // 格式化的包装价格
    val shoppingbags: List<OrderPlaceShoppingBag>? = null, // 购物袋列表
    val bagsTitle: String? = null, // 购物袋标题
    val markbagprice: Int? = null // 包装价格(整数，单位为分)
) : BaseWidgetData()

/**
 * 购物袋自提小部件数据
 */
data class ShoppingBagsPickSelfWidgetData(
    val shoppingbags: List<OrderPlaceShoppingBag>? = null, // 购物袋列表
    val markbagpriceNew: String? = null, // 格式化的包装价格
    val markbagprice: Int? = null // 包装价格(整数，单位为分)
) : BaseWidgetData()

/**
 * 免运券小部件数据
 */
data class FreeDeliveryWidgetData(
    val title: String? = null, // 标题
    val desc: String? = null, // 描述
    val icon: String? = null, // 图标
    val subTitle: String? = null, // 副标题
    val labelDesc: String? = null // 标签描述
) : BaseWidgetData()

/**
 * 购物袋信息
 */
data class OrderPlaceShoppingBag(
    val id: String = "", // 购物袋ID
    val name: String = "", // 购物袋名称
    val price: Int = 0, // 价格(整数，单位为分)
    val priceNew: String = "", // 格式化的价格
    val bagmsg: String = "", // 购物袋说明
    val selectstate: Int = 0 // 选择状态(0:未选择, 1:已选择)
)

/**
 * 配送金额信息小部件数据
 */
data class DeliveryAmountInfoWidgetData(
    val icon: String? = null, // 图标URL
    val title: String? = null, // 标题
    val subTitle: String? = null, // 副标题
    val deliveryamountinfo: DeliveryAmountInfo? = null // 配送金额详细信息
) : BaseWidgetData()

/**
 * 配送金额小部件数据
 * 添加此类以修复类型转换错误
 */
data class DeliveryAmountWidgetData(
    val icon: String? = null, // 图标URL
    val title: String? = null, // 标题
    val subTitle: String? = null, // 副标题
    val deliveryamountinfo: DeliveryAmountInfo? = null // 配送金额详细信息
) : BaseWidgetData()

/**
 * 组小部件数据
 */
data class GroupWidgetData(
    val data: List<OrderPlaceWidget>? = null // 子小部件列表
) : BaseWidgetData()

/**
 * 地址切换弹出窗口
 */
data class AddressSwitchingPopUp(
    val title: String = "", // 标题
    val content: String = "", // 内容
    val cancelText: String = "", // 取消文本
    val goToCartText: String = "" // 前往购物车文本
)

/**
 * 接收信息
 */
data class OrderPlaceRecvInfo(
    val id: String = "", // ID
    val name: String = "", // 名称
    val gender: String = "", // 性别
    val alias: String = "", // 别名
    val phone: String = "", // 电话
    val address: OrderPlaceAddress? = null, // 地址
    val location: AddressLocation? = null, // 位置
    val isdefault: Int = 0 // 是否默认
)

/**
 * 自提信息
 */
data class OrderPlacePickSelf(
    val icon: String = "", // 图标
    val storeid: String = "", // 店铺ID
    val shopname: String = "", // 店铺名称
    val shopaddr: String = "", // 店铺地址
    val shoptel: String = "", // 店铺电话
)

/**
 * 地址
 */
data class OrderPlaceAddress(
    val city: String = "", // 城市
    val cityid: Int = 0, // 城市ID
    val area: String = "", // 区域
    val detail: String = "" // 详细
)

/**
 * 自提协议
 */
data class SelfDeliveryAgreement(
    val title: String = "", // 标题
    val action: String = "", // 动作
    val isShow: Boolean = false, // 是否显示
    val selectStatus: Int = 0 // 选择状态
)

/**
 * 包裹
 */
data class OrderPlacePackage(
    val products: List<OrderPlaceProduct> = emptyList(), // 商品列表
    val type: Int = 0, // 类型
    val appointment: Appointment? = null, // 预约
    val hourPerformancePack: Boolean = false, // 小时性能包
    val appointmentTimeResults: List<AppointmentTimeResult> = emptyList(), // 预约时间结果
    val appointmentTimeSwitch: Int = 0, // 预约时间开关
    val canSelectTime: Boolean = false, // 是否可选择时间
    val packagename: String = "", // 包裹名称
    val packagefullname: String = "", // 包裹全名
    val packageid: String = "" // 包裹ID
)

/**
 * 商品
 */
data class OrderPlaceProduct(
    val id: String = "", // 商品ID
    val title: String = "", // 商品标题
    val subtitle: String = "", // 商品副标题
    val action: String = "", // 商品动作
    val price: OrderPlacePrice? = null, // 商品价格
    val pattern: String = "", // 商品模式
    val spec: OrderPlaceSpec? = null, // 商品规格
    val primaryCategory: String = "", // 主类别
    val secondCategory: String = "", // 次类别
    val isbulkitem: Int = 0, // 是否批量商品
    val unit: String = "", // 单位
    val qty: Int = 0, // 数量
    val barcode: String = "", // 条形码
    val rowNum: Int = 0, // 行号
    val splitRowNum: Int = 0, // 分割行号
    val tomorrowDeliver: Boolean = false, // 明天配送
    val performanceHourHour: Boolean = false, // 性能小时 
    val imgurl: String = "", // 图片URL
    val num: Int = 0, // 数量
    val sellerid: String = "", // 卖家ID
    val calnum: String = "", // 计算数量
    val isrefund: Int = 0, // 是否退款
    val skusaletype: Int = 0, // SKU销售类型
    val isperformancehourhour: Boolean = false, // 是否性能小时
    val deliveryRuleId: Int = 0, // 配送规则ID
    val startDeliveryTime: Long = 0, // 预售开始配送时间(毫秒)
    val endDeliveryTime: Long = 0, // 预售结束配送时间(毫秒)
    val preSaleDeliveryRange: Int = 0, // 预售配送范围
    val skutype: Int = 0, // SKU类型(1:预售商品)
    val performancedata: List<Long> = emptyList(), // 预售配送性能数据(可选配送日期时间戳)
    val relativeseconds: Int = 0, // 相对秒数
    val taglist: List<OrderPlaceProductTag> = emptyList() // 商品标签列表
)

/**
 * 价格
 */
data class OrderPlacePrice(
    val total: Int = 0, // 总价（整数，单位为分）
    val value: Int = 0, // 价值（整数，单位为分）
    val market: Int = 0, // 市场价（整数，单位为分）
    val lineprice: Int = 0 // 线价（整数，单位为分）
)

/**
 * 规格
 */
data class OrderPlaceSpec(
    val desc: String = "" // 规格描述
)

/**
 * 预约
 */
data class Appointment(
    val appointmentselect: AppointmentSelect? = null, // 预约选择
    val appointments: List<AppointmentInfo>? = null // 预售预约信息列表
)

/**
 * 预约选择
 */
data class AppointmentSelect(
    val tdateindex: Int = 0, // 日期索引
    val ttimeindex: Int = 0 // 时间索引
)

/**
 * 预约时间结果
 */
data class AppointmentTimeResult(
    val appointmentDate: Long = 0, // 预约日期
    val dateRow: String = "", // 日期行
    val today: Int = 0, // 是否今天
    val appointmentTimeInfos: List<AppointmentTimeInfo> = emptyList() // 预约时间信息列表
)

/**
 * 预约时间信息
 */
data class AppointmentTimeInfo(
    val start: String = "", // 开始
    val end: String = "", // 结束
    val timeRow: String = "", // 时间行
    val isAvailed: Int = 0, // 是否可用
    val immediateSupport: Int = 0 // 是否支持即时
)

/**
 * 配送金额信息
 */
data class DeliveryAmountInfo(
    val amountNew: String = "", // 金额（新）
    val discountamountNew: String = "", // 折扣金额（新）
    val amount: Int = 0, // 金额（整数，单位为分）
    val discountamount: Int = 0, // 折扣金额（整数，单位为分）
    val totalamount: Int = 0, // 总金额（整数，单位为分）
    val basicamount: Int = 0, // 基础金额（整数，单位为分）
    val freightdescinfo: FreightDescInfo? = null // 运费描述信息
)

/**
 * 运费描述信息
 */
data class FreightDescInfo(
    val title: String = "", // 标题
    val detail: List<FreightDetail> = emptyList(), // 详情
    val desc: List<FreightDesc> = emptyList() // 描述
)

/**
 * 运费详情
 */
data class FreightDetail(
    val bold: Int = 0, // 是否加粗
    val prompt: String = "", // 提示
    val value: String = "" // 值
)

/**
 * 运费描述
 */
data class FreightDesc(
    val subtitle: String = "", // 副标题
    val content: String = "", // 内容
    val type: Int = 0 // 类型
)

/**
 * 样式化描述
 */
data class StylizedDesc(
    val activitytext: String = "", // 活动文本
    val signs: List<OrderPlaceSign> = emptyList() // 标志
)

/**
 * 标志
 */
data class OrderPlaceSign(
    val start: Int = 0, // 开始
    val end: Int = 0, // 结束
    val color: String = "", // 颜色
    val size: Int = 0 // 大小
)

/**
 * 提示信息
 */
data class OrderPlaceTipInfo(
    val tiptype: Int = 0, // 提示类型
    val show: Int = 0 // 是否显示
)

/**
 * 价格详情
 */
data class OrderPlacePriceDetail(
    val size: Int? = null, // 字体大小
    val prompt: String = "", // 提示
    val value: String = "", // 值
    val hinturl: String? = null // 提示链接
)

/**
 * 模板
 */
data class OrderPlaceTemplate(
    val type: Int = 0, // 类型
    val description: String = "", // 描述
    val isTacit: Int? = null // 是否默认
)

/**
 * 备注
 */
data class OrderPlaceRemark(
    val id: Int = 0, // ID
    val message: String = "" // 消息
)

/**
 * 超重信息
 */
data class OrderPlaceOverWeight(
    val isOver: Boolean = false, // 是否超重
    val proposePickSelf: Boolean = false, // 是否建议自提
    val msg: String = "" // 消息
)

/**
 * 预售预约信息
 */
data class AppointmentInfo(
    val dates: List<Long> = emptyList(), // 可选日期时间戳列表
    val times: List<AppointmentTimeDetail> = emptyList(), // 时间详情列表
    val deliveryType: Int = 0, // 配送类型
    val relativeSeconds: Int = 0, // 相对秒数
    val startDeliveryTime: Long = 0, // 开始配送时间
    val endDeliveryTime: Long = 0, // 结束配送时间
    val displayMode: Int = 0 // 显示模式
)

/**
 * 预约时间详情
 */
data class AppointmentTimeDetail(
    val isimmediatesupport: Int = 0, // 是否支持即时配送
    val fromhour: Int = 0, // 开始小时
    val fromminute: Int = 0, // 开始分钟
    val tohour: Int = 0, // 结束小时
    val tominute: Int = 0, // 结束分钟
    val interval: Int = 0, // 时间间隔(分钟)
    val immediatedescription: String = "" // 即时配送描述
)

/**
 * 商品标签
 */
data class OrderPlaceProductTag(
    val type: String = "", // 标签类型
    val text: String = "", // 标签文本
    val sort: Int = 0 // 排序
) 