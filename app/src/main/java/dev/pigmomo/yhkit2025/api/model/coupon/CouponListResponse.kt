package dev.pigmomo.yhkit2025.api.model.coupon

data class CouponListResponse(
    val code: Int = 0,
    val message: String = "",
    val data: CouponData? = null,
    val now: Long = 0
)

data class CouponData(
    val availablecoupons: CouponList = CouponList(),
    val unavailablecoupons: CouponList = CouponList(),
    val categories: List<CouponCategory> = emptyList(),
    val cornerMarkText: String = "",
    val count: Int = 0,
    val couponSuperpose: String = "",
    val pageCount: Int = 0,
    val pageNo: Int = 0,
    val pendingcount: Int = 0,
    val popUpWindowInfo: CouponList = CouponList()
)

data class CouponList(
    val count: Int = 0,
    val coupons: List<CouponListCoupon> = emptyList()
)

data class CouponCategory(
    val text: String = "",
    val value: Int = 0,
    val childCategories: List<CouponCategory> = emptyList()
)

data class CouponListCoupon(
    val actionurl: String = "",
    val activityShops: List<String> = emptyList(),
    val amount: Int = 0,
    val availableTo: Long = 0,
    val catalog: Int = 0,
    val channelLabel: Int = 0,
    val code: String = "",
    val conditiondesc: String = "",
    val couponImg: String = "",
    val couponNotStartedDesc: String = "",
    val couponSendScene: String = "",
    val couponTimeType: Int = 0,
    val couponType: String = "",
    val couponlatitude: String = "",
    val date: String = "",
    val deliveredDate: String = "",
    val desc: String = "",
    val descriptions: List<String> = emptyList(),
    val details: List<String> = emptyList(),
    val endDate: String = "",
    val expirationDesc: String = "",
    val expirationDescColor: String = "",
    val expireddate: String = "",
    val ext: String = "",
    val iscurrentavailable: Int = 0,
    val label: Int = 0,
    val loginType: String = "",
    val memberLevelTag: Map<String, Any> = emptyMap(),
    val minmemberlevel: Int = 0,
    val name: String = "",
    val newcustomer: Int = 0,
    val orderLimits: List<Int> = emptyList(),
    val orderminamount: Int = 0,
    val promotionShops: List<PromotionShop> = emptyList(),
    val promotioncode: String = "",
    val realm: String = "",
    val realmid: Int = 0,
    val saleChannel: Int = 0,
    val scope: String = "",
    val sellers: List<String> = emptyList(),
    val shopSellers: List<String> = emptyList(),
    val shoprealm: String = "",
    val startDate: String = "",
    val status: Int = 0,
    val taglist: List<CouponTag> = emptyList(),
    val useprompt: String = "",
    val usescenario: Int = 0,
    val freightfreedesc : String = ""
)

data class PromotionShop(
    val sellerid: Int = 0,
    val shopid: String = ""
)

data class CouponTag(
    val text: String = "",
    val type: String = ""
) 