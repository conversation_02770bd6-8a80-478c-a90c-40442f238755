package dev.pigmomo.yhkit2025.api.model

/**
 * 通用API响应封装类
 * @param T 响应数据的具体类型
 */
data class BaseResponse<T>(
    val code: Int = 0,
    val data: T? = null,
    val message: String = "",
    val msg: String = "",
    val now: Long = 0
) {
    /**
     * 判断请求是否成功
     */
    val isSuccess: Boolean
        get() = code == 0

    /**
     * 获取错误消息
     */
    val errorMessage: String
        get() = message.ifEmpty { msg }
}