package dev.pigmomo.yhkit2025.api.model.coupon

/**
 * 新用户优惠券信息响应数据类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 新用户优惠券信息数据
 * @property now 当前时间戳
 */
data class NewUserCouponInfoResponse(
    val code: Int = 0,
    val message: String = "",
    val data: NewUserCouponInfoData? = null,
    val now: Long = 0
)

/**
 * 新用户优惠券信息数据类
 * @property name 优惠券名称
 * @property code 优惠券代码
 * @property type 优惠券类型
 * @property startTime 开始时间戳
 * @property endTime 结束时间戳
 * @property status 状态
 * @property statusStr 状态描述
 * @property landingPageUrl 落地页链接
 * @property rule 规则说明
 */
data class NewUserCouponInfoData(
    val name: String = "",
    val code: String = "",
    val type: String = "",
    val startTime: Long = 0,
    val endTime: Long = 0,
    val status: Int = 0,
    val statusStr: String? = null,
    val landingPageUrl: String = "",
    val rule: String = ""
) 