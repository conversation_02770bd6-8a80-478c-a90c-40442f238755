package dev.pigmomo.yhkit2025.api.utils

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL
import java.util.concurrent.TimeUnit

/**
 * HTTP服务器通信工具类
 * 用于与本地签名服务器进行通信
 */
object SignUtils {
    private const val TAG = "SignUtils"
    private const val SERVER_URL = "http://localhost:8883"
    private const val CONNECT_TIMEOUT = 10L // 连接超时时间(秒)
    private const val READ_TIMEOUT = 30L // 读取超时时间(秒)

    /**
     * 发送POST请求的方法
     * @param endpoint 请求端点
     * @param data 请求数据
     * @return 服务器响应，失败时返回空字符串
     */
    suspend fun postToSign(endpoint: String, data: String): String {
        return sendHttpRequest(endpoint, data)
    }

    /**
     * 发送POST请求获取XYH业务参数
     * @param endpoint 请求端点
     * @param data 请求数据
     * @return 服务器响应，失败时返回空字符串
     */
    suspend fun postToXyhBizParams(endpoint: String, data: String): String {
        return sendHttpRequest(endpoint, data)
    }

     /**
     * 发送POST请求的通用方法
     * @param endpoint 请求端点
     * @param data 请求数据
     * @param headers 可选的请求头
     * @return 服务器响应，失败时返回空字符串
     */
    private suspend fun sendHttpRequest(
        endpoint: String, 
        data: String,
        method: String = "POST",
        headers: Map<String, String> = mapOf("Content-Type" to "application/json")
    ): String = withContext(Dispatchers.IO) {
        val serverUrl = "$SERVER_URL$endpoint"
        Log.d(TAG, "Sending $method request to: $serverUrl with data: $data")

        var connection: HttpURLConnection? = null
        try {
            val url = URL(serverUrl)
            connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = method
            connection.doOutput = method != "GET"
            connection.doInput = true
            connection.connectTimeout = TimeUnit.SECONDS.toMillis(CONNECT_TIMEOUT).toInt()
            connection.readTimeout = TimeUnit.SECONDS.toMillis(READ_TIMEOUT).toInt()
            
            // 设置请求头
            headers.forEach { (key, value) ->
                connection.setRequestProperty(key, value)
            }

            // 如果有请求体，写入数据
            if (method != "GET" && data.isNotEmpty()) {
                connection.outputStream.use { outputStream ->
                    outputStream.write(data.toByteArray())
                    outputStream.flush()
                }
            }

            // 检查响应码
            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 读取服务器的响应
                connection.inputStream.use { inputStream ->
                    val reader = BufferedReader(InputStreamReader(inputStream))
                    val response = reader.readText()
                    Log.d(TAG, "Server response: $response")
                    response
                }
            } else {
                Log.e(TAG, "Server responded with code: $responseCode")
                ""
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in $method request: ${e.message}", e)
            ""
        } finally {
            connection?.disconnect()
        }
    }

    /**
     * 检查服务器是否运行
     * @return 服务器是否正常运行
     */
    suspend fun checkServer(): Boolean = withContext(Dispatchers.IO) {
        val response = sendHttpRequest("/", "", "GET")
        response == "HTTP Server is running"
    }
}