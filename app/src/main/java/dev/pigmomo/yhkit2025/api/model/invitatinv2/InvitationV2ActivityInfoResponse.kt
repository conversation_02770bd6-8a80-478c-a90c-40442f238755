package dev.pigmomo.yhkit2025.api.model.invitatinv2

/**
 * 邀请活动V2信息响应数据类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 邀请活动信息数据
 * @property now 当前时间戳
 */
data class InvitationV2ActivityInfoResponse(
    val code: Int = 0,
    val message: String = "",
    val data: InvitationV2ActivityInfoData? = null,
    val now: String = ""
)

/**
 * 邀请活动V2信息数据类
 * @property promoteCode 推广码
 * @property showRankBannerFlag 是否显示排行榜横幅
 * @property themeUrl 主题URL
 * @property showOrderPromotionFlag 是否显示订单促销标志
 * @property inviterTypeStr2 邀请者类型字符串2
 * @property wechatCouponRewardVo 微信优惠券奖励
 * @property baseinfo 基本信息
 * @property scrollmsgs 滚动消息列表
 * @property invitertype 邀请者类型
 * @property membertypename 会员类型名称
 * @property auditstatus 审核状态
 * @property activitystatus 活动状态
 * @property invitertabtitle 邀请者标签标题
 * @property leadertabtitle 领导者标签标题
 * @property stepRewardInfo 阶梯奖励信息
 * @property invitationApplyResult 邀请申请结果
 * @property invitertypeStr 邀请者类型字符串
 * @property activityTypes 活动类型列表
 * @property shopName 商店名称
 * @property shopId 商店ID
 * @property memberId 会员ID
 * @property faActivityCode 活动代码
 */
data class InvitationV2ActivityInfoData(
    val promoteCode: String? = null,
    val showRankBannerFlag: Boolean = false,
    val themeUrl: String = "",
    val showOrderPromotionFlag: Int = 0,
    val inviterTypeStr2: String = "",
    val wechatCouponRewardVo: Any? = null,
    val baseinfo: InvitationV2ActivityInfoBaseInfo? = null,
    val scrollmsgs: List<InvitationV2ActivityInfoScrollMsg> = emptyList(),
    val invitertype: Any? = null,
    val membertypename: String = "",
    val auditstatus: Int = 0,
    val activitystatus: Any? = null,
    val invitertabtitle: Any? = null,
    val leadertabtitle: Any? = null,
    val stepRewardInfo: Any? = null,
    val invitationApplyResult: InvitationApplyResult? = null,
    val invitertypeStr: String = "",
    val activityTypes: List<String> = emptyList(),
    val shopName: String = "",
    val shopId: String = "",
    val memberId: String = "",
    val faActivityCode: String = ""
)

/**
 * 基本信息类
 * @property mode 模式
 * @property validOperationType 有效操作类型
 * @property validOperationTypeName 有效操作类型名称
 * @property noticeTitle 通知标题
 * @property noticeImage 通知图片
 * @property communityReward 社区奖励
 * @property communityCode 社区代码
 * @property orderPayLimit 订单支付限制
 * @property repurchaseReward 回购奖励
 * @property repurchaseRewardType 回购奖励类型
 * @property activitycode 活动代码
 * @property title 标题
 * @property description 描述
 * @property enablefrom 启用开始时间
 * @property enableto 启用结束时间
 * @property status 状态
 * @property inviterrewardtype 邀请者奖励类型
 * @property validperiod 有效期
 * @property inviteeorderdesc 被邀请者订单描述
 * @property rewardrule 奖励规则列表
 * @property hidewechatprogramcode 是否隐藏微信程序码
 * @property showinput 是否显示输入
 * @property invitermobile 邀请者手机号
 * @property invitermobiledesc 邀请者手机号描述
 * @property inviteRuleType 邀请规则类型
 * @property validDataOnRule 规则上的有效数据
 * @property shareImageUrl 分享图片URL
 */
data class InvitationV2ActivityInfoBaseInfo(
    val mode: Any? = null,
    val validOperationType: String = "",
    val validOperationTypeName: String = "",
    val noticeTitle: Any? = null,
    val noticeImage: Any? = null,
    val communityReward: Any? = null,
    val communityCode: Any? = null,
    val orderPayLimit: Int = 0,
    val repurchaseReward: Any? = null,
    val repurchaseRewardType: Any? = null,
    val activitycode: String = "",
    val title: String = "",
    val description: String = "",
    val enablefrom: Long = 0,
    val enableto: Long = 0,
    val status: String = "",
    val inviterrewardtype: String = "",
    val validperiod: Int = 0,
    val inviteeorderdesc: String = "",
    val rewardrule: List<InvitationV2ActivityInfoRewardRule> = emptyList(),
    val hidewechatprogramcode: Boolean = false,
    val showinput: Boolean = false,
    val invitermobile: String = "",
    val invitermobiledesc: String = "",
    val inviteRuleType: Any? = null,
    val validDataOnRule: Any? = null,
    val shareImageUrl: String = ""
)

/**
 * 奖励规则类
 * @property isNewCustom 是否新客户
 * @property isConfigureRewards 是否配置奖励
 * @property newInviteeCount 新被邀请者数量
 * @property ladderReward 阶梯奖励
 * @property surplusTime 剩余时间
 * @property rewardtype 奖励类型
 * @property inviteetype 被邀请者类型
 * @property inviteetypedetail 被邀请者类型详情
 * @property inviteetypedesc 被邀请者类型描述
 * @property inviterrewardtype 邀请者奖励类型
 * @property inviterrewardlimit 邀请者奖励限制
 * @property inviterrewarddiscountlimit 邀请者奖励折扣限制
 * @property inviterrewardcontent 邀请者奖励内容
 * @property inviteerewardcontent 被邀请者奖励内容
 */
data class InvitationV2ActivityInfoRewardRule(
    val isNewCustom: Int = 0,
    val isConfigureRewards: Int = 0,
    val newInviteeCount: Int = 0,
    val ladderReward: Any? = null,
    val surplusTime: Int = 0,
    val rewardtype: String = "",
    val inviteetype: String = "",
    val inviteetypedetail: String? = null,
    val inviteetypedesc: String? = null,
    val inviterrewardtype: String = "",
    val inviterrewardlimit: String = "",
    val inviterrewarddiscountlimit: Any? = null,
    val inviterrewardcontent: String = "",
    val inviteerewardcontent: String = ""
)

/**
 * 滚动消息类
 * @property needSubFlag 是否需要订阅标志
 * @property invitermobile 邀请者手机号
 * @property inviteeMobile 被邀请者手机号
 * @property rewardAmount 奖励金额
 * @property inviteeNickName 被邀请者昵称
 * @property inviteeAvator 被邀请者头像
 * @property inviterNickName 邀请者昵称
 * @property inviterAvator 邀请者头像
 */
data class InvitationV2ActivityInfoScrollMsg(
    val needSubFlag: Boolean = false,
    val invitermobile: Any? = null,
    val inviteeMobile: String = "",
    val rewardAmount: String = "",
    val inviteeNickName: Any? = null,
    val inviteeAvator: Any? = null,
    val inviterNickName: String = "",
    val inviterAvator: String = ""
)

/**
 * 邀请申请结果类
 * @property id ID
 * @property applyStatus 申请状态
 * @property haveShow 是否已显示
 */
data class InvitationApplyResult(
    val id: Any? = null,
    val applyStatus: Int = 0,
    val haveShow: Int = 0
)