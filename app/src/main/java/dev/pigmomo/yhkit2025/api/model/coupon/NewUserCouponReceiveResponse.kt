package dev.pigmomo.yhkit2025.api.model.coupon

/**
 * 新用户优惠券领取响应数据类
 * @property code 响应状态码，0表示成功，42002表示已领取过
 * @property message 响应消息
 * @property data 新用户优惠券领取数据
 * @property now 当前时间戳
 */
data class NewUserCouponReceiveResponse(
    val code: Int = 0,
    val message: String = "",
    val data: NewUserCouponReceiveData? = null,
    val now: Long = 0
)

/**
 * 新用户优惠券领取数据类
 * @property s 未知字段
 * @property customerServiceUrl 客服链接
 * @property groupChatUrl 群聊链接
 * @property receiveFlag 领取标志
 */
data class NewUserCouponReceiveData(
    val s: String = "",
    val customerServiceUrl: String? = null,
    val groupChatUrl: String? = null,
    val receiveFlag: Boolean? = null
) 