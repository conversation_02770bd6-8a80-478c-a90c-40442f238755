package dev.pigmomo.yhkit2025.api.service

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestHelper
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.SignType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 拼手气服务类
 * 提供拼手气相关的API调用方法
 */
class HandLuckService(requestHelper: RequestHelper) : BaseService(requestHelper) {

    private val tag = "HandLuckService"

    private val serviceType = requestHelper.getServiceType()

    /**
     * 获取拼手气相关数据
     * @param eventId 事件ID
     * @return 拼手气相关数据响应结果
     */
    suspend fun handLuckGetSlotMachineData(eventId: String): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    val cityId = requestHelper.getCityId()

                    if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                        Log.e(
                            tag,
                            "handLuckGetSlotMachineData: sellerId or shopId or cityId is empty"
                        )
                        return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getAppCommonParams()

                    // 构建Web业务参数
                    val businessParams = buildAppWebBusinessParams()

                    // 添加额外参数
                    businessParams["eventId"] = eventId
                    businessParams["shopId"] = shopId
                    businessParams["channel"] = "512"
                    businessParams["platform"] = "wechatminiprogram"
                    businessParams["appid"] = "wxc9cf7c95499ee604"
                    businessParams["wechatunionid"] = ""
                    businessParams["sellerid"] = sellerId
                    businessParams["cityid"] = cityId
                    businessParams["shopid"] = shopId

                    // 构建URL
                    val urlWithParams = buildAppApiUrl(
                        RequestConfig.Path.HAND_LUCK_GET_SLOT_MACHINE_DATA_PATH,
                        businessParams,
                        commonParams
                    )

                    // 生成签名
                    val sign = requestHelper.generateSign(urlWithParams, "", SignType.APP_WEB.value)
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val webXyhBizParams = requestHelper.getWebXyhBizParams()
                    if (webXyhBizParams.isEmpty()) {
                        Log.e(tag, "handLuckGetSlotMachineData: webXyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("webXyhBizParams not set"))
                    }

                    val headers = buildAppStandardHeaders(webXyhBizParams, true, false)

                    // 发送请求
                    requestHelper.get(fullUrl, headers)
                }

                "mini" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    val cityId = requestHelper.getCityId()

                    if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                        Log.e(
                            tag,
                            "handLuckGetSlotMachineData: sellerId or shopId or cityId is empty"
                        )
                        return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getMiniProgramCommonParams()

                    // 构建业务参数
                    val businessParams = buildMiniProgramWebBusinessParams()

                    // 添加额外参数
                    businessParams["eventId"] = eventId
                    businessParams["shopId"] = shopId
                    businessParams["channel"] = "512"
                    businessParams["appid"] = "wxc9cf7c95499ee604"
                    businessParams["wechatunionid"] = ""
                    businessParams["sellerid"] = sellerId
                    businessParams["cityid"] = cityId
                    businessParams["shopid"] = shopId
                    businessParams["memberid"] = requestHelper.getUid()

                    // 构建URL
                    val urlWithParams = buildMiniApiUrl(
                        RequestConfig.Path.HAND_LUCK_GET_SLOT_MACHINE_DATA_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                    // 生成签名
                    val sign = requestHelper.generateSign(
                        urlWithParams,
                        "",
                        SignType.MINIPROGRAM_WEB_ACTIVITY.value
                    )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getWebXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "handLuckGetSlotMachineData: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildMiniStandardHeaders(xyhBizParams, true)

                    // 发送请求
                    requestHelper.get(fullUrl, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }

    /**
     * 获取拼手气奖励列表
     * @param eventId 事件ID
     * @return 拼手气奖励列表响应结果
     */
    suspend fun handLuckGetTotalRewardList(eventId: String): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    val cityId = requestHelper.getCityId()

                    if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                        Log.e(
                            tag,
                            "handLuckGetTotalRewardList: sellerId or shopId or cityId is empty"
                        )
                        return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getAppCommonParams()

                    // 构建Web业务参数
                    val businessParams = buildAppWebBusinessParams()

                    // 添加额外参数
                    businessParams["eventId"] = eventId
                    businessParams["shopId"] = shopId
                    businessParams["channel"] = "512"
                    businessParams["platform"] = "wechatminiprogram"
                    businessParams["appid"] = "wxc9cf7c95499ee604"
                    businessParams["wechatunionid"] = ""
                    businessParams["sellerid"] = sellerId
                    businessParams["cityid"] = cityId
                    businessParams["shopid"] = shopId

                    // 构建URL
                    val urlWithParams = buildAppApiUrl(
                        RequestConfig.Path.HAND_LUCK_GET_TOTAL_REWARD_LIST_PATH,
                        businessParams,
                        commonParams
                    )

                    val requestBody = "{\"eventId\":\"${eventId}\"}"

                    // 生成签名
                    val sign =
                        requestHelper.generateSign(
                            urlWithParams,
                            requestBody,
                            SignType.APP_WEB.value
                        )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val webXyhBizParams = requestHelper.getWebXyhBizParams()
                    if (webXyhBizParams.isEmpty()) {
                        Log.e(tag, "handLuckGetTotalRewardList: webXyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("webXyhBizParams not set"))
                    }

                    val headers = buildAppStandardHeaders(webXyhBizParams, true, false)

                    // 发送请求
                    requestHelper.postJson(fullUrl, requestBody, headers)
                }

                "mini" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    val cityId = requestHelper.getCityId()

                    if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                        Log.e(
                            tag,
                            "handLuckGetTotalRewardList: sellerId or shopId or cityId is empty"
                        )
                        return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getMiniProgramCommonParams()

                    // 构建业务参数
                    val businessParams = buildMiniProgramWebBusinessParams()

                    // 添加额外参数
                    businessParams["eventId"] = eventId
                    businessParams["shopId"] = shopId
                    businessParams["channel"] = "512"
                    businessParams["appid"] = "wxc9cf7c95499ee604"
                    businessParams["wechatunionid"] = ""
                    businessParams["sellerid"] = sellerId
                    businessParams["cityid"] = cityId
                    businessParams["shopid"] = shopId
                    businessParams["memberid"] = requestHelper.getUid()

                    // 构建URL
                    val urlWithParams = buildMiniApiUrl(
                        RequestConfig.Path.HAND_LUCK_GET_TOTAL_REWARD_LIST_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                    val requestBody = """{"eventId":"$eventId"}"""

                    // 生成签名
                    val sign = requestHelper.generateSign(
                        urlWithParams,
                        requestBody,
                        SignType.MINIPROGRAM_WEB_ACTIVITY.value
                    )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getWebXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "handLuckGetTotalRewardList: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildMiniStandardHeaders(xyhBizParams, true)

                    // 发送请求
                    requestHelper.postJson(fullUrl, requestBody, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }


    /**
     * 拼手气
     * @param eventId 事件ID
     * @param activityCode 活动代码
     * @return 拼手气结果响应
     */
    suspend fun handLuckDrawLottery(eventId: String, activityCode: String): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    val cityId = requestHelper.getCityId()

                    if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                        Log.e(tag, "handLuckDrawLottery: sellerId or shopId or cityId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getAppCommonParams()

                    // 构建Web业务参数
                    val businessParams = buildAppWebBusinessParams()

                    // 添加额外参数
                    businessParams["eventId"] = eventId
                    businessParams["shopId"] = shopId
                    businessParams["activityCode"] = activityCode
                    businessParams["channel"] = "512"
                    businessParams["platform"] = "wechatminiprogram"
                    businessParams["appid"] = "wxc9cf7c95499ee604"
                    businessParams["wechatunionid"] = ""
                    businessParams["sellerid"] = sellerId
                    businessParams["cityid"] = cityId
                    businessParams["shopid"] = shopId

                    // 构建URL
                    val urlWithParams = buildAppApiUrl(
                        RequestConfig.Path.HAND_LUCK_DRAW_LOTTERY_PATH,
                        businessParams,
                        commonParams
                    )

                    // 构建请求体
                    val requestBody = ""

                    // 生成签名
                    val sign =
                        requestHelper.generateSign(
                            urlWithParams,
                            requestBody,
                            SignType.APP_WEB.value
                        )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val webXyhBizParams = requestHelper.getWebXyhBizParams()
                    if (webXyhBizParams.isEmpty()) {
                        Log.e(tag, "handLuckDrawLottery: webXyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("webXyhBizParams not set"))
                    }

                    val headers = buildAppStandardHeaders(webXyhBizParams, true, false)

                    // 发送请求
                    requestHelper.postJson(fullUrl, requestBody, headers)
                }

                "mini" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    val cityId = requestHelper.getCityId()

                    if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                        Log.e(tag, "handLuckDrawLottery: sellerId or shopId or cityId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getMiniProgramCommonParams()

                    // 构建业务参数
                    val businessParams = buildMiniProgramWebBusinessParams()

                    // 添加额外参数
                    businessParams["eventId"] = eventId
                    businessParams["shopId"] = shopId
                    businessParams["activityCode"] = activityCode
                    businessParams["channel"] = "512"
                    businessParams["appid"] = "wxc9cf7c95499ee604"
                    businessParams["wechatunionid"] = ""
                    businessParams["sellerid"] = sellerId
                    businessParams["cityid"] = cityId
                    businessParams["shopid"] = shopId
                    businessParams["memberid"] = requestHelper.getUid()

                    // 构建URL
                    val urlWithParams = buildMiniApiUrl(
                        RequestConfig.Path.HAND_LUCK_DRAW_LOTTERY_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                    // 构建请求体
                    val requestBody = ""

                    // 生成签名
                    val sign = requestHelper.generateSign(
                        urlWithParams,
                        requestBody,
                        SignType.MINIPROGRAM_WEB_ACTIVITY.value
                    )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getWebXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "handLuckDrawLottery: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildMiniStandardHeaders(xyhBizParams, true)

                    // 发送请求
                    requestHelper.postJson(fullUrl, requestBody, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }
}