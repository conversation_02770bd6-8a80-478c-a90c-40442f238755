package dev.pigmomo.yhkit2025.api.service

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestHelper
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.SignType
import dev.pigmomo.yhkit2025.api.model.user.AddressItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.UUID

/**
 * 店铺服务类
 * 提供店铺相关的API调用方法
 */
class ShopService(requestHelper: RequestHelper) : BaseService(requestHelper) {

    private val tag = "ShopService"

    private val serviceType = requestHelper.getServiceType()

    /**
     * 获取店铺信息
     * @param selectedAddress 选中的地址信息
     * @return 店铺信息响应结果
     */
    suspend fun getFbShopLbs(
        selectedAddress: AddressItem
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val commonParams = requestHelper.getAppCommonParams()

                val xyhBizParams =
                    requestHelper.getXyhBizParams()
                        .ifEmpty { RequestConfig.XYH_BIZ_PARAMS_ORIGIN_APP }

                // 构建业务参数
                val businessParams = buildAppBusinessParams()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.FB_SHOP_LBS_PATH,
                        businessParams,
                        commonParams
                    )

                // 从AddressItem中获取地址信息
                val area = selectedAddress.address.area
                val city = selectedAddress.address.city
                val cityid = selectedAddress.address.cityid.toString()
                val detail = selectedAddress.address.detail
                val provname = selectedAddress.address.provname

                val deliverydesc = selectedAddress.deliverydesc
                val gender = selectedAddress.gender
                val id = selectedAddress.id
                val isdefault = selectedAddress.isdefault.toString()
                val lat = selectedAddress.location?.lat ?: ""
                val lng = selectedAddress.location?.lng ?: ""
                val name = selectedAddress.name
                val nextdaydeliver = selectedAddress.nextdaydeliver.toString()
                val phone = selectedAddress.phone
                val scope = selectedAddress.scope.toString()

                // 构建请求体
                val requestBody = """
                {
                "address":{
                    "address":{
                        "area":"$area",
                        "city":"$city",
                        "cityid":"$cityid",
                        "detail":"$detail",
                        "_mid":"",
                        "_uuid":"${UUID.randomUUID()}"
                    },
                    "deliverydesc":"$deliverydesc",
                    "foodsupport":0,
                    "gender":"$gender",
                    "id":"$id",
                    "isSearch":false,
                    "ischanged":false,
                    "isdefault":$isdefault,
                    "itemType":3,
                    "location":{
                        "lat":"$lat",
                        "lng":"$lng",
                        "_mid":"",
                        "_uuid":"${UUID.randomUUID()}"
                    },
                    "name":"$name",
                    "nextdaydeliver":$nextdaydeliver,
                    "phone":"$phone",
                    "scope":$scope,
                    "_mid":"",
                    "_uuid":"${UUID.randomUUID()}"
                },
                "isuserselect":1,
                "lastResult":"",
                "requestType":1
                }
                """.trimIndent().replace("\n", "").replace(" ", "")

                val needSignStr =
                    urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                        .replace("=", "")
                        .replace("&", "") + requestBody

                // 生成签名
                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "getFbShopLbs: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val commonParams = requestHelper.getMiniProgramCommonParams()

                val xyhBizParams = requestHelper.getXyhBizParams()
                    .ifEmpty { RequestConfig.XYH_BIZ_PARAMS_ORIGIN_MINI_PROGRAM }

                // 构建业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.FB_SHOP_LBS_PATH_MINI,
                        businessParams,
                        commonParams,
                        "activity"
                    )

                // 从AddressItem中获取地址信息
                val area = selectedAddress.address.area
                val city = selectedAddress.address.city
                val cityid = selectedAddress.address.cityid.toString()
                val detail = selectedAddress.address.detail
                val provname = selectedAddress.address.provname

                val deliverydesc = selectedAddress.deliverydesc
                val gender = selectedAddress.gender
                val id = selectedAddress.id
                val isdefault = selectedAddress.isdefault.toString()
                val lat = selectedAddress.location?.lat ?: ""
                val lng = selectedAddress.location?.lng ?: ""
                val name = selectedAddress.name
                val nextdaydeliver = selectedAddress.nextdaydeliver.toString()
                val phone = selectedAddress.phone
                val scope = selectedAddress.scope.toString()

                // 构建请求体
                val requestBody = """
                {
                "address":{
                    "address":{
                        "area":"$area",
                        "city":"$city",
                        "cityid":"$cityid",
                        "detail":"$detail",
                        "_mid":"",
                        "_uuid":"${UUID.randomUUID()}"
                    },
                    "deliverydesc":"$deliverydesc",
                    "foodsupport":0,
                    "gender":"$gender",
                    "id":"$id",
                    "isSearch":false,
                    "ischanged":false,
                    "isdefault":$isdefault,
                    "itemType":3,
                    "location":{
                        "lat":"$lat",
                        "lng":"$lng",
                        "_mid":"",
                        "_uuid":"${UUID.randomUUID()}"
                    },
                    "name":"$name",
                    "nextdaydeliver":$nextdaydeliver,
                    "phone":"$phone",
                    "scope":$scope,
                    "_mid":"",
                    "_uuid":"${UUID.randomUUID()}"
                },
                "isuserselect":1,
                "lastResult":"",
                "requestType":1
                }
                """.trimIndent().replace("\n", "").replace(" ", "")

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }
}