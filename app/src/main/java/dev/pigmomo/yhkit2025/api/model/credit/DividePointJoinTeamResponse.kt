package dev.pigmomo.yhkit2025.api.model.credit

/**
 * 积分组队加入响应数据类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 加入组队结果数据
 * @property now 当前时间戳
 */
data class DividePointJoinTeamResponse(
    val code: Int = 0,
    val message: String = "",
    val data: DividePointJoinTeamData? = null,
    val now: Long = 0
)

/**
 * 积分组队加入结果数据类
 * @property isSuccess 是否加入成功
 * @property description 描述信息，可能包含失败原因
 */
data class DividePointJoinTeamData(
    val isSuccess: Boolean = false,
    val description: String? = null
) 