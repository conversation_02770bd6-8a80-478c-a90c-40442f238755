package dev.pigmomo.yhkit2025.api.model.order

data class OrderListResponse(
    val code: Int = 0,
    val message: String = "",
    val data: OrderListData? = null,
    val now: Long = 0
)

data class OrderListData(
    val count: Int = 0,
    val page: Int = 0,
    val dataSourceFinish: Boolean = false,
    val showSearchButton: Boolean = false,
    val hasnextpage: Int = 0,
    val lastorderid: String = "",
    val orders: List<Order> = emptyList()
)

data class Order(
    val id: String = "",
    val status: Int = 0,
    val title: String = "",
    val subtitle: String = "",
    val dinners: Int = 0,
    val description: String = "",
    val products: List<OrderProduct> = emptyList(),
    val seller: OrderSeller = OrderSeller(),
    val cashierswitch: Int = 0,
    val ordersubtype: String = "",
    val totalpayment: Int = 0,
    val ispickself: Int = 0,
    val paytypename: String = "",
    val deliverymode: Int = 0,
    val balancepay: Int = 0,
    val shopid: String = "",
    val timeinfo: OrderTimeInfo = OrderTimeInfo(),
    val detailaction: String = "",
    val refundable: Int = 0,
    val balanceamount: Int = 0,
    val totalamount: Int = 0,
    val canrate: Int = 0,
    val canbuyagain: Int = 0,
    val texpecttime: OrderExpectTime? = null,
    val ordertypetag: String = "",
    val actioninfos: List<OrderActionInfo> = emptyList(),
    val icon: String = "",
    val statusmsg: String = "",
    val ispresale: Int = 0,
    val isDeliveryException: Int = 0,
    val mergeOrder: Boolean = false,
    val canAppendOrder: Boolean = false
)

data class OrderProduct(
    val id: String = "",
    val title: String = "",
    val action: String = "",
    val price: OrderProductPrice = OrderProductPrice(),
    val pattern: String = "",
    val spec: OrderProductSpec = OrderProductSpec(),
    val lackOrExchangeNotShow: Boolean = false,
    val isbulkitem: Int = 0,
    val goodstag: String = "",
    val unit: String = "",
    val qty: Int = 0,
    val barcode: String = "",
    val rowNum: Int = 0,
    val performanceHourHour: Boolean = false,
    val imgurl: String = "",
    val subtitle: String = "",
    val num: Int = 0,
    val originalselectstate: Boolean = false,
    val sellerid: String = "",
    val calnum: String = "",
    val isrefund: Int = 0,
    val producttags: List<String> = emptyList(),
    val taglist: List<OrderProductTag> = emptyList(),
    val skusaletype: Int = 0,
    val isperformancehourhour: Boolean = false,
    val deliveryRuleId: Int = 0,
    val canNotBuy: Boolean = false,
    val bundlepromocode: String = ""
)

data class OrderProductPrice(
    val total: Int = 0,
    val value: Int = 0,
    val market: Int = 0,
    val lineprice: Int = 0,
    val priceafterlist: List<PriceAfterItem> = emptyList()
)

data class PriceAfterItem(
    val text: String = "",
    val type: String = ""
)

data class OrderProductSpec(
    val desc: String = ""
)

data class OrderProductTag(
    val type: String = "",
    val text: String = "",
    val sort: Int = 0
)

data class OrderSeller(
    val id: String = "",
    val title: String = "",
    val icon: String = ""
)

data class OrderTimeInfo(
    val generate: Long = 0,
    val pay: Long = 0,
    val pick: Long = 0,
    val delivery: Long = 0,
    val complete: Long = 0,
    val refunding: Long = 0,
    val refunded: Long = 0,
    val payend: Long = 0
)

data class OrderExpectTime(
    val date: Long = 0,
    val timeslots: List<TimeSlot> = emptyList()
)

data class TimeSlot(
    val from: String = "",
    val to: String = "",
    val slottype: String = "",
    val immediatedescription: String = ""
)

data class OrderActionInfo(
    val highlight: Int = 0,
    val actionname: String = "",
    val actiontype: Int = 0,
    val actionurl: String = "",
    val extra: Map<String, Any> = emptyMap(),
    val reasons: List<String> = emptyList()
) 