package dev.pigmomo.yhkit2025.api.utils.login

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.utils.SignUtils
import dev.pigmomo.yhkit2025.data.model.LoginTokenEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID
import kotlin.random.Random

/**
 * 处理验证码登录相关的工具类
 */
object SmsLoginUtils : BaseLoginUtils() {

    private object LoginConstants {
        const val LOGIN_PATH = "/web/passport/member/signIn/700"
    }

    /**
     * 使用手机号和验证码登录获取loginToken
     *
     * @param phone 用户手机号
     * @param smsCode 短信验证码
     * @param param 参数，格式为：deviceId,jysessionid,distinctId
     * @param deviceInfo 设备信息，如果为空，则生成新的设备信息
     * @param loginIndex 登录索引，默认为0
     * @param useProxy 是否使用代理
     * @param proxyType 代理类型，默认为"pinzan"
     * @return RequestResult 包含LoginTokenEntity或错误信息
     */
    suspend fun loginWithSmsCode(
        phone: String,
        smsCode: String,
        param: String = "",
        deviceInfo: DeviceInfo? = null,
        loginIndex: Int = 0,
        useProxy: Boolean = false,
        proxyType: String = "pinzan"
    ): RequestResult<LoginTokenEntity> = withContext(Dispatchers.IO) {
        try {
            // 1. 获取安全票据
            val securityTicketResult = fetchSecurityTicketForm2Captcha()
            val securityTicket = when (securityTicketResult) {
                is RequestResult.Success -> securityTicketResult.data
                is RequestResult.Error -> return@withContext RequestResult.Error(
                    securityTicketResult.error
                )
            }

            // 2. 生成设备信息和随机参数
            val deviceInfo = deviceInfo ?: DeviceInfoProvider.generate()
            var jysessionid: String
            var distinctId: String
            if (param.isNotEmpty()) {
                val parts = param.split(",")
                jysessionid = parts[1]
                distinctId = parts[2]
            } else {
                jysessionid = UUID.randomUUID().toString()
                distinctId = UUID.randomUUID().toString()
            }

            // 3. 创建HTTP客户端
            val client = createHttpClient(useProxy, proxyType)

            // 4. 构建登录请求
            val request = buildLoginRequest(
                phone, smsCode, securityTicket, deviceInfo, jysessionid, distinctId
            )

            // 5. 执行请求并处理响应
            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d("SmsLoginUtils", "Response body: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                // 解析响应并返回结果
                val (message, loginTokenEntity) = parseLoginResponse(
                    responseBody,
                    phone,
                    deviceInfo,
                    distinctId,
                    loginIndex
                )
                if (loginTokenEntity != null) {
                    RequestResult.Success(loginTokenEntity)
                } else {
                    RequestResult.Error(Exception(message))
                }
            } else {
                RequestResult.Error(Exception("Network request failed: ${response.code} - ${response.message}"))
            }
        } catch (e: Exception) {
            Log.e("SmsLoginUtils", "Login failed with exception", e)
            RequestResult.Error(e)
        }
    }

    /**
     * 构建登录请求
     */
    private suspend fun buildLoginRequest(
        phone: String,
        smsCode: String,
        ticket: SecurityTicket,
        deviceInfo: DeviceInfo,
        jysessionid: String,
        distinctId: String
    ): Request {
        val timestamp = System.currentTimeMillis()

        // 构建请求体
        val requestBodyJson = JSONObject().apply {
            put("hasCheck", "1")
            put("jysessionid", jysessionid)
            put("loginToken", "")
            put("marketchannelreq", JSONObject().apply {
                put("mainchannelid", "AppChannel")
                put("referenceid", deviceInfo.channel)
                put("secondarychannelid", "Default")
            })
            put("mobile", phone)
            put("phonenum", phone)
            put("randStr", ticket.randstr)
            put("riskCheckType", "1")
            put("riskCpuBuild", "armeabi-v7a")
            put("riskLoginType", "3")
            put("riskMobile", "")
            put("riskOperator", "联通")
            put("riskPhonePower", "${Random.Default.nextInt(30, 100)}")
            put("riskRegisterType", "3")
            put("riskScene", "2")
            put("securitycode", smsCode)
            put("securityticket", ticket.ticket)
            put("securityversion", "tc_v3")
            put("tracesignid", distinctId)
            put("unionId", "")
        }

        // 构建URL参数
        val urlParams = buildCommonUrlParams(jysessionid, deviceInfo, distinctId, timestamp)

        val urlWithParams =
            buildUrlWithParams(Constants.BASE_URL, LoginConstants.LOGIN_PATH, urlParams)

        // 计算签名
        val sortedParams =
            urlParams.entries.sortedBy { it.key }.joinToString("") { it.key + it.value }
        val needSignStr = sortedParams + requestBodyJson.toString()
        val sign = SignUtils.postToSign("/process", needSignStr)
        val fullUrl = "$urlWithParams&sign=$sign"

        // 构建请求
        val mediaType = Constants.CONTENT_TYPE.toMediaType()
        val requestBody = requestBodyJson.toString().toRequestBody(mediaType)

        // 获取通用请求头
        val headers = buildCommonHeaders(deviceInfo)

        // 构建请求
        val requestBuilder = Request.Builder()
            .url(fullUrl)
            .post(requestBody)

        // 添加所有请求头
        headers.forEach { (key, value) ->
            requestBuilder.addHeader(key, value)
        }

        return requestBuilder.build()
    }

    /**
     * 解析验证码登录响应并创建LoginTokenEntity
     */
    private fun parseLoginResponse(
        jsonResponse: String,
        phone: String,
        deviceInfo: DeviceInfo,
        distinctId: String,
        loginIndex: Int
    ): Pair<String, LoginTokenEntity?> {
        try {
            val json = JSONObject(jsonResponse)
            if (json.optInt("code") != 0) {
                val errorMessage = json.optString("message", "Unknown error")
                Log.e("SmsLoginUtils", "Response error: $errorMessage")
                return Pair(errorMessage, null)
            }

            val data =
                json.optJSONObject("data") ?: return Pair("Missing required data in response", null)

            // 验证码登录响应中的字段
            val uid = data.optString("uid")
            val refreshToken = data.optString("refresh_token")
            val accessToken = data.optString("access_token")
            val userKey = data.optString("userKey", "")

            // 检查必要字段
            if (uid.isEmpty() || refreshToken.isEmpty() || accessToken.isEmpty()) {
                return Pair("Missing required fields in response", null)
            }

            // 构建appParam
            val appParam = with(deviceInfo) {
                "$channel,$screen,$deviceId,$distinctId,$osVersion,$model,${Constants.NETWORK_TYPE},${brand},${Constants.APP_VERSION}"
            }

            // 获取时间戳并格式化
            val now = json.optLong("now", System.currentTimeMillis())
            val updateDate = SimpleDateFormat("MM.dd.yy", Locale.getDefault()).format(Date(now))

            // 检查操作结果
            val operation = data.optInt("operation", -1)
            val success = data.optInt("success", -1)

            /*if (success != 0) {
                return Pair("Login operation failed with code: $operation", null)
            }*/

            // 创建LoginTokenEntity
            val token = LoginTokenEntity(
                uid = uid,
                phoneNumber = phone,
                userKey = userKey,
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = data.optString("expires_in", "7200").toLong(),
                updateDate = updateDate,
                isNew = false,
                bargainFirst = false,
                activityLimited = false,
                yhCardLimited = false,
                appParam = appParam,
                extraNote = "",
                isLogin = true,
                loginIndex = loginIndex
            )
            return Pair("Success", token)
        } catch (e: Exception) {
            Log.e("SmsLoginUtils", "Error parsing login response", e)
            return Pair("Error parsing login response: ${e.message}", null)
        }
    }

    suspend fun getSmsCodeFromServer(): String = withContext(Dispatchers.IO) {
        try {
            val url = "http://utrevbsxlou.enhancer.cn/get_message"
            val client = OkHttpClient()

            val request = Request.Builder()
                .url(url)
                .build()

            val response = client.newCall(request).execute()
            val data = response.body?.let { JSONObject(it.string()) }
            if (data != null && data.getInt("code") == 200) {
                val phone = data.getJSONObject("message").getString("phone")
                val code = data.getJSONObject("message").getString("code")
                val param = data.getJSONObject("message").getString("param")
                val remainingMessages = data.getInt("remaining_messages")
                Log.i("getSmsCodeFromServer", "phone: $phone, code: $code")
                return@withContext "$phone----$code----$remainingMessages----$param"
            } else {
                Log.e("getSmsCodeFromServer", "failed to get phone and code")
                return@withContext ""
            }
        } catch (e: Exception) {
            Log.e("getSmsCodeFromServer", "failed to get phone and code", e)
            return@withContext ""
        }
    }

    /**
     * 创建一个空的包含验证码的LoginTokenEntity
     * @param phone 手机号
     * @param smsCode 验证码
     * @param param 参数，格式为：deviceId,jysessionid,distinctId
     * @return 包含LoginTokenEntity和DeviceInfo的Pair
     */
    fun createEmptyLoginTokenEntity(
        phone: String,
        smsCode: String,
        param: String = ""
    ): Pair<LoginTokenEntity, DeviceInfo> {
        var distinctId: String
        var deviceInfo: DeviceInfo
        if (param.isNotEmpty()) {
            val parts = param.split(",")
            distinctId = parts[2]
            deviceInfo = DeviceInfoProvider.generate(parts[0])
        } else {
            distinctId = UUID.randomUUID().toString()
            deviceInfo = DeviceInfoProvider.generate()
        }
        val appParam = with(deviceInfo) {
            "$channel,$screen,$deviceId,$distinctId,$osVersion,$model,${Constants.NETWORK_TYPE},${brand},${Constants.APP_VERSION}"
        }
        // 生成一个唯一的临时 uid，使用 "temp_" 前缀 + 手机号 + 时间戳 + 随机数
        val tempUid = "temp_${phone}_${System.currentTimeMillis()}_${Random.nextInt(10000)}"
        
        val token = LoginTokenEntity(
            uid = tempUid,
            phoneNumber = phone,
            userKey = "",
            accessToken = "",
            refreshToken = "",
            expiresIn = 0,
            updateDate = "",
            isNew = false,
            bargainFirst = false,
            activityLimited = false,
            yhCardLimited = false,
            appParam = appParam,
            extraNote = "",
            smsCode = smsCode,
            isLogin = false,
            loginIndex = 0
        )
        return Pair(token, deviceInfo)
    }
} 