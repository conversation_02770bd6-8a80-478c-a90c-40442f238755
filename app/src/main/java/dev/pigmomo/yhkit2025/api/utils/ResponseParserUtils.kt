package dev.pigmomo.yhkit2025.api.utils

import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import dev.pigmomo.yhkit2025.api.model.user.AddressResponse
import dev.pigmomo.yhkit2025.api.model.BaseResponse
import dev.pigmomo.yhkit2025.api.model.boostcoupon.BoostCouponGameCodeResponse
import dev.pigmomo.yhkit2025.api.model.card.CardListResponse
import dev.pigmomo.yhkit2025.api.model.card.CardIndexInfoResponse
import dev.pigmomo.yhkit2025.api.model.cart.CartResponse
import dev.pigmomo.yhkit2025.api.model.user.ShopResponse
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceResponse
import dev.pigmomo.yhkit2025.api.model.coupon.CouponListResponse
import dev.pigmomo.yhkit2025.api.model.boostcoupon.BoostCouponListResponse
import dev.pigmomo.yhkit2025.api.model.card.BindCardOrLinkResponse
import dev.pigmomo.yhkit2025.api.model.card.CancelSendCardResponse
import dev.pigmomo.yhkit2025.api.model.card.CardPlaceResponse
import dev.pigmomo.yhkit2025.api.model.card.CardTransInfoListByPageResponse
import dev.pigmomo.yhkit2025.api.model.card.ReceiveCardResponse
import dev.pigmomo.yhkit2025.api.model.card.SendCardResponse
import dev.pigmomo.yhkit2025.api.model.card.ValidateCardOrLinkResponse
import dev.pigmomo.yhkit2025.api.model.coupon.BoostCouponResponse
import dev.pigmomo.yhkit2025.api.model.coupon.NewPersonAssemblyDataResponse
import dev.pigmomo.yhkit2025.api.model.coupon.NewPersonPopupResponse
import dev.pigmomo.yhkit2025.api.model.order.OrderListResponse
import dev.pigmomo.yhkit2025.api.model.invitatinv2.InvitationRewardResponse
import dev.pigmomo.yhkit2025.api.model.invitatinv2.SuccessInviteListResponse
import dev.pigmomo.yhkit2025.api.model.credit.CreditResponse
import dev.pigmomo.yhkit2025.api.model.user.HomepageResponse
import dev.pigmomo.yhkit2025.api.model.user.UserInfoResponse
import dev.pigmomo.yhkit2025.api.model.coupon.NewUserCouponInfoResponse
import dev.pigmomo.yhkit2025.api.model.coupon.NewUserCouponReceiveResponse
import dev.pigmomo.yhkit2025.api.model.coupon.KindCouponResponse
import dev.pigmomo.yhkit2025.api.model.credit.DividePointJoinTeamResponse
import dev.pigmomo.yhkit2025.api.model.credit.DividePointTeamResponse
import dev.pigmomo.yhkit2025.api.model.credit.SignRewardDetailResponse
import dev.pigmomo.yhkit2025.api.model.handluck.HandLuckDrawLotteryResponse
import dev.pigmomo.yhkit2025.api.model.handluck.HandLuckRewardListResponse
import dev.pigmomo.yhkit2025.api.model.handluck.HandLuckSlotMachineDataResponse
import dev.pigmomo.yhkit2025.api.model.invitatinv2.InvitationV2ActivityInfoResponse
import dev.pigmomo.yhkit2025.api.model.order.AfterSalesListResponse
import dev.pigmomo.yhkit2025.api.model.order.InvoiceCanApplyOrderListResponse
import dev.pigmomo.yhkit2025.api.model.order.OrderDeliveryInfoResponse
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailResponse
import dev.pigmomo.yhkit2025.api.model.order.OrderPrepayResponse
import dev.pigmomo.yhkit2025.api.model.order.OrderRedEnvelopeCheckResponse
import dev.pigmomo.yhkit2025.api.model.order.UpdateDeliveryInfoResponse
import dev.pigmomo.yhkit2025.api.model.order.OrderCancelResponse
import dev.pigmomo.yhkit2025.api.model.order.OrderAfterSalesResponse
import dev.pigmomo.yhkit2025.api.model.order.OrderDeleteResponse
import dev.pigmomo.yhkit2025.api.model.invitatinv2.InvitationV2BindResponse
import dev.pigmomo.yhkit2025.api.model.order.OrderConfirmResponse

/**
 * API响应解析工具类
 */
object ResponseParserUtils {
    val gson = Gson()
    const val TAG = "ResponseParserUtils"
    
    /**
     * 通用响应解析方法
     * @param jsonString JSON字符串
     * @return 解析后的对象，解析失败返回null
     */
    inline fun <reified T> parseResponse(jsonString: String): BaseResponse<T>? {
        return try {
            val type = object : TypeToken<BaseResponse<T>>() {}.type
            gson.fromJson(jsonString, type)
        } catch (e: JsonSyntaxException) {
            Log.e(TAG, "Parse response failed: ${e.message}")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing response: ${e.message}")
            null
        }
    }
    
    /**
     * 解析JSON字符串为列表
     * @param jsonString JSON字符串
     * @return 解析后的列表，解析失败返回空列表
     */
    inline fun <reified T> parseList(jsonString: String): List<T> {
        return try {
            val type = object : TypeToken<List<T>>() {}.type
            gson.fromJson(jsonString, type)
        } catch (e: JsonSyntaxException) {
            Log.e(TAG, "Parse list failed: ${e.message}")
            emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing list: ${e.message}")
            emptyList()
        }
    }

    /**
     * 解析地址列表响应
     * @param jsonString JSON字符串
     * @return 解析后的地址响应对象，解析失败返回null
     */
    fun parseAddressResponse(jsonString: String): AddressResponse? {
        return try {
            gson.fromJson(jsonString, AddressResponse::class.java)
        } catch (e: JsonSyntaxException) {
            Log.e(TAG, "Parse address response failed: ${e.message}")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing address response: ${e.message}")
            null
        }
    }
    
    /**
     * 解析店铺信息响应
     * @param jsonString JSON字符串
     * @return 解析后的店铺响应对象，解析失败返回null
     */
    fun parseShopResponse(jsonString: String): ShopResponse? {
        return try {
            gson.fromJson(jsonString, ShopResponse::class.java)
        } catch (e: JsonSyntaxException) {
            Log.e(TAG, "Parse shop response failed: ${e.message}")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing shop response: ${e.message}")
            null
        }
    }

    /**
     * 解析购物车响应
     * @param jsonString JSON字符串
     * @return 解析后的购物车响应对象，解析失败返回null
     */
    fun parseCartResponse(jsonString: String): CartResponse? {
        return try {
            gson.fromJson(jsonString, CartResponse::class.java)
        } catch (e: JsonSyntaxException) {
            Log.e(TAG, "Parse cart response failed: ${e.message}")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing cart response: ${e.message}")
            null
        }
    }

    /**
     * 解析永辉卡列表响应
     * @param jsonString JSON字符串
     * @return 解析后的永辉卡列表响应对象，解析失败返回null
     */
    fun parseCardListResponse(jsonString: String): CardListResponse? {
        return try {
            gson.fromJson(jsonString, CardListResponse::class.java)
        } catch (e: JsonSyntaxException) {
            Log.e(TAG, "Parse card list response failed: ${e.message}")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing card list response: ${e.message}")
            null
        }
    }
    
    /**
     * 解析永辉卡索引信息响应
     * @param jsonString JSON字符串
     * @return 解析后的永辉卡索引信息响应对象，解析失败返回null
     */
    fun parseCardIndexInfoResponse(jsonString: String): CardIndexInfoResponse? {
        return try {
            gson.fromJson(jsonString, CardIndexInfoResponse::class.java)
        } catch (e: JsonSyntaxException) {
            Log.e(TAG, "Parse card index info response failed: ${e.message}")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing card index info response: ${e.message}")
            null
        }
    }

    /**
     * 解析订单下单响应
     * @param jsonString JSON字符串
     * @return 解析后的订单下单响应对象，解析失败返回null
     */
    fun parseOrderPlaceResponse(jsonString: String): OrderPlaceResponse? {
        return try {
            // 使用GSON的TypeToken处理复杂嵌套结构，可以更好地兼容数据结构不一致的情况
            val type = object : TypeToken<OrderPlaceResponse>() {}.type
            gson.fromJson(jsonString, type)
        } catch (e: JsonSyntaxException) {
            Log.e(TAG, "Parse order place response failed: ${e.message}")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing order place response: ${e.message}")
            null
        }
    }

    /**
     * 解析优惠券列表响应
     * @param jsonString JSON字符串
     * @return 优惠券响应对象
     */
    fun parseCouponResponse(jsonString: String): CouponListResponse? {
        return try {
            gson.fromJson(jsonString, CouponListResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse coupon response", e)
            null
        }
    }

    /**
     * 解析助力券列表响应
     * @param jsonString JSON字符串
     * @return 助力券响应对象
     */
    fun parseBoostCouponListResponse(jsonString: String): BoostCouponListResponse? {
        return try {
            gson.fromJson(jsonString, BoostCouponListResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse boost coupon response", e)
            null
        }
    }

    /**
     * 解析订单列表响应
     * @param jsonString JSON字符串
     * @return 订单列表响应对象
     */
    fun parseOrderListResponse(jsonString: String): OrderListResponse? {
        return try {
            gson.fromJson(jsonString, OrderListResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse order list response", e)
            null
        }
    }

    /**
     * 解析邀请奖励列表响应
     * @param jsonString JSON字符串
     * @return 邀请奖励响应对象
     */
    fun parseInvitationRewardResponse(jsonString: String): InvitationRewardResponse? {
        return try {
            gson.fromJson(jsonString, InvitationRewardResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse invitation reward response", e)
            null
        }
    }

    /**
     * 解析成功邀请列表响应
     * @param jsonString JSON字符串
     * @return 成功邀请列表响应对象
     */
    fun parseSuccessInviteListResponse(jsonString: String): SuccessInviteListResponse? {
        return try {
            gson.fromJson(jsonString, SuccessInviteListResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse success invite list response", e)
            null
        }
    }

    /**
     * 解析积分详情响应
     * @param jsonString JSON字符串
     * @return 积分详情响应对象
     */
    fun parseCreditResponse(jsonString: String): CreditResponse? {
        return try {
            gson.fromJson(jsonString, CreditResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse credit response", e)
            null
        }
    }
    
    /**
     * 解析首页响应数据
     * @param jsonString JSON字符串
     * @return 首页响应对象
     */
    fun parseHomepageResponse(jsonString: String): HomepageResponse? {
        return try {
            gson.fromJson(jsonString, HomepageResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse homepage response", e)
            null
        }
    }

    /**
     * 解析用户信息响应
     * @param jsonString JSON字符串
     * @return 用户信息响应对象
     */
    fun parseUserInfoResponse(jsonString: String): UserInfoResponse? {
        return try {
            gson.fromJson(jsonString, UserInfoResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse user info response", e)
            null
        }
    }

    /**
     * 解析卡片下单响应
     * @param jsonString JSON字符串
     * @return 卡片下单响应对象
     */
    fun parseCardPlaceResponse(jsonString: String): CardPlaceResponse? {
        return try {
            gson.fromJson(jsonString, CardPlaceResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse card place response", e)
            null
        }
    }

    /**
     * 解析领取卡片响应
     * @param jsonString JSON字符串
     * @return 领取卡片响应对象
     */
    fun parseReceiveCardResponse(jsonString: String): ReceiveCardResponse? {
        return try {
            gson.fromJson(jsonString, ReceiveCardResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse receive card response", e)
            null
        }
    }

    /**
     * 解析绑定卡片响应
     * @param jsonString JSON字符串
     * @return 绑定卡片响应对象
     */
    fun parseBindCardOrLinkResponse(jsonString: String): BindCardOrLinkResponse? {
        return try {
            gson.fromJson(jsonString, BindCardOrLinkResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse bind card response", e)
            null
        }
    }

    /**
     * 解析验证卡片响应
     * @param jsonString JSON字符串
     * @return 验证卡片响应对象
     */
    fun parseValidateCardOrLinkResponse(jsonString: String): ValidateCardOrLinkResponse? {
        return try {
            gson.fromJson(jsonString, ValidateCardOrLinkResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse validate card or link response", e)
            null
        }
    }

    /**
     * 解析新人优惠券信息响应
     * @param jsonString JSON字符串
     * @return 新人优惠券信息响应对象
     */
    fun parseNewPersonCouponInfoResponse(jsonString: String): NewPersonAssemblyDataResponse? {
        return try {
            gson.fromJson(jsonString, NewPersonAssemblyDataResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse new person coupon info response", e)
            null
        }
    }

    /**
     * 解析新人优惠券领取响应
     * @param jsonString JSON字符串
     * @return 新人优惠券领取响应对象
     */
    fun parseNewPersonPopupResponse(jsonString: String): NewPersonPopupResponse? {
        return try {
            gson.fromJson(jsonString, NewPersonPopupResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse new person popup response", e)
            null
        }
    }
    
    /**
     * 解析新用户优惠券信息响应
     * @param jsonString JSON字符串
     * @return 新用户优惠券信息响应对象
     */
    fun parseNewUserCouponInfoResponse(jsonString: String): NewUserCouponInfoResponse? {
        return try {
            gson.fromJson(jsonString, NewUserCouponInfoResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse new user coupon info response", e)
            null
        }
    }

    /**
     * 解析新用户优惠券领取响应
     * @param jsonString JSON字符串
     * @return 新用户优惠券领取响应对象
     */
    fun parseNewUserCouponReceiveResponse(jsonString: String): NewUserCouponReceiveResponse? {
        return try {
            gson.fromJson(jsonString, NewUserCouponReceiveResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse new user coupon receive response", e)
            null
        }
    }

    /**
     * 解析KindCoupon响应
     * @param jsonString JSON字符串
     * @return KindCoupon响应对象
     */
    fun parseKindCouponResponse(jsonString: String): KindCouponResponse? {
        return try {
            gson.fromJson(jsonString, KindCouponResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse kind coupon response", e)
            null
        }
    }

    /**
     * 解析拼手气数据响应
     * @param jsonString JSON字符串
     * @return 拼手气数据响应对象
     */
    fun parseHandLuckSlotMachineDataResponse(jsonString: String): HandLuckSlotMachineDataResponse? {
        return try {
            gson.fromJson(jsonString, HandLuckSlotMachineDataResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse hand luck get slot machine data response", e)
            null
        }
    }

    /**
     * 解析拼手气领取响应
     * @param jsonString JSON字符串
     * @return 拼手气领取响应对象
     */
    fun parseHandLuckDrawLotteryResponse(jsonString: String): HandLuckDrawLotteryResponse? {
        return try {
            gson.fromJson(jsonString, HandLuckDrawLotteryResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse hand luck draw lottery response", e)
            null
        }
    }

    /**
     * 解析卡片交易信息列表响应
     * @param jsonString JSON字符串
     * @return 卡片交易信息列表响应对象
     */
    fun parseCardTransInfoListByPageResponse(jsonString: String): CardTransInfoListByPageResponse? {
        return try {
            gson.fromJson(jsonString, CardTransInfoListByPageResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse card trans info list by page response", e)
            null
        }
    }

    /**
     * 解析订单详情响应
     * @param jsonString JSON字符串
     * @return 订单详情响应对象
     */
    fun parseOrderDetailResponse(jsonString: String): OrderDetailResponse? {
        return try {
            gson.fromJson(jsonString, OrderDetailResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse order detail response", e)
            null
        }
    }   

    /**
     * 解析订单红包检查响应
     * @param jsonString JSON字符串
     * @return 订单红包检查响应对象
     */
    fun parseOrderRedEnvelopeCheckResponse(jsonString: String): OrderRedEnvelopeCheckResponse? {
        return try {
            gson.fromJson(jsonString, OrderRedEnvelopeCheckResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse order red envelope check response", e)
            null
        }
    }

    /**
     * 解析助力券游戏码响应
     * @param jsonString JSON字符串
     * @return 助力券游戏码响应对象
     */
    fun parseBoostCouponGameCodeResponse(jsonString: String): BoostCouponGameCodeResponse? {
        return try {
            gson.fromJson(jsonString, BoostCouponGameCodeResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse boost coupon game code response", e)
            null
        }
    }

    /**
     * 解析赠送卡片响应
     * @param jsonString JSON字符串
     * @return 赠送卡片响应对象
     */
    fun parseSendCardResponse(jsonString: String): SendCardResponse? {
        return try {
            gson.fromJson(jsonString, SendCardResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse send card response", e)
            null
        }
    }

    /**
     * 解析赠送卡片简单响应
     * @param jsonString JSON字符串
     * @return 赠送卡片简单响应对象
     */
    fun parseSendCardSimpleResponse(jsonString: String): SendCardResponse? {
        return try {
            // 与赠送卡片响应相同，activityId 为空
            gson.fromJson(jsonString, SendCardResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse send card simple response", e)
            null
        }
    }

    /**
     * 解析取消赠送卡片响应
     * @param jsonString JSON字符串
     * @return 取消赠送卡片响应对象
     */
    fun parseCancelSendCardResponse(jsonString: String): CancelSendCardResponse? {
        return try {
            gson.fromJson(jsonString, CancelSendCardResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse cancel send card response", e)
            null
        }
    }

    /**
     * 解析可申请发票的订单列表响应
     * @param jsonString JSON字符串
     * @return 可申请发票的订单列表响应对象
     */
    fun parseInvoiceCanApplyOrderListResponse(jsonString: String): InvoiceCanApplyOrderListResponse? {
        return try {
            gson.fromJson(jsonString, InvoiceCanApplyOrderListResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse invoice can apply order list response", e)
            null
        }
    }

    /**
     * 解析售后订单列表响应
     * @param jsonString JSON字符串
     * @return 售后订单列表响应对象
     */
    fun parseAfterSalesListResponse(jsonString: String): AfterSalesListResponse? {
        return try {
            gson.fromJson(jsonString, AfterSalesListResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse after sales list response", e)
            null
        }
    }

    /**
     * 解析订单预支付响应
     * @param jsonString JSON字符串
     * @return 订单预支付响应对象
     */
    fun parseOrderPrepayResponse(jsonString: String): OrderPrepayResponse? {
        return try {
            gson.fromJson(jsonString, OrderPrepayResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse order prepay response", e)
            null
        }
    }

    /**
     * 解析更新配送信息响应
     * @param jsonString JSON字符串
     * @return 更新配送信息响应对象
     */
    fun parseUpdateDeliveryInfoResponse(jsonString: String): UpdateDeliveryInfoResponse? {
        return try {
            gson.fromJson(jsonString, UpdateDeliveryInfoResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse update delivery info response", e)
            null
        }
    }

    /**
     * 解析订单配送信息响应
     * @param jsonString JSON字符串
     * @return 订单配送信息响应对象
     */
    fun parseOrderDeliveryInfoResponse(jsonString: String): OrderDeliveryInfoResponse? {
        return try {
            gson.fromJson(jsonString, OrderDeliveryInfoResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse order delivery info response", e)
            null
        }
    }

    /**
     * 解析取消订单响应
     * @param jsonString JSON字符串
     * @return 取消订单响应对象
     */
    fun parseOrderCancelResponse(jsonString: String): OrderCancelResponse? {
        return try {
            gson.fromJson(jsonString, OrderCancelResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse order cancel response", e)
            null
        }
    }

    /**
     * 解析申请售后响应
     * @param jsonString JSON字符串
     * @return 申请售后响应对象
     */
    fun parseOrderAfterSalesResponse(jsonString: String): OrderAfterSalesResponse? {
        return try {
            gson.fromJson(jsonString, OrderAfterSalesResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse order after sales response", e)
            null
        }
    }

    /**
     * 解析删除订单响应
     * @param jsonString JSON字符串
     * @return 删除订单响应对象
     */
    fun parseOrderDeleteResponse(jsonString: String): OrderDeleteResponse? {
        return try {
            gson.fromJson(jsonString, OrderDeleteResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse order delete response", e)
            null
        }
    }

    /**
     * 解析邀请V2活动信息响应
     * @param jsonString JSON字符串
     * @return 邀请V2活动信息响应对象
     */
    fun parseInvitationV2ActiveInfoResponse(jsonString: String): InvitationV2ActivityInfoResponse? {
        return try {
            gson.fromJson(jsonString, InvitationV2ActivityInfoResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse invitation v2 active info response", e)
            null
        }
    }

    /**
     * 解析绑定邀请关系V2响应
     * @param jsonString JSON字符串
     * @return 绑定邀请关系V2响应对象
     */
    fun parseInvitationV2BindResponse(jsonString: String): InvitationV2BindResponse? {
        return try {
            gson.fromJson(jsonString, InvitationV2BindResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse invitation v2 bind response", e)
            null
        }
    }

    /**
     * 解析拼手气奖励列表响应
     * @param jsonString JSON字符串
     * @return 拼手气奖励列表响应对象
     */
    fun parseHandLuckTotalRewardListResponse(jsonString: String): HandLuckRewardListResponse? {
        return try {
            gson.fromJson(jsonString, HandLuckRewardListResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse hand luck total reward list response", e)
            null
        }
    }

    /**
     * 解析签到奖励详情响应
     * @param jsonString JSON字符串
     * @return 签到奖励详情响应对象
     */
    fun parseSignRewardDetailResponse(jsonString: String): SignRewardDetailResponse? {
        return try {
            gson.fromJson(jsonString, SignRewardDetailResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse sign reward detail response", e)
            null
        }
    }

    /**
     * 解析组队瓜分响应
     * @param jsonString JSON字符串
     * @return 组队瓜分响应对象
     */ 
    fun parseDividePointTeamResponse(jsonString: String): DividePointTeamResponse? {
        return try {
            gson.fromJson(jsonString, DividePointTeamResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse point team response", e)
            null
        }
    }
    
    /**
     * 解析组队瓜分加入响应
     * @param jsonString JSON字符串
     * @return 组队瓜分加入响应对象
     */ 
    fun parseDividePointJoinTeamResponse(jsonString: String): DividePointJoinTeamResponse? {
        return try {
            gson.fromJson(jsonString, DividePointJoinTeamResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse point team join response", e)
            null
        }
    }

    /**
     * 解析助力券响应
     * @param jsonString JSON字符串
     * @return 助力券响应对象
     */
    fun parseBoostCouponResponse(jsonString: String): BoostCouponResponse? {
        return try {
            gson.fromJson(jsonString, BoostCouponResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse boost coupon response", e)
            null
        }
    }

    fun parseOrderConfirmResponse(jsonString: String): OrderConfirmResponse? {
        return try {
            gson.fromJson(jsonString, OrderConfirmResponse::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse order confirm response", e)
            null
        }
    }
    
}