package dev.pigmomo.yhkit2025.api.model.user

data class AddressResponse(
    val code: Int = 0,
    val data: AddressData? = null,
    val message: String = "",
    val now: Long = 0
)

data class AddressData(
    val list: List<AddressItem> = emptyList()
)

data class AddressItem(
    val address: AddressDetail,
    val deliverydesc: String = "",
    val gender: String = "",
    var id: String = "",
    val isdefault: Int = 0,
    val location: AddressLocation? = null,
    val name: String = "",
    val nextdaydeliver: Int = 0,
    val phone: String = "",
    val scope: Int = 0
)

data class AddressDetail(
    val area: String = "",
    val city: String = "",
    val cityid: Int = 0,
    val detail: String = "",
    val provname: String = ""
)

data class AddressLocation(
    val lat: String = "",
    val lng: String = ""
) 