package dev.pigmomo.yhkit2025.api.model.coupon

/**
 * 助力券响应数据模型
 */
data class BoostCouponResponse(
    val code: Int = 0,
    val message: String = "",
    val data: BoostCouponData? = null,
    val now: Long = 0
)

/**
 * 助力券数据
 */
data class BoostCouponData(
    val boostCouponBoostDTO: BoostCouponBoostInfo? = null,
    val boostCouponVOS: List<BoostCouponVO> = emptyList(),
    val titleAmountStr: String? = null,
    val newPersonActivityCode: String? = null,
    val resourceDTOS: List<ResourceDTO> = emptyList()
)

/**
 * 助力券助力信息
 */
data class BoostCouponBoostInfo(
    val newPerson: Int = 0,
    val status: Int = 0,
    val promotionCode: String? = null,
    val lastIsNotNewPerson: Boolean = false
)

/**
 * 助力券信息
 */
data class BoostCouponVO(
    val id: String = "",
    val name: String = "",
    val amount: String = "",
    val type: Int = 0,
    val status: Int = 0
)

/**
 * 资源信息
 */
data class ResourceDTO(
    val id: String = "",
    val type: String = "",
    val url: String = "",
    val resourceType: String = ""
) 