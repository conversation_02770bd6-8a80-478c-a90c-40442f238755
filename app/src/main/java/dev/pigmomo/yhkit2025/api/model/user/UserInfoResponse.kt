package dev.pigmomo.yhkit2025.api.model.user

/**
 * 账户信息响应数据模型
 * 用于解析获取用户账户信息接口返回的JSON数据
 */
data class UserInfoResponse(
    val code: Int = 0,                // 响应状态码，0表示成功
    val message: String = "",         // 响应消息
    val data: UserInfoData? = null, // 响应数据对象
    val now: Long = 0                 // 服务器时间戳
)

/**
 * 账户信息数据详情
 * 包含用户个人信息及标签信息
 */
data class UserInfoData(
    val allfilled: Boolean = false,         // 是否已填写完所有信息
    val avatarImageUrl: String = "",        // 用户头像URL
    val credit: Int = 0,                    // 用户积分
    val giftcard: UserInfoGiftCard = UserInfoGiftCard(),    // 礼品卡信息
    val gradeDesc: String = "",             // 会员等级描述
    val hadsendcredit: Boolean = false,     // 是否已发送积分
    val hassettags: Boolean = false,        // 是否已设置标签
    val memberid: String = "",              // 会员ID
    val mobile: String = "",                // 手机号码
    val nickname: String = "",              // 用户昵称
    val signupTime: String = "",            // 注册时间
    val tagprofiles: List<TagProfile> = emptyList(), // 标签配置列表
    val wechatname: String = ""             // 微信名称
)

/**
 * 礼品卡信息
 */
data class UserInfoGiftCard(
    val amount: Int = 0                     // 礼品卡金额
)

/**
 * 标签配置
 * 定义标签组的属性和包含的标签
 */
data class TagProfile(
    val optional: Boolean = false,          // 是否可选
    val single: Boolean = false,            // 是否单选
    val tags: List<UserInfoTag> = emptyList(),      // 标签列表
    val title: String = ""                  // 标签组标题
)

/**
 * 标签项
 * 定义单个标签的属性
 */
data class UserInfoTag(
    val name: String = "",                  // 标签名称
    val tagged: Boolean = false             // 是否已标记
) 