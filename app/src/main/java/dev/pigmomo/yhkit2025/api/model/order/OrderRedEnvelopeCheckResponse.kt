package dev.pigmomo.yhkit2025.api.model.order

/**
 * 订单红包检查响应数据类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 红包检查数据
 * @property now 当前时间戳
 */
data class OrderRedEnvelopeCheckResponse(
    val code: Int = 0,
    val message: String = "",
    val data: OrderRedEnvelopeCheckData? = null,
    val now: Long = 0
)

/**
 * 订单红包检查数据类
 * @property title 红包标题
 * @property bunchid 红包ID
 * @property imgurl 图片URL
 * @property iconurl 图标URL
 * @property wechaturl 微信URL
 * @property miniurl 小程序URL
 * @property sharebuoyurl 分享浮标URL
 * @property sharepopupurl 分享弹窗URL
 * @property payshareurl 支付分享URL
 * @property jumpnewurl 是否跳转新URL
 * @property oldtitle 旧标题
 * @property nickname 昵称
 */
data class OrderRedEnvelopeCheckData(
    val title: String = "",
    val bunchid: String = "",
    val imgurl: String = "",
    val iconurl: String = "",
    val wechaturl: String = "",
    val miniurl: String = "",
    val sharebuoyurl: String = "",
    val sharepopupurl: String = "",
    val payshareurl: String = "",
    val jumpnewurl: Boolean = false,
    val oldtitle: String = "",
    val nickname: String = ""
) 