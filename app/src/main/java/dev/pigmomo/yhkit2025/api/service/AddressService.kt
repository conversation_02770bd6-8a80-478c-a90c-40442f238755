package dev.pigmomo.yhkit2025.api.service

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestHelper
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.SignType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.util.UUID

/**
 * 地址服务类
 * 提供用户地址相关的API调用方法
 */
class AddressService(requestHelper: RequestHelper) : BaseService(requestHelper) {

    private val tag = "AddressService"

    private val serviceType = requestHelper.getServiceType()

    /**
     * 获取用户地址列表
     * @return 地址列表
     */
    suspend fun getAllAddress(shopId: String = ""): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    if (shopId.isEmpty()) {
                        //此处shopId可为空，不影响获取地址列表
                        Log.d(tag, "getAllAddress: shopId is empty")
                    }

                    val commonParams = requestHelper.getAppCommonParams()
                    val businessParams = buildAppBusinessParams()
                    businessParams["uid"] = requestHelper.getUid()
                    if (shopId.isNotEmpty()) {
                        businessParams["shopid"] = shopId
                    }

                    val urlWithParams =
                        buildAppApiUrl(
                            RequestConfig.Path.ADDRESS_LIST_PATH,
                            businessParams,
                            commonParams
                        )
                    val needSignStr =
                        urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                            .replace("=", "")
                            .replace("&", "")
                    val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                    if (sign.isEmpty()) {
                        Log.e(tag, "getAllAddress: sign empty, service may not be initialized")
                        return@withContext RequestResult.Error(Exception("服务未初始化"))
                    }
                    val fullUrl = "$urlWithParams&sign=$sign"

                    //此处xyhBizParams可为空，设置为XYH_BIZ_PARAMS_ORIGIN
                    val xyhBizParams =
                        requestHelper.getXyhBizParams()
                            .ifEmpty { RequestConfig.XYH_BIZ_PARAMS_ORIGIN_APP }
                    val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                    requestHelper.get(fullUrl, headers)
                }

                "mini" -> {
                    if (shopId.isEmpty()) {
                        //此处shopId可为空，不影响获取地址列表
                        Log.d(tag, "getAllAddress: shopId is empty")
                    }

                    val commonParams = requestHelper.getMiniProgramCommonParams()
                    val businessParams = buildMiniProgramBusinessParams()
                    businessParams["memberId"] = requestHelper.getUid()
                    businessParams["phonenum"] = requestHelper.getPhoneNumber()
                    businessParams["distinctId"] = requestHelper.getUid()
                    if (shopId.isNotEmpty()) {
                        businessParams["shopid"] = shopId
                    }

                    val urlWithParams =
                        buildMiniApiUrl(
                            RequestConfig.Path.ADDRESS_LIST_PATH,
                            businessParams,
                            commonParams
                        )
                    val sign = requestHelper.generateSign(
                        urlWithParams,
                        "",
                        SignType.MINIPROGRAM.value,
                        requestHelper.getUid()
                    )
                    if (sign.isEmpty()) {
                        Log.e(tag, "getAllAddress: sign empty, check sign type")
                        return@withContext RequestResult.Error(Exception("签名错误"))
                    }
                    val fullUrl = "$urlWithParams&sign=$sign"

                    //此处xyhBizParams可为空，设置为XYH_BIZ_PARAMS_ORIGIN
                    val xyhBizParams =
                        requestHelper.getXyhBizParams()
                            .ifEmpty { RequestConfig.XYH_BIZ_PARAMS_ORIGIN_MINI_PROGRAM }
                    val headers = buildMiniStandardHeaders(xyhBizParams, false)

                    requestHelper.get(fullUrl, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }

    /**
     * 删除地址
     * @param addressId 要删除的地址ID
     * @return 删除结果
     */
    suspend fun deleteAddress(addressId: String): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    val commonParams = requestHelper.getAppCommonParams()
                    val businessParams = buildAppBusinessParams()
                    val urlWithParams =
                        buildAppApiUrl(
                            RequestConfig.Path.DELETE_ADDRESS_PATH,
                            businessParams,
                            commonParams
                        )

                    // 构建请求体
                    val requestBody = """{"addressid":"$addressId"}"""

                    val needSignStr =
                        urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                            .replace("=", "")
                            .replace("&", "") + requestBody

                    // 生成签名
                    val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                    if (sign.isEmpty()) {
                        Log.e(tag, "deleteAddress: sign empty, service may not be initialized")
                        return@withContext RequestResult.Error(Exception("服务未初始化"))
                    }
                    val fullUrl = "$urlWithParams&sign=$sign"

                    val xyhBizParams = requestHelper.getXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "deleteAddress: xyhBizParams empty, config may not be set")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }

                    val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                    requestHelper.postJson(fullUrl, requestBody, headers)
                }

                "mini" -> {
                    val commonParams = requestHelper.getMiniProgramCommonParams()
                    val businessParams = buildMiniProgramBusinessParams()
                    businessParams["distinctId"] = requestHelper.getUid()
                    val urlWithParams =
                        buildMiniApiUrl(
                            RequestConfig.Path.DELETE_ADDRESS_PATH,
                            businessParams,
                            commonParams
                        )

                    // 构建请求体
                    val requestBody = """{"addressid":"$addressId"}"""

                    // 生成签名
                    val sign =
                        requestHelper.generateSign(
                            urlWithParams,
                            requestBody,
                            SignType.MINIPROGRAM.value,
                            requestHelper.getUid()
                        )
                    if (sign.isEmpty()) {
                        Log.e(tag, "deleteAddress: sign empty, check sign type")
                        return@withContext RequestResult.Error(Exception("签名错误"))
                    }
                    val fullUrl = "$urlWithParams&sign=$sign"

                    val xyhBizParams = requestHelper.getXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "deleteAddress: xyhBizParams empty, config may not be set")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }

                    val headers = buildMiniStandardHeaders(xyhBizParams, false)

                    requestHelper.postJson(fullUrl, requestBody, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }

    /**
     * 添加用户地址
     * @param addAddress 地址信息JSON字符串
     * @param addLocation 位置信息JSON字符串
     * @param addName 收件人姓名
     * @param addPhone 收件人电话
     * @return 添加地址结果响应结果
     */
    suspend fun addAddress(
        addAddress: String,
        addLocation: String,
        addName: String,
        addPhone: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val commonParams = requestHelper.getAppCommonParams()

                val addAddressJson = JSONObject(addAddress)
                val district = addAddressJson.getString("district")
                val requestBody =
                    """{"address":$addAddress,"district":"$district","foodsupport":0,"isSearch":false,"ischanged":false,"isdefault":0,"itemType":0,"location":$addLocation,"name":"$addName","nextdaydeliver":0,"phone":"$addPhone","scope":0,"validateId":"1","_uuid":"${UUID.randomUUID()}"}"""

                val businessParams = buildAppBusinessParams()
                businessParams["abdata"] = "{\"poi_address_change_855\":\"2\"}"
                val urlWithParams =
                    buildAppApiUrl(RequestConfig.Path.ADD_ADDRESS_PATH, businessParams, commonParams)

                val needSignStr =
                    (urlWithParams.split("?")[1].split("&").sorted().joinToString("&").replace("=", "")
                        .replace("&", "") + requestBody).replace(
                        "%7B%22poi_address_change_855%22%3A%222%22%7D",
                        "{\"poi_address_change_855\":\"2\"}"
                    )

                // 生成签名
                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "addAddress: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                val xyhBizParams =
                    requestHelper.getXyhBizParams().ifEmpty { RequestConfig.XYH_BIZ_PARAMS_ORIGIN_APP }
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "addAddress: xyhBizParams empty, config may not be set")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val commonParams = requestHelper.getMiniProgramCommonParams()

                val addAddressJson = JSONObject(addAddress)
                val district = addAddressJson.getString("district")
                val requestBody =
                    """{"address":$addAddress,"district":"$district","foodsupport":0,"isSearch":false,"ischanged":false,"isdefault":0,"itemType":0,"location":$addLocation,"name":"$addName","nextdaydeliver":0,"phone":"$addPhone","scope":0,"validateId":"1","_uuid":"${UUID.randomUUID()}"}"""

                val businessParams = buildMiniProgramBusinessParams()
                businessParams["abdata"] = "{\"poi_address_change_855\":\"2\"}"
                businessParams["distinctId"] = requestHelper.getUid()
                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.ADD_ADDRESS_PATH,
                        businessParams,
                        commonParams
                    )

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                if (sign.isEmpty()) {
                    Log.e(tag, "addAddress: sign empty, check sign type")
                    return@withContext RequestResult.Error(Exception("签名错误"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                val xyhBizParams =
                    requestHelper.getXyhBizParams()
                        .ifEmpty { RequestConfig.XYH_BIZ_PARAMS_ORIGIN_MINI_PROGRAM }
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "addAddress: xyhBizParams empty, config may not be set")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }
}