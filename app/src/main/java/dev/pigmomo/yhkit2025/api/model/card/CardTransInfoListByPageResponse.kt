package dev.pigmomo.yhkit2025.api.model.card

/**
 * 卡交易信息列表分页响应数据类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 卡交易信息列表数据
 * @property now 当前时间戳
 */
data class CardTransInfoListByPageResponse(
    val code: Int = 0,
    val message: String = "",
    val data: CardTransInfoListData? = null,
    val now: Long = 0
)

/**
 * 卡交易信息列表数据类
 * @property total 总记录数
 * @property list 交易信息列表
 * @property pageNum 当前页码
 * @property pageSize 每页大小
 * @property size 当前页记录数
 * @property startRow 起始行号
 * @property endRow 结束行号
 * @property pages 总页数
 * @property prePage 上一页页码
 * @property nextPage 下一页页码
 * @property isFirstPage 是否第一页
 * @property isLastPage 是否最后一页
 * @property hasPreviousPage 是否有上一页
 * @property hasNextPage 是否有下一页
 * @property navigatePages 导航页码数
 * @property navigatepageNums 导航页码列表
 * @property navigateFirstPage 导航第一页
 * @property navigateLastPage 导航最后一页
 */
data class CardTransInfoListData(
    val total: Int = 0,
    val list: List<CardTransInfo> = emptyList(),
    val pageNum: Int = 0,
    val pageSize: Int = 0,
    val size: Int = 0,
    val startRow: Int = 0,
    val endRow: Int = 0,
    val pages: Int = 0,
    val prePage: Int = 0,
    val nextPage: Int = 0,
    val isFirstPage: Boolean = false,
    val isLastPage: Boolean = false,
    val hasPreviousPage: Boolean = false,
    val hasNextPage: Boolean = false,
    val navigatePages: Int = 0,
    val navigatepageNums: List<Int> = emptyList(),
    val navigateFirstPage: Int = 0,
    val navigateLastPage: Int = 0
)

/**
 * 卡交易信息类
 * @property cardNo 卡号
 * @property merchantNo 商户编号
 * @property merchantName 商户名称
 * @property txnCode 交易代码
 * @property transTime 交易时间
 * @property txnName 交易名称
 * @property txnAmount 交易金额
 * @property txnId 交易ID
 * @property balanceAmount 余额
 * @property orderId 订单ID
 * @property consumptionPattern 消费模式
 */
data class CardTransInfo(
    val cardNo: String = "",
    val merchantNo: String = "",
    val merchantName: String = "",
    val txnCode: String = "",
    val transTime: String = "",
    val txnName: String = "",
    val txnAmount: String = "",
    val txnId: String = "",
    val balanceAmount: String = "",
    val orderId: String = "",
    val consumptionPattern: String = ""
)