package dev.pigmomo.yhkit2025.api.utils


import javax.crypto.spec.SecretKeySpec
import java.security.KeyFactory
import java.security.interfaces.RSAPrivateKey
import java.security.interfaces.RSAPublicKey
import javax.crypto.Cipher
import org.json.JSONObject
import java.net.HttpURLConnection
import java.net.URL
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi
import kotlin.text.Charsets.UTF_8

object AliPayConvertUtils {
    private val privateKey = """-----BEGIN RSA PRIVATE KEY-----
            MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCL1i+fn62CHFUAEIlsuSU8DM4PhqjqiW4itBCne/MRfKIsDCdevuw1OHCV80ICUjd5ke+Rr7AvwO3gU88M6UjGvCrJK/krElYigJswehgjmRMJAay8tO78h2ABgwvoUfkPOP7IHJaxiUaZGGzifd2cFNNRgRN9H20HTvlzmIhgBOcSVEVmJugo2BjzOzU4/Lu10D/arOn9EeofXXpfEBAo799OTm7nqXzKMA9Egdl/8ZIPhXrbMl6CVg4syYV5t46T0W7frRw7L78b2O3inxeH2LchEjDHKDQTNiexJ/xFQVCh1iVgoIwaKBLC6LMTPh06gZVDURKJB9wnfyTJ+hpNAgMBAAECggEABykkryviGrOQtrwiDWs9uOF++9SNedUnyqcl4y25uL+FHnRQ380vE1qciVE3pB7JsHQErJUulINwqvgfti2MCIFCP6L803PQ7Vtglw7phYklLGTlj5REWLIl/G3VgkQQWPM2ONEd9mFtOBHEIaUIYCHA4H+Xm+SsFJ+6rmy1LxV9ZW5ddDeGRIgZLCIDDJtsaEuc18RbvFE2S98lha1LAdL1Una210Rw70IUH358VzsRf/jfsaFeZZGnF1n7PXw5eor5aEKguCioiTqTyRfPtege2KdcRrhUWP2+JuYhFvA6eFq3/87hHizI6/Yrg2AggYUUY6gA6eS1tjE1gqXTEQKBgQDoed+flyVnqsCX3jWYybpsUy5rBDzVZODcoVbvgEl8Xw5R7nR93+R1g2NYmDPnS5XiEKWtNNYewo6Pv2pmRMbuyKX6B41a0IsDMIG1vAqbdc7Xj91eLCfTVPqS0AiGIFJX5CWFOCs70rglG3h/nZe5hcR11ltOAWqSqRlvcGcrUwKBgQCZ/I4O4T0d7s7BJAkHo41LIo3pVA7IkRn4DQTpF1BxElnWMNv6Vov0YbnXz84cfGmq4bTOX8ueHSTP0a0JR0nMvbWukj9p8C7nGmXJuHXpLJjraHTT+O8OkE30VajEyP2CnPNryfR66oi2XpUpRcXTj7KaVb2UyResIgbWUHiP3wKBgQCQUA+UtzQeHW5/GA73cMrMMfrPrgrBgWThMTqRZHa5wRxXmgowlYrxtAU42wrlWxOJCUJ/uhvtbmMnMvEu2SUQ1/fItWV3aZvR+AudMET5anFjeUg3DHwQgWEnQAL6mBflvZfZEhwsf8uWJW5w8fhcz4A8kjuNue1Za6WBeypgRwKBgB4Ml9g1ggy2TmiIVK7F7suruY+/1Ia1MiEiwUOPRiZak2dl73eBrhwJeg+wQKN0b9Zl5zeioASB4W4gl6jI3ZDzsGGZroBI245Dq3ta4L+Y8Vp27t1ypYvtAxlcIewM4NO9Nw9gwLG/1N/pwyfjssAfOZY+hxliyJjRpw3pdC13AoGAETuOBJDCHiGqBAFL7L0ctGdynOKOC4aMxbCeWYLQWYr3FtMJ11rCib4XxfGN6k/dXAcF97ivZjs8xCSHHx4ruzAFJyF9dshpMMeZB6pWDa0NX/jB9zYMGEbFiGz6uBqCYeL028xh8Nn2tmoXl5hEK5/QuUM0XSRt9ELLjFYAm4s=
            -----END RSA PRIVATE KEY-----"""
    private val publicKey = """-----BEGIN PUBLIC KEY-----
            MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDENksAVqDoz5SMCZq0bsZwE+I3NjrANyTTwUVSf1+ec1PfPB4tiocEpYJFCYju9MIbawR8ivECbUWjpffZq5QllJg+19CB7V5rYGcEnb/M7CS3lFF2sNcRFJUtXUUAqyR3/l7PmpxTwObZ4DLG258dhE2vFlVGXjnuLs+FI2hg4QIDAQAB
            -----END PUBLIC KEY-----"""
    private val key = "23h4fhdilenbs741kogue1tl"

    fun encrypt3DES(data: String): ByteArray {
        val cipher = Cipher.getInstance("DESede/ECB/PKCS5Padding")
        val secretKey = SecretKeySpec(key.toByteArray(), "DESede")
        cipher.init(Cipher.ENCRYPT_MODE, secretKey)
        return cipher.doFinal(data.toByteArray())
    }

    @OptIn(ExperimentalEncodingApi::class)
    fun decrypt3DES(data: String): String {
        val cipher = Cipher.getInstance("DESede/ECB/PKCS5Padding")
        val secretKey = SecretKeySpec(key.toByteArray(), "DESede")
        cipher.init(Cipher.DECRYPT_MODE, secretKey)
        val decodedData = Base64.decode(data)
        val decryptedData = cipher.doFinal(decodedData)
        return String(decryptedData, UTF_8)
    }

    @OptIn(ExperimentalEncodingApi::class)
    fun rsaEncrypt(message: String): String {
        val cleanPublicKey = publicKey
            .replace("-----BEGIN PUBLIC KEY-----", "")
            .replace("-----END PUBLIC KEY-----", "")
            .replace("\n", "")  // 去除换行符
            .replace("\\s+".toRegex(), "")  // 去除空格

        val keyFactory = KeyFactory.getInstance("RSA")
        val pubKey: RSAPublicKey = keyFactory.generatePublic(
            java.security.spec.X509EncodedKeySpec(
                Base64.decode(cleanPublicKey)
            )
        ) as RSAPublicKey
        val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
        cipher.init(Cipher.ENCRYPT_MODE, pubKey)
        val encryptedData = cipher.doFinal(message.toByteArray())
        return Base64.encode(encryptedData)
    }

    @OptIn(ExperimentalEncodingApi::class)
    fun rsaDecrypt(text: String): String {
        val keyFactory = KeyFactory.getInstance("RSA")
        val privKey: RSAPrivateKey = keyFactory.generatePrivate(
            java.security.spec.PKCS8EncodedKeySpec(
                Base64.decode(privateKey)
            )
        ) as RSAPrivateKey
        val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
        cipher.init(Cipher.DECRYPT_MODE, privKey)
        val decodedData = Base64.decode(text)
        val decryptedData = cipher.doFinal(decodedData)
        return String(decryptedData, UTF_8)
    }

    @OptIn(ExperimentalEncodingApi::class)
    fun convertAlipayToH5(alipaySdk: String): String {
        val jsonRequest = """
            {
                "tid": "qwertyuiopasdfghjklzxcvbnm",
                "user_agent": "Msp/9.1.5 (Android 12;Linux 4.4.146;zh_CN;http;540*960;21.0;WIFI;87699552;32617;1;000000000000000;000000000000000;8efce46e85;GOOGLE;H002;false;00:00:00:00:00:00;-1.0;-1.0;sdk-and-lite;65r7u2pfruicqrn;r2agza5c56pzmev;<unknown ssid>;02:00:00:00:00:00)",
                "has_alipay": false,
                "has_msp_app": false,
                "external_info": "$alipaySdk",
                "app_key": "2021002145675770",
                "utdid": "z1x2c3v4v5v6v78v9",
                "new_client_key": "8efcf8b134",
                "action": {"type": "cashier", "method": "main"},
                "gzip": true
            }
        """
        val encryptedData = Base64.encode(encrypt3DES(jsonRequest))
        val parameter1 = rsaEncrypt(key)
        val parameter2 = String.format("%08X", parameter1.length)
        val parameter3 = String.format("%08X", encryptedData.length)
        val reqData = parameter2 + parameter1 + parameter3 + encryptedData

        val url = URL("http://mcgw.alipay.com/gateway.do")
        val connection = url.openConnection() as HttpURLConnection
        connection.requestMethod = "POST"
        connection.setRequestProperty(
            "Content-Type",
            "application/octet-stream;binary/octet-stream"
        )
        connection.setRequestProperty("User-Agent", "msp")
        connection.doOutput = true

        val data = """
            {
                "data": {
                    "device": "GOOGLE-H002",
                    "namespace": "com.alipay.mobilecashier",
                    "api_name": "com.alipay.mcpay",
                    "api_version": "4.0.2",
                    "params": {
                        "req_data": "$reqData"
                    }
                }
            }
        """
        connection.outputStream.write(data.toByteArray())

        val response = connection.inputStream.bufferedReader().readText()
        val jsonData = JSONObject(response)
        val resData = decrypt3DES(
            jsonData.getJSONObject("data").getJSONObject("params").getString("res_data")
        )
        val urldata =
            JSONObject(resData).getJSONObject("form").getJSONObject("onload").getString("name")
        val regex = "https://[^\\s']+".toRegex()
        val match = regex.find(urldata)
        return match?.value ?: ""
    }
}