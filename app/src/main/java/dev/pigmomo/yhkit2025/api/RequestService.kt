package dev.pigmomo.yhkit2025.api

import dev.pigmomo.yhkit2025.api.service.AddressService
import dev.pigmomo.yhkit2025.api.service.CardService
import dev.pigmomo.yhkit2025.api.service.CartService
import dev.pigmomo.yhkit2025.api.service.CouponService
import dev.pigmomo.yhkit2025.api.service.CreditService
import dev.pigmomo.yhkit2025.api.service.HandLuckService
import dev.pigmomo.yhkit2025.api.service.OrderService
import dev.pigmomo.yhkit2025.api.service.ShopService
import dev.pigmomo.yhkit2025.api.service.UserService
import dev.pigmomo.yhkit2025.data.model.TokenEntity
import android.util.Log
import dev.pigmomo.yhkit2025.api.encryption.EncryptionUtil
import dev.pigmomo.yhkit2025.api.service.InvitationService
import dev.pigmomo.yhkit2025.api.utils.SignUtils
import dev.pigmomo.yhkit2025.utils.HttpProxyUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.net.InetSocketAddress
import java.net.Proxy
import java.net.URLEncoder

/**
 * API服务中介类
 * 提供对各个具体服务的统一访问入口，支持多实例
 */
class RequestService(token: TokenEntity, val serviceType: String) {
    // ApiHelper实例
    private val requestHelper = RequestHelper(token, serviceType)
    
    // 当前代理配置
    private var currentProxyInfo: Pair<String?, Int?>? = null
    private var currentProxyAccount: Pair<String?, String?>? = null

    // 各服务实例
    val user = UserService(requestHelper)
    val cart = CartService(requestHelper)
    val shop = ShopService(requestHelper)
    val order = OrderService(requestHelper)
    val coupon = CouponService(requestHelper)
    val card = CardService(requestHelper)
    val address = AddressService(requestHelper)
    val invitation = InvitationService(requestHelper)
    val handLuck = HandLuckService(requestHelper)
    val credit = CreditService(requestHelper)

    /**
     * 停止所有正在进行的网络请求
     * 当需要取消所有请求时调用此方法，例如在切换账号或退出应用时
     */
    fun stop() {
        requestHelper.cancelAllRequests()
    }

    /**
     * 更新HTTP客户端，应用最新的代理设置
     */
    suspend fun updateClient() {
        requestHelper.updateClient()
    }
    
    /**
     * 配置代理
     * @param proxyConfig 代理配置信息，包含代理IP、端口和账户信息
     * @return 是否配置成功
     */
    suspend fun setupProxy(proxyConfig: HttpProxyUtils.ProxyConfig): Boolean {
        return try {
            // 保存当前代理信息
            currentProxyInfo = proxyConfig.proxyInfo
            currentProxyAccount = proxyConfig.proxyAccount
            
            // 在IO线程中执行网络操作
            withContext(Dispatchers.IO) {
                // 验证代理配置是否有效
                if (proxyConfig.proxyInfo.first == null || proxyConfig.proxyInfo.second == null) {
                    Log.e("RequestService", "Invalid proxy configuration: missing host or port")
                    return@withContext false
                }
                
                // 检查代理账号信息
                if (proxyConfig.proxyAccount != null) {
                    val (username, password) = proxyConfig.proxyAccount
                    if (username.isNullOrEmpty() || password.isNullOrEmpty()) {
                        Log.e("RequestService", "Invalid proxy authentication: missing username or password")
                        return@withContext false
                    }
                    Log.d("RequestService", "Using proxy authentication with username: $username")
                }
                
                // 配置RequestHelper的代理
                val proxy = Proxy(
                    Proxy.Type.HTTP,
                    InetSocketAddress(proxyConfig.proxyInfo.first!!, proxyConfig.proxyInfo.second!!)
                )
                
                // 更新RequestHelper中的代理配置
                requestHelper.setProxyConfig(proxy, proxyConfig.proxyAccount)
                
                // 更新HTTP客户端
                updateClient()
                
                Log.d("RequestService", "Proxy setup successful: ${proxyConfig.proxyInfo.first}:${proxyConfig.proxyInfo.second}")
                true
            }
        } catch (e: Exception) {
            Log.e("RequestService", "Error setting up proxy: ${e.message}", e)
            false
        }
    }
    
    /**
     * 清除代理配置
     */
    suspend fun clearProxy(): Boolean {
        return try {
            currentProxyInfo = null
            currentProxyAccount = null
            
            // 在IO线程中执行网络操作
            withContext(Dispatchers.IO) {
                // 清除RequestHelper中的代理配置
                requestHelper.setProxyConfig(null, null)
                
                // 更新HTTP客户端
                updateClient()
                
                Log.d("RequestService", "Proxy configuration cleared")
                true
            }
        } catch (e: Exception) {
            Log.e("RequestService", "Error clearing proxy: ${e.message}", e)
            false
        }
    }

    /**
     * 设置配置
     * @param token 用户Token实体
     */
    fun updateConfig(token: TokenEntity) {
        requestHelper.updateConfig(token)
    }

    /**
     * 设置店铺ID
     * @param shopId 店铺ID
     */
    fun setShopId(shopId: String) {
        requestHelper.setShopId(shopId)
    }

    /**
     * 设置销售商ID
     * @param sellerId 销售商ID
     */
    fun setSellerId(sellerId: String) {
        requestHelper.setSellerId(sellerId)
    }

    /**
     * 设置城市ID
     * @param cityId 城市ID
     */
    fun setCityId(cityId: String) {
        requestHelper.setCityId(cityId)
    }

    /**
     * 设置区域
     * @param district 区域
     */
    fun setDistrict(district: String) {
        requestHelper.setDistrict(district)
    }

    /**
     * 设置业务参数
     * @param xyhBizParams X-YH-Biz-Params请求头参数
     */
    fun setXyhBizParams(xyhBizParams: String) {
        requestHelper.setXyhBizParams(xyhBizParams)
    }

    /**
     * 设置Web业务参数
     * @param webXyhBizParams Web X-YH-Biz-Params请求头参数
     */
    fun setWebXyhBizParams(webXyhBizParams: String) {
        requestHelper.setWebXyhBizParams(webXyhBizParams)
    }

    /**
     * 设置orderPlaceUrl
     * @param orderPlaceUrl orderPlaceUrl
     */
    fun setOrderPlaceUrl(orderPlaceUrl: String) {
        requestHelper.setOrderPlaceUrl(orderPlaceUrl)
    }

    /**
     * 设置orderPlaceBody
     * @param orderPlaceBody orderPlaceBody
     */
    fun setOrderPlaceBody(orderPlaceBody: JSONObject) {
        requestHelper.setOrderPlaceBody(orderPlaceBody)
    }

    /**
     * 获取手机号
     * @return 手机号
     */
    fun getPhoneNumber(): String {
        return requestHelper.getPhoneNumber()
    }

    /**
     * 获取Token
     * @return Token
     */
    fun getToken(): TokenEntity {
        return requestHelper.getToken()
    }

    /**
     * 获取XYH业务参数
     *
     * @param lat 纬度
     * @param lng 经度
     * @param cityid 城市ID
     * @param district 区域
     * @param sellerid 销售商ID
     * @param shopid 店铺ID
     * @return 请求结果
     */
    suspend fun getXyhBizParams(
        lat: String = "",
        lng: String = "",
        cityid: String = "",
        district: String = "",
        sellerid: String = "",
        shopid: String = "",
        addressId: String = "",
        appid: String = "wxc9cf7c95499ee604",
        area: String = "",
        serviceType: String = "app"
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                try {
                    // URL编码区域名称
                    val encodedDistrict = URLEncoder.encode(district, "UTF-8")

                    // 构建请求参数
                    val requestParams =
                        "XYHBizParams,lat=$lat&lng=$lng&cityid=$cityid&area=$encodedDistrict&sellerid=$sellerid&shopid=$shopid&appid=iuswcxvztz25f233nc"

                    // 请求端点
                    val endpoint = "/process"

                    // 发送请求
                    val response = SignUtils.postToXyhBizParams(endpoint, requestParams)

                    // 检查响应
                    if (response.isNotEmpty()) {
                        return@withContext RequestResult.Success(response)
                    } else {
                        return@withContext RequestResult.Error(Exception("获取XYH业务参数失败：响应为空"))
                    }
                } catch (e: Exception) {
                    Log.e("RequestService", "获取XYH业务参数出错", e)
                    return@withContext RequestResult.Error(e)
                }
            }

            "mini" -> {
                try {
                    // 构建签名参数
                    val signParams =
                        "lat=$lat&lng=$lng&addressId=$addressId&appid=$appid&cityid=$cityid&shopid=$shopid&sellerid=$sellerid&area=$area"

                    // 使用 EncryptionUtil.miniProgramXYHSign 生成签名
                    val response = EncryptionUtil.miniProgramXYHSign(signParams)

                    // 检查响应
                    if (response.isNotEmpty()) {
                        return@withContext RequestResult.Success(response)
                    } else {
                        return@withContext RequestResult.Error(Exception("获取XYH业务参数失败：响应为空"))
                    }
                } catch (e: Exception) {
                    Log.e("RequestService", "获取XYH业务参数出错", e)
                    return@withContext RequestResult.Error(e)
                }
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    companion object {
        /**
         * 创建新的ApiService实例
         * @param token Token实体
         * @return 新的ApiService实例
         */
        fun create(token: TokenEntity, serviceType: String): RequestService {
            return RequestService(token, serviceType)
        }
    }
}