package dev.pigmomo.yhkit2025.api.model.card

/**
 * 接收卡片响应数据类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 接收卡片数据
 * @property now 当前时间戳
 */
data class ReceiveCardResponse(
    val code: Int = 0,
    val message: String = "",
    val data: ReceiveCardData? = null,
    val now: Long = 0
)

/**
 * 接收卡片数据类
 * @property count 接收的卡片数量
 * @property amount 接收的卡片金额
 * @property riskLevel 风险等级，0表示正常
 */
data class ReceiveCardData(
    val count: Int = 0,
    val amount: Double = 0.0,
    val riskLevel: Int = 0
)