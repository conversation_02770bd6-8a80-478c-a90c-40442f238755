package dev.pigmomo.yhkit2025.api.model.card

/**
 * 简单礼物响应数据类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 礼物数据
 * @property now 当前时间戳
 */
data class SimplePresentResponse(
    val code: Int = 0,
    val message: String = "",
    val data: SimplePresentData? = null,
    val now: Long = 0
)

/**
 * 简单礼物数据类
 * @property yhPackageId 永辉包ID
 * @property id 礼物ID
 * @property imageUrl 礼物图片URL
 * @property title 礼物标题
 * @property activityId 活动ID
 */
data class SimplePresentData(
    val yhPackageId: String = "",
    val id: String = "",
    val imageUrl: String = "",
    val title: String = "",
    val activityId: String = ""
) 