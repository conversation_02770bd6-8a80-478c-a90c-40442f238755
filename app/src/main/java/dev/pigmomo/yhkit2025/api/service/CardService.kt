package dev.pigmomo.yhkit2025.api.service

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestHelper
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.SignType
import dev.pigmomo.yhkit2025.api.encryption.EncryptionUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 卡片服务类
 * 提供卡片相关的API调用方法
 */
class CardService(requestHelper: RequestHelper) : BaseService(requestHelper) {

    private val tag = "CardService"

    private val serviceType = requestHelper.getServiceType()

    /**
     * 获取永辉卡记录的订单
     * @return 永辉卡记录的订单
     */
    suspend fun getCardRecordOrder(pageNum: String = "1"): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    if (sellerId.isEmpty() || shopId.isEmpty()) {
                        Log.e(tag, "getCardRecordOrder: sellerId or shopId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getAppCommonParams()

                    // 构建Web业务参数
                    val businessParams = buildAppWebBusinessParams()
                    businessParams["pageSize"] = "20"
                    businessParams["pageNum"] = pageNum
                    businessParams["sellerid"] = sellerId
                    businessParams["shopid"] = shopId
                    businessParams["memberid"] = requestHelper.getUid()

                    // 构建URL
                    val urlWithParams = buildAppApiUrl(
                        RequestConfig.Path.CARD_TRANS_INFO_LIST_BY_PAGE_PATH,
                        businessParams,
                        commonParams
                    )

                    // 生成签名
                    val sign =
                        requestHelper.generateSign(urlWithParams, "", SignType.APP_WEB.value)
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getWebXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "getCardRecordOrder: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildAppStandardHeaders(xyhBizParams, true, false)

                    requestHelper.get(fullUrl, headers)
                }

                "mini" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    if (sellerId.isEmpty() || shopId.isEmpty()) {
                        Log.e(tag, "getCardRecordOrder: sellerId or shopId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getMiniProgramCommonParams()

                    // 构建Web业务参数
                    val businessParams = buildMiniProgramWebBusinessParams()
                    businessParams["pageSize"] = "20"
                    businessParams["pageNum"] = pageNum
                    businessParams["sellerid"] = sellerId
                    businessParams["shopid"] = shopId
                    businessParams["memberid"] = requestHelper.getUid()

                    // 构建URL
                    val urlWithParams = buildMiniApiUrl(
                        RequestConfig.Path.CARD_TRANS_INFO_LIST_BY_PAGE_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                    // 生成签名
                    val sign =
                        requestHelper.generateSign(
                            urlWithParams,
                            "",
                            SignType.MINIPROGRAM_WEB_INFO.value
                        )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getWebXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "getCardRecordOrder: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildMiniStandardHeaders(xyhBizParams, true)

                    requestHelper.get(fullUrl, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }

    /**
     * 获取永辉卡列表
     * @param state 状态 stateList = listOf("0", "1") //, "2"
     * @param pageSize 页大小
     * @return 永辉卡列表
     */
    suspend fun getCardList(
        state: String = "0",
        pageSize: String = "10"
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "getCardList: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams["pageNum"] = "1"
                businessParams["pageSize"] = pageSize
                businessParams["state"] = state
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(RequestConfig.Path.CARD_LIST_PATH, businessParams, commonParams)

                // 生成签名
                val sign = requestHelper.generateSign(urlWithParams, "", SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getCardList: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, true, false)

                requestHelper.get(fullUrl, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "getCardList: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["pageNum"] = "1"
                businessParams["pageSize"] = pageSize
                businessParams["state"] = state
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.CARD_LIST_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                // 生成签名
                val sign =
                    requestHelper.generateSign(
                        urlWithParams,
                        "",
                        SignType.MINIPROGRAM_WEB_INFO.value
                    )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getCardList: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                requestHelper.get(fullUrl, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 简单赠送永辉卡
     * @param cardNo 卡号
     * @param cardAmount 卡金额
     * @param cardCnt 卡数量
     * @param coverId 封面ID
     * @return 简单赠送永辉卡响应结果
     */
    suspend fun getCardSimplePresent(
        cardNo: String,
        cardAmount: Double,
        cardCnt: String,
        coverId: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "getCardSimplePresent: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.CARD_SIMPLE_PRESENT_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建请求体
                val requestBody =
                    """{"cardNo":"$cardNo","cardAmount":$cardAmount,"cardCnt":$cardCnt,"coverId":"$coverId"}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(urlWithParams, requestBody, SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getCardSimplePresent: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, true, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "getCardSimplePresent: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.CARD_SIMPLE_PRESENT_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                // 构建请求体
                val requestBody =
                    """{"cardNo":"$cardNo","cardAmount":$cardAmount,"cardCnt":$cardCnt,"coverId":"$coverId"}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(
                        urlWithParams,
                        requestBody,
                        SignType.MINIPROGRAM_WEB_INFO.value
                    )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getCardSimplePresent: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 取消赠送永辉卡
     * @param cardNo 卡号
     * @return 取消赠送永辉卡响应结果
     */
    suspend fun cancelSendCard(
        cardNo: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "cancelSendCard: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.CANCEL_SEND_CARD_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建请求体
                val requestBody = """{"cardNo":"$cardNo"}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(urlWithParams, requestBody, SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "cancelSendCard: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, true, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "cancelSendCard: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.CANCEL_SEND_CARD_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                // 构建请求体
                val requestBody = """{"cardNo":"$cardNo"}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(
                        urlWithParams,
                        requestBody,
                        SignType.MINIPROGRAM_WEB_INFO.value
                    )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "cancelSendCard: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 赠送永辉卡
     * @param cardNo 卡号
     * @param cardAmount 金额
     * @param cardCnt 数量
     * @param coverId 封面ID
     * @param type 类型
     * @return 赠送永辉卡响应结果
     */
    suspend fun sendCard(
        cardNo: String,
        cardAmount: Double,
        cardCnt: String,
        coverId: String,
        type: Int
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "sendCard: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(RequestConfig.Path.SEND_CARD_PATH, businessParams, commonParams)

                // 构建请求体
                val requestBody =
                    """{"appId":"wxc9cf7c95499ee604","cardGiveList":[{"cardNo":"$cardNo","cardAmount":$cardAmount,"cardCnt":$cardCnt,"coverId":"$coverId"}],"description":"凛冬散尽，星河长明，新的一年，万事顺遂。","type":$type,"background":"https://image.yonghuivip.com/web-card-center/webCardCenterWelfare1.png"}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(urlWithParams, requestBody, SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "sendCard: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, true, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "sendCard: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.SEND_CARD_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                // 构建请求体
                val requestBody =
                    """{"appId":"wxc9cf7c95499ee604","cardGiveList":[{"cardNo":"$cardNo","cardAmount":$cardAmount,"cardCnt":$cardCnt,"coverId":"$coverId"}],"description":"凛冬散尽，星河长明，新的一年，万事顺遂。","type":$type,"background":"https://image.yonghuivip.com/web-card-center/webCardCenterWelfare1.png"}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(
                        urlWithParams,
                        requestBody,
                        SignType.MINIPROGRAM_WEB_INFO.value
                    )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "sendCard: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 验证永辉卡
     * @param cardNum 卡号/链接（密码为空）
     * @param cardPassword 卡密码
     * @return 验证永辉卡响应结果
     */
    suspend fun validateCardOrLink(
        cardNum: String,
        cardPassword: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "validateCard: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.VALIDATE_CARD_PATH,
                        businessParams,
                        commonParams
                    )

                // 加密卡号/链接（密码为空）和密码
                Log.d("YHCardBuyDeal", "cardNum: $cardNum")
                Log.d("YHCardBuyDeal", "cardPassword: $cardPassword")
                val encryptCardNum = EncryptionUtil.rsaEncrypt(cardNum)
                val encryptCardPassword = if (cardPassword.isNotEmpty()) {
                    EncryptionUtil.rsaEncrypt(cardPassword)
                } else {
                    ""
                }

                // 构建请求体
                val requestBody = if (encryptCardPassword.isEmpty()) {
                    """{"bindType":2,"cardOrLink":"$encryptCardNum"}"""
                } else {
                    """{"bindType":1,"cardCypher":"$encryptCardPassword","cardOrLink":"$encryptCardNum"}"""
                }

                // 生成签名
                val sign =
                    requestHelper.generateSign(urlWithParams, requestBody, SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "validateCard: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, true, false)
                headers["Content-Type"] = "application/json"

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "validateCard: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.VALIDATE_CARD_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                // 加密卡号/链接（密码为空）和密码
                Log.d("YHCardBuyDeal", "cardNum: $cardNum")
                Log.d("YHCardBuyDeal", "cardPassword: $cardPassword")
                val encryptCardNum = EncryptionUtil.rsaEncrypt(cardNum)
                val encryptCardPassword = if (cardPassword.isNotEmpty()) {
                    EncryptionUtil.rsaEncrypt(cardPassword)
                } else {
                    ""
                }

                // 构建请求体
                val requestBody = if (encryptCardPassword.isEmpty()) {
                    """{"bindType":2,"cardOrLink":"$encryptCardNum"}"""
                } else {
                    """{"bindType":1,"cardCypher":"$encryptCardPassword","cardOrLink":"$encryptCardNum"}"""
                }

                // 生成签名
                val sign =
                    requestHelper.generateSign(
                        urlWithParams,
                        requestBody,
                        SignType.MINIPROGRAM_WEB_INFO.value
                    )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "validateCard: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)
                headers["Content-Type"] = "application/json"

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 绑定永辉卡
     * @param cardNum 卡号/链接（密码为空）
     * @param cardPassword 卡密码
     * @return 绑定永辉卡响应结果
     */
    suspend fun bindCardOrLink(
        cardNum: String,
        cardPassword: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "bindCard: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(RequestConfig.Path.BIND_CARD_PATH, businessParams, commonParams)

                // 加密卡号/链接（密码为空）和密码
                Log.d("YHCardBuyDeal", "cardNum: $cardNum")
                Log.d("YHCardBuyDeal", "cardPassword: $cardPassword")
                val encryptCardNum = EncryptionUtil.rsaEncrypt(cardNum)
                val encryptCardPassword = if (cardPassword.isNotEmpty()) {
                    EncryptionUtil.rsaEncrypt(cardPassword)
                } else {
                    ""
                }

                // 构建请求体
                val requestBody = if (encryptCardPassword.isEmpty()) {
                    """{"bindType":2,"cardOrLink":"$encryptCardNum"}"""
                } else {
                    """{"bindType":1,"cardCypher":"$encryptCardPassword","cardOrLink":"$encryptCardNum"}"""
                }

                // 生成签名
                val sign =
                    requestHelper.generateSign(urlWithParams, requestBody, SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "bindCard: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, true, false)
                headers["Content-Type"] = "application/json"

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "bindCard: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.BIND_CARD_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                // 加密卡号/链接（密码为空）和密码
                Log.d("YHCardBuyDeal", "cardNum: $cardNum")
                Log.d("YHCardBuyDeal", "cardPassword: $cardPassword")
                val encryptCardNum = EncryptionUtil.rsaEncrypt(cardNum)
                val encryptCardPassword = if (cardPassword.isNotEmpty()) {
                    EncryptionUtil.rsaEncrypt(cardPassword)
                } else {
                    ""
                }

                // 构建请求体
                val requestBody = if (encryptCardPassword.isEmpty()) {
                    """{"bindType":2,"cardOrLink":"$encryptCardNum"}"""
                } else {
                    """{"bindType":1,"cardCypher":"$encryptCardPassword","cardOrLink":"$encryptCardNum"}"""
                }

                // 生成签名
                val sign =
                    requestHelper.generateSign(
                        urlWithParams,
                        requestBody,
                        SignType.MINIPROGRAM_WEB_INFO.value
                    )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "bindCard: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)
                headers["Content-Type"] = "application/json"

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 接收永辉卡
     * @param yhPackageId 包裹ID
     * @return 接收永辉卡响应结果
     */
    suspend fun receiveCard(
        yhPackageId: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "receiveCard: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.RECEIVE_CARD_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建请求体
                val requestBody = """{"getStatus":3,"yhPackageId":"$yhPackageId"}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(urlWithParams, requestBody, SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "receiveCard: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, true, false)
                headers["Content-Type"] = "application/json"

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "receiveCard: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.RECEIVE_CARD_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                // 构建请求体
                val requestBody = """{"getStatus":3,"yhPackageId":"$yhPackageId"}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(
                        urlWithParams,
                        requestBody,
                        SignType.MINIPROGRAM_WEB_INFO.value
                    )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "receiveCard: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)
                headers["Content-Type"] = "application/json"

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }

    }

    /**
     * 获取永辉卡总余额
     * @return 永辉卡总余额
     */
    suspend fun getCardIndexInfo(): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "getCardAllBalance: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.CARD_ALL_BALANCE_PATH,
                        businessParams,
                        commonParams
                    )

                // 生成签名
                val sign = requestHelper.generateSign(urlWithParams, "", SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getCardAllBalance: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, true, false)

                requestHelper.get(fullUrl, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()
                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "getCardIndexInfo: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["cityid"] = cityId

                // 构建URL
                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.CARD_ALL_BALANCE_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                // 生成签名
                val sign =
                    requestHelper.generateSign(
                        urlWithParams,
                        "",
                        SignType.MINIPROGRAM_WEB_INFO.value
                    )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getCardIndexInfo: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                requestHelper.get(fullUrl, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }

    }

    /**
     * 检查是否能够购买永辉卡
     * @return 检查是否能够购买永辉卡响应结果
     */
    suspend fun checkCardBuy(): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "checkCardBuy: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(RequestConfig.Path.PLACE_CARD_PATH, businessParams, commonParams)

                // 构建请求体
                val requestBody =
                    """{"cardCover":"https://image.yonghuivip.com/web-card-center/srkl1.jpg?imageMogr2/size-limit/50k!/format/jpg","cardList":[{"id":"65657871","quantity":1,"amount":66}],"cardQuantity":1,"cardTheme":"祝你生日快乐","coverId":"61","shopId":"","themeId":"13","themeType":0,"totalAmount":"66.00","tradePlatform":1,"sessionId":"${requestHelper.jysessionid}"}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(urlWithParams, requestBody, SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "checkCardBuy: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, true, false)
                headers["Content-Type"] = "application/json"

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "checkCardBuy: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                val requestBody =
                    """{"cardCover":"https://image.yonghuivip.com/web-card-center/srkl1.jpg?imageMogr2/size-limit/50k!/format/jpg","cardList":[{"id":"65657871","quantity":1,"amount":66}],"cardQuantity":1,"cardTheme":"祝你生日快乐","coverId":"61","shopId":"","themeId":"13","themeType":0,"totalAmount":"66.00","tradePlatform":2,"sessionId":"${requestHelper.jysessionid}"}"""

                // 构建URL
                val urlWithParams =
                    buildMiniApiUrl(
                        RequestConfig.Path.PLACE_CARD_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )
                // 生成签名
                val sign =
                    requestHelper.generateSign(
                        urlWithParams,
                        requestBody,
                        SignType.MINIPROGRAM_WEB_INFO.value
                    )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "checkCardBuy: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }

    }
}