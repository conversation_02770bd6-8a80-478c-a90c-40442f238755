package dev.pigmomo.yhkit2025.api.model.handluck

/**
 * 手气抽奖机数据响应类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 手气抽奖机数据
 * @property now 当前时间戳
 */
data class HandLuckSlotMachineDataResponse(
    val code: Int = 0,
    val message: String = "",
    val data: HandLuckSlotMachineData? = null,
    val now: Long = 0
)

/**
 * 手气抽奖机数据类
 * @property eventStatusCode 事件状态码
 * @property otherActivityPopDTO 其他活动弹窗数据
 * @property activityCode 活动代码
 * @property swellActivityCode 膨胀活动代码
 * @property startTime 开始时间
 * @property endTime 结束时间
 * @property activityRule 活动规则
 * @property wechatShareMsg 微信分享消息
 * @property wechatShareImg 微信分享图片
 * @property receiveStatus 接收状态
 * @property customerType 客户类型
 * @property redEnvelope 红包信息
 * @property buttonStatus 按钮状态
 * @property buttonJumpUrl 按钮跳转链接
 * @property swellCouponInfo 膨胀优惠券信息
 */
data class HandLuckSlotMachineData(
    val eventStatusCode: String? = null,
    val otherActivityPopDTO: Any? = null,
    val activityCode: String = "",
    val swellActivityCode: String? = null,
    val startTime: Long = 0,
    val endTime: Long = 0,
    val activityRule: String = "",
    val wechatShareMsg: String = "",
    val wechatShareImg: String = "",
    val receiveStatus: Int = 0,
    val customerType: Int = 0,
    val redEnvelope: RedEnvelope? = null,
    val buttonStatus: Int = 0,
    val buttonJumpUrl: String? = null,
    val swellCouponInfo: SwellCouponInfo? = null
)

/**
 * 红包信息类
 * @property eventStatusCode 事件状态码
 * @property otherActivityPopDTO 其他活动弹窗数据
 * @property couponInfo 优惠券信息
 * @property rewardType 奖励类型
 * @property creditValue 积分值
 * @property luckiest 是否手气最佳
 * @property customerStatus 客户状态
 * @property newCustomerUrl 新客户链接
 * @property rewardButtonStatus 奖励按钮状态
 * @property swellCouponInfo 膨胀优惠券信息
 * @property swelled 是否已膨胀
 */
data class RedEnvelope(
    val eventStatusCode: String? = null,
    val otherActivityPopDTO: Any? = null,
    val couponInfo: HandLuckSlotMachineDataCouponInfo? = null,
    val rewardType: Int = 0,
    val creditValue: String? = null,
    val luckiest: Int? = null,
    val customerStatus: Int? = null,
    val newCustomerUrl: String? = null,
    val rewardButtonStatus: Int? = null,
    val swellCouponInfo: SwellCouponInfo? = null,
    val swelled: Boolean = false
)

/**
 * 优惠券信息类
 * @property promotionCode 促销码
 * @property couponCode 优惠券码
 * @property name 优惠券名称
 * @property amount 优惠券金额
 * @property enabled 是否启用
 * @property couponDescription 优惠券描述
 * @property couponDescription2 优惠券描述2
 * @property catalogCode 目录代码
 * @property applicationScope 适用范围
 * @property applicationPlatform 应用平台
 * @property couponStatus 优惠券状态
 * @property acquiredStatus 获取状态
 * @property deliveryLimit 发放限制
 * @property actionUrl 操作URL
 * @property startTime 开始时间
 * @property endTime 结束时间
 * @property remark 备注
 * @property skus 商品列表
 * @property deliveryTime 发放时间
 * @property detailList 详情列表
 * @property couponLatitude 优惠券纬度
 * @property superposeType 叠加类型
 */
data class HandLuckSlotMachineDataCouponInfo(
    val promotionCode: String = "",
    val couponCode: String = "",
    val name: String = "",
    val amount: String = "",
    val enabled: Boolean = true,
    val couponDescription: String = "",
    val couponDescription2: String? = null,
    val catalogCode: String = "",
    val applicationScope: String = "",
    val applicationPlatform: String? = null,
    val couponStatus: String = "",
    val acquiredStatus: String = "",
    val deliveryLimit: Int = 0,
    val actionUrl: String? = null,
    val startTime: Long = 0,
    val endTime: Long = 0,
    val remark: String? = null,
    val skus: List<String>? = null,
    val deliveryTime: Long = 0,
    val detailList: List<String>? = null,
    val couponLatitude: String = "",
    val superposeType: Boolean = false
)

/**
 * 膨胀优惠券信息类
 * @property swellActivityCode 膨胀活动代码
 * @property canSwell 是否可以膨胀
 * @property highAmount 最高金额
 * @property allHelperNum 所有助手数量
 */
data class SwellCouponInfo(
    val swellActivityCode: String? = null,
    val canSwell: Boolean = false,
    val highAmount: String? = null,
    val allHelperNum: Int? = null
) 