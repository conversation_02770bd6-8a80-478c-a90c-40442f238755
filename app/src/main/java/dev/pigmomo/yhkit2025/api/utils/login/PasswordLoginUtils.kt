package dev.pigmomo.yhkit2025.api.utils.login

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.encryption.EncryptionUtil
import dev.pigmomo.yhkit2025.api.utils.SignUtils
import dev.pigmomo.yhkit2025.data.model.LoginTokenEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONException
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID
import kotlin.random.Random

/**
 * 处理密码登录相关的工具类
 */
object PasswordLoginUtils : BaseLoginUtils() {

    private object LoginConstants {
        const val LOGIN_PATH = "/web/passport/member/login/700"
    }
    
    /**
     * 缓存手机号对应的设备信息，用于登录重试时复用
     */
    private val deviceInfoCache = mutableMapOf<String, DeviceInfo>()

    /**
     * 使用手机号和密码登录获取loginToken
     *
     * @param phone 用户手机号
     * @param password 加密后的密码
     * @param useProxy 是否使用代理
     * @param proxyType 代理类型，默认为"pinzan"
     * @return RequestResult 包含LoginTokenEntity或错误信息
     */
    suspend fun loginWithPassword(
        phone: String,
        password: String,
        loginIndex: Int,
        useProxy: Boolean = false,
        proxyType: String = "pinzan"
    ): RequestResult<LoginTokenEntity> = withContext(Dispatchers.IO) {
        try {
            // 1. 获取安全票据
            val securityTicketResult = fetchSecurityTicketForm2Captcha()
            val securityTicket = when (securityTicketResult) {
                is RequestResult.Success -> securityTicketResult.data
                is RequestResult.Error -> return@withContext RequestResult.Error(
                    securityTicketResult.error
                )
            }

            // 2. 获取设备信息 - 从缓存获取或新生成
            val deviceInfo = deviceInfoCache[phone] ?: DeviceInfoProvider.generate().also {
                // 将新生成的设备信息存入缓存
                deviceInfoCache[phone] = it
                Log.d("PasswordLoginUtils", "Generated new device info for phone: $phone")
            }
            val jysessionid = UUID.randomUUID().toString()
            val distinctId = UUID.randomUUID().toString()

            // 3. 创建HTTP客户端
            val client = createHttpClient(useProxy, proxyType)

            // 4. 构建登录请求
            val request = buildLoginRequest(
                phone, password, securityTicket, deviceInfo, jysessionid, distinctId
            )

            // 5. 执行请求并处理响应
            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            if (response.isSuccessful && responseBody != null) {
                // 从响应头中提取userKey
                val userKey = response.headers("Set-Cookie")
                    .firstOrNull { it.contains("userKey=") }
                    ?.split(";")?.get(0)?.trim()
                    ?.substringAfter("=") ?: ""

                Log.d("PasswordLoginUtils", "Extracted userKey: $userKey")

                // 解析响应并返回结果
                val (message, loginTokenEntity) = parseLoginResponse(
                    responseBody,
                    phone,
                    deviceInfo,
                    distinctId,
                    userKey,
                    loginIndex
                )
                if (loginTokenEntity != null) {
                    // 登录成功，从缓存中移除设备信息
                    deviceInfoCache.remove(phone)
                    Log.d("PasswordLoginUtils", "Login successful, removed device info for phone: $phone")
                    RequestResult.Success(loginTokenEntity)
                } else {
                    RequestResult.Error(Exception(message))
                }
            } else {
                RequestResult.Error(Exception("Network request failed: ${response.code} - ${response.message}"))
            }
        } catch (e: Exception) {
            Log.e("PasswordLoginUtils", "Login failed with exception", e)
            RequestResult.Error(e)
        }
    }

    /**
     * 从远程服务器获取安全票据
     *
     * @return RequestResult 包含SecurityTicket或错误信息
     */
    private suspend fun fetchSecurityTicketFormMyService(): RequestResult<SecurityTicket> =
        withContext(Dispatchers.IO) {
            try {
                val client = OkHttpClient()
                val request = Request.Builder().url(Constants.TICKET_SERVICE_URL).build()
                val response = client.newCall(request).execute()
                val responseBody = response.body?.string()

                if (!response.isSuccessful || responseBody.isNullOrEmpty()) {
                    return@withContext RequestResult.Error(Exception("Failed to fetch ticket, response code: ${response.code}"))
                }

                val rootJson = JSONObject(responseBody)
                val messageStr = rootJson.optString("message").replace("\n", "")
                if (messageStr.isEmpty()) {
                    return@withContext RequestResult.Error(Exception("Ticket message is empty"))
                }

                val messageJson = JSONObject(messageStr)
                val ticket = messageJson.optString("ticket")
                val randstr = messageJson.optString("randstr")

                if (ticket.isEmpty() || randstr.isEmpty()) {
                    Log.d("fetchSecurityTicket", "Failed to get ticket fields from: $messageStr")
                    return@withContext RequestResult.Error(Exception("Invalid ticket content"))
                } else {
                    Log.d("fetchSecurityTicket", "Successfully got ticket")
                    return@withContext RequestResult.Success(SecurityTicket(ticket, randstr))
                }
            } catch (e: JSONException) {
                Log.e("fetchSecurityTicket", "Error parsing ticket JSON", e)
                return@withContext RequestResult.Error(
                    Exception(
                        "Failed to parse security ticket response",
                        e
                    )
                )
            } catch (e: Exception) {
                Log.e("fetchSecurityTicket", "Error getting ticket", e)
                return@withContext RequestResult.Error(e)
            }
        }

    /**
     * 构建登录请求
     */
    private suspend fun buildLoginRequest(
        phone: String,
        password: String,
        ticket: SecurityTicket,
        deviceInfo: DeviceInfo,
        jysessionid: String,
        distinctId: String
    ): Request {
        val timestamp = System.currentTimeMillis()
        val cipher = EncryptionUtil.rsaEncryptPassword(password)

        // 构建请求体
        val requestBodyJson = JSONObject().apply {
            put("cipher", cipher)
            put("hasCheck", "1")
            put("jysessionid", jysessionid)
            put("marketchannelreq", JSONObject().apply {
                put("mainchannelid", "AppChannel")
                put("referenceid", deviceInfo.channel)
                put("secondarychannelid", "Default")
            })
            put("mobile", phone)
            put("phonenum", phone)
            put("randstr", ticket.randstr)
            put("riskCheckType", "1")
            put("riskCpuBuild", "armeabi-v7a")
            put("riskLogType", "5")
            put("riskLoginType", "5")
            put("riskMobile", "")
            put("riskOperator", "联通")
            put("riskPhonePower", "${Random.Default.nextInt(70, 100)}")
            put("riskRegisterType", "5")
            put("riskScene", "2")
            put("securityticket", ticket.ticket)
            put("securityversion", "tc_v3")
            put("tracesignid", distinctId)
        }

        // 构建URL参数
        val urlParams = buildCommonUrlParams(jysessionid, deviceInfo, distinctId, timestamp)

        val urlWithParams = buildUrlWithParams(Constants.BASE_URL, LoginConstants.LOGIN_PATH, urlParams)

        // 计算签名
        val sortedParams =
            urlParams.entries.sortedBy { it.key }.joinToString("") { it.key + it.value }
        val needSignStr = sortedParams + requestBodyJson.toString()
        val sign = SignUtils.postToSign("/process", needSignStr)
        val fullUrl = "$urlWithParams&sign=$sign"

        // 构建请求
        val mediaType = Constants.CONTENT_TYPE.toMediaType()
        val requestBody = requestBodyJson.toString().toRequestBody(mediaType)
        
        // 获取通用请求头
        val headers = buildCommonHeaders(deviceInfo)
        
        // 构建请求
        val requestBuilder = Request.Builder()
            .url(fullUrl)
            .post(requestBody)
        
        // 添加所有请求头
        headers.forEach { (key, value) ->
            requestBuilder.addHeader(key, value)
        }

        return requestBuilder.build()
    }


    /**
     * 解析登录响应并创建LoginTokenEntity
     */
    private fun parseLoginResponse(
        jsonResponse: String,
        phone: String,
        deviceInfo: DeviceInfo,
        distinctId: String,
        userKey: String,
        loginIndex: Int
    ): Pair<String, LoginTokenEntity?> {
        try {
            val json = JSONObject(jsonResponse)
            if (json.optInt("code") != 0) {
                val errorMessage = json.optString("message", "Unknown error")
                Log.e("parseLoginResponse", "Response error: $errorMessage")
                return Pair(errorMessage, null)
            }

            val data =
                json.optJSONObject("data") ?: return Pair("Missing required data in response", null)
            val uid = data.optString("uid")
            val refreshToken = data.optString("refresh_token")
            val accessToken = data.optString("access_token")

            if (uid.isEmpty() || refreshToken.isEmpty() || accessToken.isEmpty()) {
                return Pair("Missing required fields in response", null)
            }

            val appParam = with(deviceInfo) {
                "$channel,$screen,$deviceId,$distinctId,$osVersion,$model,${Constants.NETWORK_TYPE},${brand},${Constants.APP_VERSION}"
            }

            val now = json.optLong("now", System.currentTimeMillis())
            val updateDate = SimpleDateFormat("MM.dd.yy", Locale.getDefault()).format(Date(now))

            val token = LoginTokenEntity(
                uid = uid,
                phoneNumber = phone,
                userKey = userKey.ifEmpty { uid },
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = data.optString("expires_in", "7200").toLong(),
                updateDate = updateDate,
                isNew = false,
                bargainFirst = false,
                activityLimited = false,
                yhCardLimited = false,
                appParam = appParam,
                extraNote = "",
                loginIndex = loginIndex
            )
            return Pair("Success", token)
        } catch (e: Exception) {
            Log.e("parseLoginResponse", "Error parsing login response", e)
            return Pair("Error parsing login response: ${e.message}", null)
        }
    }
    
    /**
     * 清除所有缓存的设备信息
     */
    fun clearAllDeviceInfoCache() {
        deviceInfoCache.clear()
        Log.d("PasswordLoginUtils", "Cleared all device info cache")
    }
    
    /**
     * 获取当前缓存的设备信息数量
     * @return 缓存中的设备信息数量
     */
    fun getCachedDeviceInfoCount(): Int {
        return deviceInfoCache.size
    }
}