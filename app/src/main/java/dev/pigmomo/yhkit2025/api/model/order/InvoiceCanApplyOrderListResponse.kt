package dev.pigmomo.yhkit2025.api.model.order

/**
 * 发票可申请订单列表响应
 * 用于解析获取可申请发票的订单列表接口返回数据
 */
data class InvoiceCanApplyOrderListResponse(
    val code: Int = 0,
    val message: String = "",
    val data: InvoiceCanApplyOrderListData? = null
)

/**
 * 发票可申请订单列表数据
 * 包含分页信息和订单列表
 */
data class InvoiceCanApplyOrderListData(
    val page: Int = 0,
    val pagecount: Int = 0,
    val count: Int = 0,
    val orders: List<InvoiceCanApplyOrder> = emptyList()
)

/**
 * 可申请发票的订单信息
 * 包含订单基本信息和商品列表
 */
data class InvoiceCanApplyOrder(
    val orderid: String = "",
    val ordersubtype: String = "",
    val ordertypetag: String = "",
    val amt: Double = 0.0,
    val createdat: Long = 0,
    val shopid: String = "",
    val shopfullname: String = "",
    val items: List<InvoiceCanApplyOrderItem> = emptyList(),
    val channelId: Int = 0
)

/**
 * 可申请发票的订单商品信息
 * 包含商品基本信息和数量
 */
data class InvoiceCanApplyOrderItem(
    val goodsid: String = "",
    val goodsname: String = "",
    val qty: Double = 0.0,
    val mainimg: String = ""
) 