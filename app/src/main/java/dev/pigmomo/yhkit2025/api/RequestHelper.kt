package dev.pigmomo.yhkit2025.api

import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import dev.pigmomo.yhkit2025.api.RequestConfig.AppVersion
import dev.pigmomo.yhkit2025.api.encryption.EncryptionUtil
import dev.pigmomo.yhkit2025.api.utils.SignUtils
import dev.pigmomo.yhkit2025.data.model.TokenEntity
import dev.pigmomo.yhkit2025.utils.HttpProxyUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Credentials
import okhttp3.FormBody
import okhttp3.Headers
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import org.json.JSONObject
import java.io.IOException
import java.net.Proxy
import java.util.UUID
import java.util.concurrent.TimeUnit
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine
import kotlin.math.pow

/**
 * API响应结果封装类
 * @param T 响应数据类型
 * @property isSuccess 是否成功
 * @property data 响应数据
 * @property error 错误信息
 */
sealed class RequestResult<out T> {
    data class Success<T>(override val data: T) : RequestResult<T>()
    data class Error(val error: Exception) : RequestResult<Nothing>()

    val isSuccess: Boolean get() = this is Success

    open val data: T?
        get() = when (this) {
            is Success -> data
            is Error -> null
        }

    fun getOrNull(): T? = when (this) {
        is Success -> data
        is Error -> null
    }

    fun getOrThrow(): T = when (this) {
        is Success -> data
        is Error -> throw error
    }

    inline fun onSuccess(action: (T) -> Unit): RequestResult<T> {
        if (this is Success) action(data)
        return this
    }

    inline fun onError(action: (Exception) -> Unit): RequestResult<T> {
        if (this is Error) action(error)
        return this
    }
}

/**
 * 签名类型枚举
 * 定义所有支持的签名算法类型
 */
enum class SignType(val value: String) {
    APP("app"),
    APP_WEB("appWebSign"),
    MINIPROGRAM("miniProgramSign"),
    MINIPROGRAM_WEB_INFO("miniProgramWebSignForInfo"),
    MINIPROGRAM_WEB_ACTIVITY("miniProgramWebSignForActivity");

    companion object {
        fun fromString(value: String): SignType = entries.find { it.value == value } ?: APP
    }
}

/**
 * API请求助手类
 * 提供协程HTTP请求方法，支持GET、POST等常用HTTP方法
 */
class RequestHelper(private var token: TokenEntity,private val serviceType: String) {
    companion object {
        private const val TAG = "RequestHelper"

        // 默认内容类型
        private val JSON_MEDIA_TYPE = "application/json; charset=utf-8".toMediaType()
    }

    // 解析TokenEntity
    private var uid = token.uid
    private var phoneNumber = token.phoneNumber
    private var userKey = token.userKey
    private var accessToken = token.accessToken

    // 解析appParam
    private val appParamList = token.appParam.split(",")
    private var channel = appParamList.getOrNull(0) ?: ""
    private var screen = appParamList.getOrNull(1) ?: ""
    private var deviceId = appParamList.getOrNull(2) ?: ""
    private var distinctId = appParamList.getOrNull(3) ?: ""
    private var osVersion = appParamList.getOrNull(4) ?: ""
    private var model = appParamList.getOrNull(5) ?: ""
    private var networkType = appParamList.getOrNull(6) ?: ""
    private var brand = appParamList.getOrNull(7) ?: ""
    private var version = appParamList.getOrNull(8) ?: ""

    // 业务参数
    private var xyhBizParams: String = ""
    private var webXyhBizParams: String = ""

    // 店铺相关参数
    private var sellerId: String = ""
    private var shopId: String = ""
    private var cityId: String = ""
    private var district: String = ""

    // orderPlaceUrl
    private var orderPlaceUrl: String = ""

    // orderPlaceBody
    private var orderPlaceBody: JSONObject = JSONObject()

    // 代理配置
    private var proxy: Proxy? = null
    private var proxyAccount: Pair<String?, String?>? = null

    // HTTP客户端与序列化工具
    private var client: OkHttpClient = createDefaultClient()
    private val gson = Gson()

    /**
     * 会话ID - 每个实例生成唯一会话ID
     */
    val jysessionid = UUID.randomUUID().toString()

    //region HTTP请求相关方法

    /**
     * 创建默认的OkHttpClient实例
     */
    private fun createDefaultClient(): OkHttpClient {
        val builder = OkHttpClient.Builder()
            .connectTimeout(15, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .addInterceptor(createRetryInterceptor())
            
        // 配置代理
        proxy?.let { builder.proxy(it) }
        
        // 配置代理认证
        if (proxy != null && proxyAccount != null && proxyAccount?.first != null) {
            builder.proxyAuthenticator { _, response ->
                val credential = Credentials.basic(
                    proxyAccount?.first ?: "", 
                    proxyAccount?.second ?: ""
                )
                response.request.newBuilder()
                    .header("Proxy-Authorization", credential)
                    .build()
            }
        }
        
        return builder.build()
    }

    private fun createRetryInterceptor(): Interceptor {
        return Interceptor { chain ->
            val request = chain.request()
            var response: Response? = null
            var exception: IOException? = null
            var tryCount = 0
            
            while (tryCount < 3 && (response == null || !response.isSuccessful)) {
                try {
                    // Close previous response before retrying
                    response?.close()
                    
                    if (tryCount > 0) {
                        Log.d(TAG, "Retrying request (attempt ${tryCount+1}): ${request.url}")
                        // 指数退避策略
                        Thread.sleep((1000L * (2.0.pow(tryCount.toDouble()))).toLong())
                    }
                    response = chain.proceed(request)
                    exception = null
                } catch (e: IOException) {
                    exception = e
                    Log.e(TAG, "Request failed (attempt ${tryCount+1}): ${e.message}")
                } finally {
                    tryCount++
                }
            }
            
            exception?.let { throw it }
            response!!
        }
    }

    /**
     * 设置代理配置
     * @param proxy 代理对象，如果为null则清除代理
     * @param proxyAccount 代理账号和密码，如果为null则不使用认证
     */
    fun setProxyConfig(proxy: Proxy?, proxyAccount: Pair<String?, String?>?) {
        this.proxy = proxy
        this.proxyAccount = proxyAccount
        Log.d(TAG, "Proxy configuration updated: ${proxy != null}")
    }

    /**
     * 更新OkHttpClient配置
     * 当代理设置改变时调用此方法
     */
    suspend fun updateClient() = withContext(Dispatchers.IO) {
        client.dispatcher.executorService.shutdown()
        client.connectionPool.evictAll()
        client = createDefaultClient()
    }

    /**
     * 取消所有正在进行的网络请求
     * 当需要停止所有请求时调用此方法
     */
    fun cancelAllRequests() {
        // 取消所有正在进行的请求
        client.dispatcher.cancelAll()
        Log.d(TAG, "所有网络请求已取消")
    }

    /**
     * 构建请求对象
     */
    private fun buildRequest(
        url: String,
        method: String,
        headers: Map<String, String> = emptyMap(),
        body: RequestBody? = null
    ): Request {
        val headerBuilder = Headers.Builder().apply {
            headers.forEach { (key, value) -> add(key, value) }
        }

        return Request.Builder()
            .url(url)
            .headers(headerBuilder.build())
            .apply {
                when (method.uppercase()) {
                    "GET" -> get()
                    "POST" -> post(body ?: FormBody.Builder().build())
                    else -> throw IllegalArgumentException("不支持的HTTP方法: $method")
                }
            }
            .build()
    }

    /**
     * 挂起函数版本的GET请求（用于协程）
     */
    suspend fun get(url: String, headers: Map<String, String> = emptyMap()): RequestResult<String> =
        withContext(Dispatchers.IO) {
            try {
                val request = buildRequest(url, "GET", headers)
                val response = executeRequest(request)
                RequestResult.Success(response)
            } catch (e: Exception) {
                RequestResult.Error(e)
            }
        }

    /**
     * 专门用于助力券的GET请求，使用更短的超时时间和重试机制
     */
    suspend fun getForBoostCoupon(
        url: String,
        headers: Map<String, String> = emptyMap(),
        maxRetries: Int = 2
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        var lastException: Exception? = null

        for (attempt in 0..maxRetries) {
            try {
                // 创建专用的客户端配置
                val boostCouponClient = client.newBuilder()
                    .connectTimeout(RequestConfig.Timeout.getBoostCouponConnectTimeoutMillis(), TimeUnit.MILLISECONDS)
                    .readTimeout(RequestConfig.Timeout.getBoostCouponReadTimeoutMillis(), TimeUnit.MILLISECONDS)
                    .writeTimeout(RequestConfig.Timeout.getBoostCouponWriteTimeoutMillis(), TimeUnit.MILLISECONDS)
                    .build()

                val request = buildRequest(url, "GET", headers)

                if (attempt > 0) {
                    Log.d(TAG, "Boost coupon request retry attempt $attempt for: $url")
                    // 指数退避策略
                    delay((500L * (2.0.pow(attempt.toDouble()))).toLong())
                }

                val response = boostCouponClient.newCall(request).execute()
                if (!response.isSuccessful) {
                    throw IOException("HTTP ${response.code}: ${response.message}")
                }

                val responseBody = response.body?.string() ?: ""
                response.close()

                Log.d(TAG, "Boost coupon request successful on attempt ${attempt + 1}")
                return@withContext RequestResult.Success(responseBody)

            } catch (e: Exception) {
                lastException = e
                Log.w(TAG, "Boost coupon request failed on attempt ${attempt + 1}: ${e.message}")

                // 如果是最后一次尝试，不再重试，直接继续到循环外
                if (attempt == maxRetries) {
                    Log.e(TAG, "All retry attempts failed for boost coupon request: $url")
                }
            }
        }

        RequestResult.Error(lastException ?: Exception("Unknown error in boost coupon request"))
    }

    /**
     * 挂起函数版本的POST请求（JSON格式数据，用于协程）
     */
    suspend fun postJson(
        url: String,
        jsonBody: String,
        headers: Map<String, String> = emptyMap()
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        try {
            val body = jsonBody.toRequestBody(JSON_MEDIA_TYPE)
            val request = buildRequest(url, "POST", headers, body)
            val response = executeRequest(request)
            RequestResult.Success(response)
        } catch (e: Exception) {
            RequestResult.Error(e)
        }
    }

    /**
     * 挂起函数版本的POST请求（表单数据，用于协程）
     */
    suspend fun postForm(
        url: String,
        formData: Map<String, String>,
        headers: Map<String, String> = emptyMap()
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        try {
            val formBuilder = FormBody.Builder().apply {
                formData.forEach { (key, value) -> add(key, value) }
            }
            val request = buildRequest(url, "POST", headers, formBuilder.build())
            val response = executeRequest(request)
            RequestResult.Success(response)
        } catch (e: Exception) {
            RequestResult.Error(e)
        }
    }

    /**
     * 执行协程请求
     */
    private suspend fun executeRequest(request: Request): String =
        suspendCoroutine { continuation ->
            if (RequestConfig.DEBUG) {
                Log.d(TAG, "Request URL: ${request.url}")
                Log.d(TAG, "Request Headers: ${request.headers}")
                request.body?.let { Log.d(TAG, "Request Body: $it") }
            }

            client.newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.e(TAG, "Request failed: ${e.message}")
                    continuation.resumeWithException(e)
                }

                override fun onResponse(call: Call, response: Response) {
                    response.use { res ->
                        try {
                            val responseBody = res.body?.string() ?: ""

                            if (!res.isSuccessful) {
                                val error = IOException("请求失败，状态码: ${res.code}")
                                Log.e(
                                    TAG,
                                    "请求失败，状态码: ${res.code}, 消息: ${res.message}"
                                )
                                continuation.resumeWithException(error)
                                return
                            }

                            if (RequestConfig.DEBUG) {
                                Log.d(TAG, "Response Body: $responseBody")
                            }

                            continuation.resume(responseBody)
                        } catch (e: Exception) {
                            Log.e(TAG, "处理响应时出错: ${e.message}")
                            continuation.resumeWithException(e)
                        }
                    }
                }
            })
        }
    //endregion

    //region JSON序列化相关方法
    /**
     * 将JSON字符串解析为指定类型的对象
     */
    internal inline fun <reified T> fromJson(json: String): RequestResult<T> = try {
        val result = gson.fromJson<T>(json, object : TypeToken<T>() {}.type)
        RequestResult.Success(result)
    } catch (e: JsonSyntaxException) {
        Log.e(TAG, "JSON解析错误: ${e.message}")
        RequestResult.Error(e)
    } catch (e: Exception) {
        Log.e(TAG, "解析JSON时出错: ${e.message}")
        RequestResult.Error(e)
    }

    /**
     * 将对象转换为JSON字符串
     */
    fun toJson(obj: Any): String = gson.toJson(obj)
    //endregion

    //region 配置相关方法
    /**
     * 设置新的OkHttpClient，可用于自定义配置
     */
    fun setClient(newClient: OkHttpClient) {
        client.dispatcher.executorService.shutdown()
        client.connectionPool.evictAll()
        client = newClient
    }

    /**
     * 更新token
     */
    fun updateConfig(newToken: TokenEntity) {
        this.token = newToken
        // 更新解析TokenEntity
        this.uid = newToken.uid
        this.phoneNumber = newToken.phoneNumber
        this.userKey = newToken.userKey
        this.accessToken = newToken.accessToken
        // 重新解析appParam
        val newAppParamList = newToken.appParam.split(",")
        newAppParamList.getOrNull(0)?.let { this.channel = it }
        newAppParamList.getOrNull(1)?.let { this.screen = it }
        newAppParamList.getOrNull(2)?.let { this.deviceId = it }
        newAppParamList.getOrNull(3)?.let { this.distinctId = it }
        newAppParamList.getOrNull(4)?.let { this.osVersion = it }
        newAppParamList.getOrNull(5)?.let { this.model = it }
        newAppParamList.getOrNull(6)?.let { this.networkType = it }
        newAppParamList.getOrNull(7)?.let { this.brand = it }
        newAppParamList.getOrNull(8)?.let { this.version = it }
    }

    /**
     * 设置业务参数
     */
    fun setXyhBizParams(params: String) {
        this.xyhBizParams = params
    }

    fun setWebXyhBizParams(params: String) {
        this.webXyhBizParams = params
    }

    /**
     * 设置店铺相关参数
     */
    fun setSellerId(sellerId: String) {
        this.sellerId = sellerId
    }

    fun setShopId(shopId: String) {
        this.shopId = shopId
    }

    fun setCityId(cityId: String) {
        this.cityId = cityId
    }

    fun setDistrict(district: String) {
        this.district = district
    }
    //endregion

    //region orderPlaceBody相关方法
    fun setOrderPlaceUrl(orderPlaceUrl: String) {
        this.orderPlaceUrl = orderPlaceUrl
    }

    fun getOrderPlaceUrl(): String = orderPlaceUrl

    fun setOrderPlaceBody(orderPlaceBody: JSONObject) {
        this.orderPlaceBody = orderPlaceBody
    }

    fun getOrderPlaceBody(): JSONObject = orderPlaceBody
    //endregion

    //region 获取配置方法

    /**
     * 获取当前服务类型
     */
    fun getServiceType(): String = serviceType

    /**
     * 获取当前token
     */
    fun getToken(): TokenEntity = token

    /**
     * 获取当前uid
     */
    fun getUid(): String = uid

    /**
     * 获取当前phoneNumber
     */
    fun getPhoneNumber(): String = phoneNumber

    /**
     * 获取当前userKey
     */
    fun getUserKey(): String = userKey

    /**
     * 获取当前accessToken
     */
    fun getAccessToken(): String = accessToken

    /**
     * 获取当前channel
     */
    fun getChannel(): String = channel

    /**
     * 获取当前screen
     */
    fun getScreen(): String = screen

    /**
     * 获取当前deviceId
     */
    fun getDeviceId(): String = deviceId

    /**
     * 获取当前distinctId
     */
    fun getDistinctId(): String = distinctId

    /**
     * 获取当前osVersion
     */
    fun getOsVersion(): String = osVersion

    /**
     * 获取当前model
     */
    fun getModel(): String = model

    /**
     * 获取当前brand
     */
    fun getBrand(): String = brand

    /**
     * 获取当前version
     */
    fun getVersion(): String = version

    /**
     * 获取当前networkType
     */
    fun getNetworkType(): String = networkType

    /**
     * 获取业务参数
     */
    fun getXyhBizParams(): String = xyhBizParams
    fun getWebXyhBizParams(): String = webXyhBizParams

    /**
     * 获取店铺相关参数
     */
    fun getSellerId(): String = sellerId
    fun getShopId(): String = shopId
    fun getCityId(): String = cityId
    fun getDistrict(): String = district
    //endregion

    //region 参数构建方法
    /**
     * 获取APP通用参数
     */
    fun getAppCommonParams(): MutableMap<String, Any> {
        val params = mutableMapOf<String, Any>().apply {
            // 添加时间戳
            put("timestamp", System.currentTimeMillis())
        }

        // 添加设备信息（如果有）
        deviceId.let { if (it.isNotEmpty()) params["deviceid"] = it }
        osVersion.let { if (it.isNotEmpty()) params["osVersion"] = it }
        model.let { if (it.isNotEmpty()) params["model"] = it }
        brand.let { if (it.isNotEmpty()) params["brand"] = it }
        channel.let { if (it.isNotEmpty()) params["channel"] = it }
        screen.let { if (it.isNotEmpty()) params["screen"] = it }
        distinctId.let { if (it.isNotEmpty()) params["distinctId"] = it }
        networkType.let { if (it.isNotEmpty()) params["networkType"] = it }

        //version.let { if (it.isNotEmpty()) params["v"] = it }
        params["v"] = AppVersion.VERSION

        return params
    }

    /**
     * 获取小程序通用参数
     */
    fun getMiniProgramCommonParams(): MutableMap<String, Any> {
        val params = mutableMapOf<String, Any>().apply {
            // 添加时间戳
            put("timestamp", System.currentTimeMillis())
        }

        // 添加设备信息（如果有）
        deviceId.let { if (it.isNotEmpty()) params["deviceid"] = it }
        osVersion.let { if (it.isNotEmpty()) params["osVersion"] = it }
        model.let { if (it.isNotEmpty()) params["model"] = it }
        brand.let { if (it.isNotEmpty()) params["brand"] = it }
        //channel.let { if (it.isNotEmpty()) params["channel"] = it }
        screen.let { if (it.isNotEmpty()) params["screen"] = it }
        distinctId.let { if (it.isNotEmpty()) params["distinctId"] = it }
        networkType.let { if (it.isNotEmpty()) params["networkType"] = it }

        params["channel"] = "512"
        params["openId"] = ""
        params["wechatunionid"] = ""

        return params
    }

    /**
     * 合并业务参数和通用参数
     */
    fun mergeParams(
        businessParams: Map<String, Any>,
        commonParams: Map<String, Any>
    ): Map<String, Any> = commonParams.toMutableMap().apply { putAll(businessParams) }

    /**
     * 从参数Map构建查询参数字符串
     */
    fun buildQueryParamsFromMap(params: Map<String, Any>): String {
        return params.entries.joinToString(prefix = "?", separator = "&") { (key, value) ->
            val valueStr = value.toString()

            // 判断值是否包含JSON特殊字符
            val needsEncoding = valueStr.contains("{") || valueStr.contains("}") ||
                    valueStr.contains("[") || valueStr.contains("]") ||
                    valueStr.contains("\"") || valueStr.contains(":")

            val finalValue = if (needsEncoding) {
                // 对包含JSON特殊字符的值进行URL编码
                java.net.URLEncoder.encode(valueStr, "UTF-8")
            } else {
                // 其他普通值不做特殊处理
                valueStr
            }

            "$key=$finalValue"
        }
    }

    /**
     * 构建User-Agent
     */
    fun buildAppUserAgent(): String {
        val uaOsVersion = osVersion.replace("android", "Android ")
        return "YhStore/${RequestConfig.AppVersion.VERSION} cn.yonghui.hyd/${RequestConfig.AppVersion.VERSION_NUMBER} (client/phone; $uaOsVersion; $brand/$model)"
    }

    /**
     * 构建APPWeb User-Agent
     */
    fun buildAppWebUserAgent(): String {
        val uaOsVersion = osVersion.replace("android", "Android ")
        val userAgentAfterBuild =
            "TKQ1.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36"

        return "Mozilla/5.0 (Linux; $uaOsVersion; $brand Build/$userAgentAfterBuild " +
                "YhStore/${RequestConfig.AppVersion.VERSION} cn.yonghui.hyd/${RequestConfig.AppVersion.VERSION_NUMBER} " +
                "(client/phone; $uaOsVersion; $model/$brand)"
    }

    /**
     * 构建小程序User-Agent
     */
    fun buildMiniProgramUserAgent(): String {
        val uaOsVersion = osVersion.replace("android", "Android ")
        //TODO: 部分参数未构建
        return "Mozilla/5.0 (Linux; $uaOsVersion; $brand Build/TKQ1.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/134.0.6998.136 Mobile Safari/537.36 XWEB/1340123 MMWEBSDK/20250201 MMWEBID/5629 MicroMessenger/8.0.58.2841(0x28003A35) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 miniProgram/wxc9cf7c95499ee604"
    }
    //endregion

    //region 签名相关方法
    /**
     * 生成签名
     * @param url 待签名URL，SignType.APP 传入已经处理后的url，SignType.APP_WEB_ACTIVITY/SignType.APP_WEB_INFO 直接传入
     * @param body 请求体（默认为空）
     * @param signType 签名类型
     * @param distinctId 小程序distinctId
     * @return 签名字符串
     */
    suspend fun generateSign(
        url: String = "",
        body: String = "",
        signType: String,
        distinctId: String = "",
    ): String {
        if (url.isEmpty()) {
            Log.e(TAG, "无法生成签名: url为空")
            return ""
        }

        if (RequestConfig.DEBUG) {
            Log.d(TAG, "URL: $url")
        }

        // 根据签名类型选择签名算法
        val sign = when (SignType.fromString(signType)) {
            SignType.APP -> SignUtils.postToSign("/process", url)
            SignType.APP_WEB -> EncryptionUtil.appWebSign(url, body)
            SignType.MINIPROGRAM -> EncryptionUtil.miniProgramSign(url, body, distinctId)
            SignType.MINIPROGRAM_WEB_INFO -> EncryptionUtil.miniProgramWebSignForInfo(url, body)
            SignType.MINIPROGRAM_WEB_ACTIVITY -> EncryptionUtil.miniProgramWebSignForActivity(
                url,
                body
            )
        }

        if (RequestConfig.DEBUG) {
            Log.d(TAG, "Sign result: $sign")
        }

        return sign
    }
    //endregion
} 
