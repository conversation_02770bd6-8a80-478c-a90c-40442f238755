package dev.pigmomo.yhkit2025.api.model.coupon

/**
 * 新人弹窗响应数据类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 新人弹窗数据
 * @property now 当前时间戳
 */
data class NewPersonPopupResponse(
    val code: Int = 0,
    val message: String = "",
    val data: NewPersonPopupData? = null,
    val now: Long = 0
)

/**
 * 新人弹窗数据类
 * @property sendStatus 发送状态
 * @property status 状态
 * @property totalAmount 总金额
 * @property url 链接地址
 * @property sendType 发送类型
 * @property activityCode 活动代码
 * @property shuntValue 分流值
 * @property newPersonPopupVoList 新人弹窗列表
 * @property newComerCouponResult 新人优惠券结果
 * @property newPersonCouponVoList 新人优惠券列表
 * @property isConfig 是否配置，服务器返回数值类型
 */
data class NewPersonPopupData(
    val sendStatus: Int = 0,
    val status: Int = 0,
    val totalAmount: Int = 0,
    val url: String? = null,
    val sendType: String? = null,
    val activityCode: String? = null,
    val shuntValue: String? = null,
    val newPersonPopupVoList: List<Any>? = null,
    val newComerCouponResult: Any? = null,
    val newPersonCouponVoList: List<Any>? = null,
    val isConfig: Int? = null
) 