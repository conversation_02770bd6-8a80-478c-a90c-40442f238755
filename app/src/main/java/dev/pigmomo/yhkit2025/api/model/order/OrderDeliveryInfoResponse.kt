package dev.pigmomo.yhkit2025.api.model.order

data class OrderDeliveryInfoResponse(
    val code: Int = 0,
    val message: String = "",
    val data: OrderDeliveryInfoData? = null,
    val now: Long = 0
)

data class OrderDeliveryInfoData(
    val canupdatedelivery: Int = 0,
    val name: String = "",
    val phone: String = "",
    val remark: String = "",
    val expectTime: String = "",
    val dateList: List<DeliveryDate> = emptyList(),
    val remarks: List<DeliveryRemark> = emptyList(),
    val receiveInfo: DeliveryReceiveInfo? = null,
    val prompt: String = "",
    val canEditAddress: Int = 0
)

data class DeliveryDate(
    var date: String = "",
    var dateshow: String = "",
    val periodlist: List<DeliveryPeriod> = emptyList()
)

data class DeliveryPeriod(
    var starttime: String = "",
    val starttimeshow: String = "",
    var endtime: String = "",
    val endtimeshow: String = "",
    val isfull: Boolean = false
)

data class DeliveryRemark(
    val id: Int = 0,
    val message: String = ""
)

data class DeliveryReceiveInfo(
    val id: String = "",
    val name: String = "",
    val phone: String = "",
    val address: DeliveryAddress? = null,
    val location: DeliveryLocation? = null
)

data class DeliveryAddress(
    val city: String = "",
    val area: String = "",
    val detail: String = ""
)

data class DeliveryLocation(
    val lat: String = "",
    val lng: String = ""
) 