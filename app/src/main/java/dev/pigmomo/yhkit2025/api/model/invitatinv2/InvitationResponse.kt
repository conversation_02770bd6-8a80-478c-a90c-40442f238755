package dev.pigmomo.yhkit2025.api.model.invitatinv2

// 邀请奖励响应
data class InvitationRewardResponse(
    val code: Int = 0,
    val message: String = "",
    val data: List<InvitationReward> = emptyList(),
    val now: String = ""
)

// 成功邀请列表响应
data class SuccessInviteListResponse(
    val code: Int = 0,
    val message: String = "",
    val data: List<SuccessInvite> = emptyList(),
    val now: String = ""
)

// 邀请奖励数据项
data class InvitationReward(
    val nickName: String = "",
    val avator: String = "",
    val mobile: String = "",
    val rewardAmount: Double = 0.0,
    val completeTime: Long = 0,
    val inviteeType: String = "",
    val repeated: String = "0",
    val count: Int = 0,
    val pageCount: Int = 0,
    val rewardType: String = "",
    val rewardNum: Int? = null
)

// 成功邀请数据项
data class SuccessInvite(
    val nickName: String = "",
    val avator: String = "",
    val mobile: String = "",
    val inviteTime: Long = 0,
    val inviteeType: String = "",
    val status: Int = 0,
    val statusDesc: String? = null,
    val rewardType: String? = null,
    val rewardAmount: Double? = null,
    val couponVo: Any? = null,
    val count: Int = 0,
    val pageCount: Int = 0
) 