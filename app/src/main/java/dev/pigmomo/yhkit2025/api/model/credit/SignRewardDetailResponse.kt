package dev.pigmomo.yhkit2025.api.model.credit

/**
 * 签到奖励详情响应数据类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 签到奖励详情数据
 * @property now 当前时间戳
 */
data class SignRewardDetailResponse(
    val code: Int = 0,
    val message: String = "",
    val data: SignRewardDetailData? = null,
    val now: Long = 0
)

/**
 * 签到奖励详情数据类
 * @property description 活动描述
 * @property reSignInEnableFlag 是否启用重新签到
 * @property missionUserFlowId 任务用户流程ID
 * @property teamdividepointflag 团队分点标志
 * @property missionid 任务ID
 * @property signflag 签到标志
 * @property notifyflag 通知标志
 * @property signrewardnodes 签到奖励节点列表
 * @property cyclecount 周期计数
 * @property currentcount 当前计数
 * @property currentsigncount 当前签到计数
 * @property currentsignnum 当前签到数
 * @property extranodes 额外节点列表
 * @property signNewUserVo 新用户签到信息
 * @property newUserShowFlag 新用户显示标志
 * @property activitycode 活动代码
 * @property activityFinishFlag 活动完成标志
 * @property showPopupResult 显示弹窗结果
 * @property dividepointvalue 分点值
 */
data class SignRewardDetailData(
    val description: String = "",
    val reSignInEnableFlag: Boolean = false,
    val missionUserFlowId: Any? = null,
    val teamdividepointflag: Boolean = false,
    val missionid: Int = 0,
    val signflag: Boolean = false,
    val notifyflag: Boolean = false,
    val signrewardnodes: List<SignRewardNode> = emptyList(),
    val cyclecount: Int = 0,
    val currentcount: Int = 0,
    val currentsigncount: Int = 0,
    val currentsignnum: Int = 0,
    val extranodes: List<SignRewardDetailExtraNode> = emptyList(),
    val signNewUserVo: Any? = null,
    val newUserShowFlag: Boolean = false,
    val activitycode: String = "",
    val activityFinishFlag: Boolean = false,
    val showPopupResult: Boolean = false,
    val dividepointvalue: Int = 0
)

/**
 * 签到奖励节点类
 * @property signRewardSummaryVO 签到奖励摘要
 * @property itemid 项目ID
 * @property count 计数
 * @property signflag 签到标志
 * @property extraflag 额外标志
 * @property resigncompleteflag 重新完成标志
 * @property extranode 额外节点
 * @property signrewardvo 签到奖励信息
 * @property basicsignreward 基础签到奖励
 * @property extrasignreward 额外签到奖励
 * @property storesignreward 商店签到奖励
 */
data class SignRewardNode(
    val signRewardSummaryVO: SignRewardSummaryVO,
    val itemid: Int = 0,
    val count: Int = 0,
    val signflag: Boolean = false,
    val extraflag: Boolean = false,
    val resigncompleteflag: Any? = null,
    val extranode: Any? = null,
    val signrewardvo: SignRewardVO,
    val basicsignreward: SignRewardSummaryVO,
    val extrasignreward: Any? = null,
    val storesignreward: Any? = null
)

/**
 * 签到奖励摘要类
 * @property rewardFlag 奖励标志
 * @property couponName 优惠券名称
 * @property credit 积分
 * @property amount 金额
 */
data class SignRewardSummaryVO(
    val rewardFlag: Int = 0,
    val couponName: String? = null,
    val credit: Int = 0,
    val amount: Any? = null
)

/**
 * 签到奖励信息类
 * @property endTime 结束时间
 * @property creditFrom 积分起始值
 * @property creditTo 积分结束值
 * @property tips 提示
 * @property rewardflag 奖励标志
 * @property couponkindshowvos 优惠券种类显示信息
 * @property couponvos 优惠券信息
 * @property credit 积分
 * @property amount 金额
 * @property missinonitemtype 任务项目类型
 */
data class SignRewardVO(
    val endTime: Any? = null,
    val creditFrom: Any? = null,
    val creditTo: Any? = null,
    val tips: Any? = null,
    val rewardflag: Int = 0,
    val couponkindshowvos: Any? = null,
    val couponvos: Any? = null,
    val credit: Int = 0,
    val amount: Any? = null,
    val missinonitemtype: Any? = null
)

/**
 * 额外节点类
 * @property count 计数
 * @property signrewardvo 签到奖励信息
 * @property signflag 签到标志
 */
data class SignRewardDetailExtraNode(
    val count: Int = 0,
    val signrewardvo: SignRewardVO,
    val signflag: Boolean = false
) 