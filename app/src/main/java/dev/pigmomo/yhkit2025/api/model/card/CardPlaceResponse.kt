package dev.pigmomo.yhkit2025.api.model.card

/**
 * 卡片下单响应数据模型
 * 用于解析卡片下单接口返回的JSON数据
 */
data class CardPlaceResponse(
    val code: Int = 0,             // 响应状态码，0表示成功
    val message: String = "",      // 响应消息
    val data: CardPlaceData? = null, // 响应数据对象
    val now: Long = 0              // 服务器时间戳
)

/**
 * 卡片下单数据详情
 * 包含订单相关信息
 */
data class CardPlaceData(
    val orderId: String = "",      // 订单ID
    val shopId: String = "",       // 商店ID
    val totalAmount: String = "",  // 订单总金额
    val memberId: String = ""      // 会员ID
) 