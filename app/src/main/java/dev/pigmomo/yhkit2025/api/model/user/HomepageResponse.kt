package dev.pigmomo.yhkit2025.api.model.user

data class HomepageResponse(
    val code: Int = 0,
    val message: String = "",
    val data: HomepageData? = null,
    val now: Long = 0
)

data class HomepageData(
    val floors: List<Floor> = emptyList(),
    val existnewexclusivesku: Int = 0,
    val pageId: String = "",
    val subpageaid: String = "",
    val lbsSeller: Int = 0,
    val titles: List<TitleItem> = emptyList(),
    val shopid: String = "",
    val newhomepage: Boolean = false,
    val waterFallGuide: WaterFallGuide? = null,
    val loginPopVO: LoginPopVO? = null
)

data class Floor(
    val pid: String = "",
    val key: String = "",
    val keyForAb: String = "",
    val point: Int = 0,
    val assemblyremark: String = "",
    val value: Any? = null,
    val style: FloorStyle? = null,
    val modulename: String = "",
    val newhomepage: Boolean = false
)

data class FloorStyle(
    val height: Int = 0,
    val width: Int = 0,
    val moretag: Boolean = false,
    val showmoretag: Boolean = false,
    val spliceassembly: Boolean = false,
    val cornerstyle: Int = 0,
    val jewelstyle: Int = 0,
    val servicepromptstyle: Boolean = false,
    val seckillstyle: SeckillStyle? = null
)

data class SeckillStyle(
    val seckillimg: String = "",
    val secKillImgHeight: Int = 0,
    val secKillImgWidth: Int = 0,
    val seckilltitle: String = "",
    val secKillTitleHeight: Int = 0,
    val secKillTitleWidth: Int = 0,
    val secKillTitleText: String = "",
    val showmoretag: Boolean = false
)

data class TitleItem(
    val title: String = "",
    val path: String = "",
    val navTabType: Int = 0
)

data class WaterFallGuide(
    val browseNumber: Int = 0,
    val continueBrowseNumber: Int = 0,
    val stopTime: Int = 0
)

data class LoginPopVO(
    // 根据实际数据填充属性
    val newPersonLogin: String = ""
)

// Gallery 轮播组件
data class GalleryValue(
    val list: List<GalleryItem> = emptyList(),
    val style: GalleryStyle? = null,
    val modulename: String = "",
    val newhomepage: Boolean = false
)

data class GalleryItem(
    val id: String = "",
    val height: Int = 0,
    val width: Int = 0,
    val action: String = "",
    val name: String = "",
    val ipoint: Int = 0,
    val imgurl: String = "",
    val bgstartcolor: String = "",
    val bgendcolor: String = "",
    val searchButtonStartColor: String = "",
    val searchButtonEndColor: String = "",
    val bgImage: BgImage? = null
)

data class BgImage(
    val url: String = "",
    val w: Int = 0,
    val h: Int = 0,
    val isLightColor: Boolean = false,
    val isShowShading: Boolean = false
)

data class GalleryStyle(
    val height: Int = 0,
    val width: Int = 0
)

// 公告栏组件
data class PublicNotifyValue(
    val value: List<NotifyItem> = emptyList(),
    val modulename: String = ""
)

data class NotifyItem(
    val id: String = "",
    val action: String = "",
    val name: String = "",
    val ipoint: Int = 0,
    val notifytype: String = "",
    val notifycontext: String = "",
    val dialogcontext: String = "",
    val jumptype: String = ""
)

// 首页宣导语组件
data class SBannerValue(
    val id: String = "",
    val height: Int = 0,
    val width: Int = 0,
    val action: String = "",
    val name: String = "",
    val ipoint: Int = 0,
    val imgurl: String = ""
)

// 百宝箱组件
data class JewelValue(
    val value: List<JewelItem> = emptyList(),
    val style: JewelStyle? = null,
    val modulename: String = "",
    val newhomepage: Boolean = false
)

data class JewelItem(
    val id: String = "",
    val action: String = "",
    val name: String = "",
    val ipoint: Int = 0,
    val imgurl: String = "",
    val thirdrow: Boolean = false,
    val blowText: String = ""
)

data class JewelStyle(
    val jewelstyle: Int = 0,
    val spliceassembly: Boolean = false,
    val servicepromptstyle: Boolean = false
)

// 百宝箱侧滑组件
data class JewelSlideValue(
    val value: List<JewelSlideItem> = emptyList(),
    val style: JewelSlideStyle? = null,
    val modulename: String = ""
)

data class JewelSlideItem(
    val id: String = "",
    val action: String = "",
    val name: String = "",
    val ipoint: Int = 0,
    val imgurl: String = ""
)

data class JewelSlideStyle(
    val spliceassembly: Boolean = false
)

// 场景营销组件
data class MarketingSceneValue(
    val style: Int = 0,
    val backgroundStyle: BackgroundStyle? = null,
    val resourceVisuals: List<ResourceVisual> = emptyList(),
    val visualStyle: String = ""
)

data class BackgroundStyle(
    val title: String = "",
    val imageUrl: String = "",
    val height: Int = 0,
    val width: Int = 0,
    val actionUrl: String = ""
)

data class ResourceVisual(
    val title: String = "",
    val action: String = "",
    val ipoint: Int = 0,
    val imgurl: String = "",
    val positionNum: Int = 0
)

// 秒杀组件
data class SeckillValue(
    val seckillimg: String = "",
    val secKillImgHeight: Int = 0,
    val secKillImgWidth: Int = 0,
    val seckilltitle: String = "",
    val secKillTitleHeight: Int = 0,
    val secKillTitleWidth: Int = 0,
    val secKillTitleText: String = "",
    val secKillRound: List<SecKillRound> = emptyList(),
    val recommendTxtReady: String = "",
    val recommendTxtOn: String = "",
    val recommendTxtOut: String = "",
    val userImages: List<String> = emptyList()
)

data class SecKillRound(
    val skuBanners: List<SkuBanner> = emptyList(),
    val enabledFrom: Long = 0,
    val enabledTo: Long = 0,
    val secKillTab: Int = 0,
    val action: String = "",
    val period: Int = 0
)

data class SkuBanner(
    val sorts: Int = 0,
    val action: String = "",
    val ipoint: Int = 0,
    val skuimg: String = "",
    val displayname: String = "",
    val originalprice: Double = 0.0,
    val salesprice: Double = 0.0,
    val activityprice: Double = 0.0,
    val skucode: String = "",
    val specprop: String = "",
    val limitorder: Int = 0,
    val haveqty: Boolean = false,
    val skusaletype: Int = 0,
    val thriftAmount: Double = 0.0
)

// 四宫格组件
data class SlicebitValue(
    val activities: List<SliceActivity> = emptyList(),
    val sliceLayoutVos: List<SliceLayoutVo> = emptyList(),
    val skuPriceConfig: Int = 0,
    val visionStrong: Int = 0,
    val seliceNumber: Int = 0
)

data class SliceActivity(
    val promotionTagValue: Int = 0,
    val ipoint: Int = 0,
    val title: String = "",
    val subTitle: String = "",
    val subTitleTextColor: String = "",
    val skus: List<SliceSku> = emptyList(),
    val action: String = "",
    val id: String = "",
    val subTitleGradientColorStart: String = "",
    val subTitleGradientColorEnd: String = "",
    val elementIndexNum: Int = 0,
    val positionNum: Int = 0,
    val subTitleStreng: Boolean = false,
    val promotionTagInfo: Map<String, PromotionTagInfo>? = null
)

data class SliceLayoutVo(
    val index: Int = 0,
    val activities: List<SliceActivity> = emptyList(),
    val skuPriceFlag: Int = 0
)

data class SliceSku(
    val categoryInfo: CategoryInfo? = null,
    val type: Int = 0,
    val action: String = "",
    val skuCode: String = "",
    val skuType: Int = 0,
    val skuSaleType: Int = 0,
    val cover: Cover? = null,
    val price: HomepagePrice? = null,
    val batch: Batch? = null,
    val cornerImageUrl: String = "",
    val tag: HomepageTag? = null,
    val spu: Spu? = null,
    val title: String = "",
    val subTitle: String = "",
    val recAttribute: List<String> = emptyList(),
    val recSlogan: String = "",
    val recSloganType: String = "",
    val inStock: Int = 0,
    val tracking: Tracking? = null,
    val preprocess: Int = 0,
    val cornerStyle: Int = 0,
    val isOnSale: Boolean = false,
    val skuProperty: Int = 0,
    val brandId: String = "",
    val brandName: String = ""
)

data class CategoryInfo(
    val categoryId: String = ""
)

data class Cover(
    val imageUrl: String = ""
)

data class HomepagePrice(
    val price: String = "",
    val marketPrice: String = "",
    val promotionMsg: String = ""
)

data class Batch(
    val batchFlag: Int = 0
)

data class HomepageTag(
    val titleTag: TitleTag? = null,
    val titleTags: List<TitleTag> = emptyList(),
    val commonTags: List<CommonTag> = emptyList()
)

data class TitleTag(
    val text: String = "",
    val type: Int = 0,
    val imageUrl: String = ""
)

data class CommonTag(
    val text: String = "",
    val type: Int = 0,
    val imageUrl: String = "",
    val tagType: Int = 0
)

data class Spu(
    val isSpu: Int = 0
)

data class Tracking(
    val priceInCent: Int = 0,
    val marketPriceInCent: Int = 0
)

data class PromotionTagInfo(
    val text: String = "",
    val type: Int = 0
)

// 垂直品类组件
data class BannerBarValue(
    val marketingres: List<MarketingRes> = emptyList()
)

data class MarketingRes(
    val id: String = "",
    val height: Int = 0,
    val width: Int = 0,
    val action: String = "",
    val name: String = "",
    val title: String = "",
    val subTitleColor: String = "",
    val ipoint: Int = 0,
    val imgurl: String = "",
    val subtitle: String = "",
    val backgroundimg: String = ""
)

// 瀑布流组件
data class NavigationTabValue(
    val position: Int = 0,
    val title: String = "",
    val type: String = "",
    val subtitle: String = ""
)