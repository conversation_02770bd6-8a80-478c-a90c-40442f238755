package dev.pigmomo.yhkit2025.api.service

import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestHelper

/**
 * API服务基类
 * 提供所有API服务共用的基础方法和属性
 */
abstract class BaseService(protected val requestHelper: RequestHelper) {
    /**
     * 基础业务参数，所有API请求通用
     * @return 基础业务参数Map
     */
    open fun buildAppBusinessParams(): MutableMap<String, Any> {
        return mutableMapOf(
            "os" to RequestConfig.AppBaseBusinessParams.OS,
            "elderly" to RequestConfig.AppBaseBusinessParams.ELDERLY,
            "platform" to RequestConfig.AppBaseBusinessParams.PLATFORM,
            "channelMain" to RequestConfig.AppBaseBusinessParams.CHANNEL_MAIN,
            "productLine" to RequestConfig.AppBaseBusinessParams.PRODUCT_LINE,
            "channelSub" to RequestConfig.AppBaseBusinessParams.CHANNEL_SUB,
            "jysessionid" to requestHelper.jysessionid,
            "access_token" to requestHelper.getAccessToken(),
        )
    }

    open fun buildAppWebBusinessParams(): MutableMap<String, Any> {
        return mutableMapOf(
            "os" to RequestConfig.AppBaseBusinessParams.OS,
            "platform" to RequestConfig.AppBaseBusinessParams.PLATFORM,
            "productLine" to RequestConfig.AppBaseBusinessParams.PRODUCT_LINE,
            "channelSub" to RequestConfig.AppBaseBusinessParams.CHANNEL_SUB,
            "appType" to "h5",
            "app_version" to RequestConfig.AppVersion.VERSION,
            "jysessionid" to requestHelper.jysessionid,
            "access_token" to "${requestHelper.getUserKey()}-601933-${requestHelper.getAccessToken()}"
        )
    }

    /**
     * 构建标准API请求头
     * @param xyhBizParams X-YH-Biz-Params头参数
     * @param includeContentType 是否包含Content-Type头
     * @return 请求头Map
     */
    open fun buildAppStandardHeaders(
        xyhBizParams: String,
        buildWebUserAgentType: Boolean = false,
        includeContentType: Boolean = false
    ): MutableMap<String, String> {
        val headers = mutableMapOf(
            "X-YH-Biz-Params" to xyhBizParams,
            "X-YH-Context" to "origin=android&morse=1",
            "User-Agent" to if (buildWebUserAgentType) requestHelper.buildAppWebUserAgent() else requestHelper.buildAppUserAgent()
        )

        if (includeContentType) {
            headers["DNT"] = "0"
            headers["Content-Type"] = "application/json; charset=UTF-8"
            headers["Cookie"] = "userKey=${requestHelper.getUserKey()}"
        }

        return headers
    }

    /**
     * 构建API请求URL
     * @param path API路径
     * @param businessParams 业务参数
     * @param commonParams 通用参数
     * @return 构建好的URL（不含签名）
     */
    open fun buildAppApiUrl(
        path: String,
        businessParams: Map<String, Any>,
        commonParams: Map<String, Any>
    ): String {
        val mergedParams = requestHelper.mergeParams(businessParams, commonParams)
        val url = RequestConfig.getAppFullUrl(path)
        val queryParams = requestHelper.buildQueryParamsFromMap(mergedParams)
        return "$url$queryParams"
    }

    /**
     * 基础业务参数，所有API请求通用
     * @return 基础业务参数Map
     */
    open fun buildMiniProgramBusinessParams(): MutableMap<String, Any> {
        return mutableMapOf(
            "os" to RequestConfig.MiniProgramBaseBusinessParams.OS,
            "platform" to RequestConfig.MiniProgramBaseBusinessParams.PLATFORM,
            "channelMain" to RequestConfig.MiniProgramBaseBusinessParams.CHANNEL_MAIN,
            "productLine" to RequestConfig.MiniProgramBaseBusinessParams.PRODUCT_LINE,
            "appType" to RequestConfig.MiniProgramBaseBusinessParams.APP_TYPE,
            "proportion" to RequestConfig.MiniProgramBaseBusinessParams.PROPORTION,
            "appid" to RequestConfig.MiniProgramBaseBusinessParams.APP_ID,
            "channel" to RequestConfig.MiniProgramBaseBusinessParams.CHANNEL,
            "v" to RequestConfig.MiniProgramVersion.VERSION,
            "sdk_version" to RequestConfig.MiniProgramVersion.SDK_VERSION,
            "wechat_version" to RequestConfig.MiniProgramVersion.WECHAT_VERSION,
            "jysessionid" to requestHelper.jysessionid
        )
    }

    open fun buildMiniProgramWebBusinessParams(): MutableMap<String, Any> {
        return mutableMapOf(
            "os" to RequestConfig.MiniProgramBaseBusinessParams.OS,
            "platform" to RequestConfig.MiniProgramBaseBusinessParams.PLATFORM,
            "productLine" to RequestConfig.MiniProgramBaseBusinessParams.PRODUCT_LINE,
            "channelSub" to RequestConfig.MiniProgramBaseBusinessParams.CHANNEL_SUB,
            "appid" to RequestConfig.MiniProgramBaseBusinessParams.APP_ID,
            "appType" to "h5",
            "v" to RequestConfig.MiniProgramVersion.VERSION,
            "app_version" to RequestConfig.MiniProgramVersion.VERSION,
            "jysessionid" to requestHelper.jysessionid,
            "access_token" to "${requestHelper.getUserKey()}-601933-${requestHelper.getAccessToken()}"
        )
    }

    /**
     * 构建标准API请求头
     * @param xyhBizParams X-YH-Biz-Params头参数
     * @param isWebUserKey 是否构建Web格式 userKey
     * @return 请求头Map
     */
    open fun buildMiniStandardHeaders(
        xyhBizParams: String,
        isWebUserKey: Boolean = false
    ): MutableMap<String, String> {
        val headers = mutableMapOf(
            "X-YH-Biz-Params" to xyhBizParams,
            "X-YH-Context" to "origin=${if (isWebUserKey) "h5" else "wechatmini"}&morse=1",
            "User-Agent" to requestHelper.buildMiniProgramUserAgent(),
            "Referer" to if(isWebUserKey) "https://m.yonghuivip.com/" else "https://servicewechat.com/wxc9cf7c95499ee604/778/page-frame.html",
        )

        if (!isWebUserKey) {
            headers["userKey"] = requestHelper.getUserKey()
        }

        return headers
    }

    /**
     * 构建API请求URL
     * @param path API路径
     * @param businessParams 业务参数
     * @param commonParams 通用参数
     * @param hostType 主机类型，可选值为 "activity" 或 "api"
     * @return 构建好的URL（不含签名）
     */
    open fun buildMiniApiUrl(
        path: String,
        businessParams: Map<String, Any>,
        commonParams: Map<String, Any>,
        hostType: String = "activity"
    ): String {
        val mergedParams = requestHelper.mergeParams(businessParams, commonParams)
        val url = if (hostType == "activity") RequestConfig.getMiniProgramFullUrl(path) else RequestConfig.getAppFullUrl(path)
        val queryParams = requestHelper.buildQueryParamsFromMap(mergedParams)
        return "$url$queryParams"
    }
}