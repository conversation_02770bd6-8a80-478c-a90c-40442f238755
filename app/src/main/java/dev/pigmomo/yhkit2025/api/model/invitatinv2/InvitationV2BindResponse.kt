package dev.pigmomo.yhkit2025.api.model.invitatinv2

/**
 * 绑定邀请关系V2响应数据类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 绑定邀请关系数据
 * @property now 当前时间戳
 */
data class InvitationV2BindResponse(
    val code: Int = 0,
    val message: String = "",
    val data: InvitationV2BindData? = null,
    val now: String = ""
)

/**
 * 绑定邀请关系V2数据类
 * @property showDialog 是否显示对话框，1为显示，0为不显示
 * @property resultCode 结果码，0为绑定成功，201表示已是老用户
 * @property showMsg 展示消息内容
 * @property btnDesc 按钮描述
 * @property actionUrl 按钮跳转链接
 */
data class InvitationV2BindData(
    val showDialog: Int = 0,
    val resultCode: Int = 0,
    val showMsg: String = "",
    val btnDesc: String = "",
    val actionUrl: String = ""
) 