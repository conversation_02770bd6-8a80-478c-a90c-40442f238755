package dev.pigmomo.yhkit2025.api.model.card

/**
 * 验证卡片或链接响应数据类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 验证卡片数据
 * @property now 当前时间戳
 */
data class ValidateCardOrLinkResponse(
    val code: Int = 0,
    val message: String = "",
    val data: ValidateCardOrLinkData? = null,
    val now: Long = 0
)

/**
 * 验证卡片或链接数据类
 * @property cardNo 卡号
 * @property balance 卡片余额
 */
data class ValidateCardOrLinkData(
    val cardNo: String = "",
    val balance: Double = 0.0
) 