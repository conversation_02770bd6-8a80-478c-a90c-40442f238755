package dev.pigmomo.yhkit2025.api.model.cart

data class CartResponse(
    val code: Int = 0,
    val message: String = "",
    val data: CartData? = null,
    val now: Long = 0
)

data class CartData(
    val cartTotalPrice: Int = 0,              // 购物车总价（单位：分）
    val cartTotalTaxes: Int = 0,              // 总税费
    val cartTotalDiscount: Int = 0,           // 总折扣金额
    val cartTotalActivityDiscount: Int = 0,   // 活动折扣总额
    val cartTotalCouponDiscount: Int = 0,     // 优惠券折扣总额
    val cartPromotionDetailInfo: PromotionDetailInfo? = null,  // 促销详情信息
    val cartPromotionMsg: String = "",        // 促销消息
    val cartlist: List<CartItem> = emptyList(), // 购物车列表
    val carttotalredenvelopdiscount: Int = 0, // 红包总折扣
    val totalamountmsg: String = "",          // 总价消息
    val cartkey: String = ""                  // 购物车唯一标识
)

data class PromotionDetailInfo(
    val title: String = "",
    val promotions: List<Promotion> = emptyList()
)

data class Promotion(
    val title: String = "",
    val amount: String = "",
    val highlight: Int = 0
)

data class CartItem(
    val type: String = "",
    val cartType: String = "",
    val discount: Int = 0,
    val activityDiscount: Int = 0,
    val couponDiscount: Int = 0,
    val carriage: Int = 0,
    val seller: CartSellerInfo? = null,
    val cartId: String = "",
    val storeid: String = "",
    val shopid: String = "",
    val realshopid: String = "",
    val shopname: String = "",
    val totalPayment: Int = 0,         // 实付金额
    val priceTotal: Int = 0,           // 价格总计
    val cartheadermsg: String = "",    // 购物车头部消息
    val cartheadermsgflag: Int = 0,
    val cartheadermsgstatus: Int = 0,
    val promotionmsg: String = "",     // 促销信息
    val totalprice: Int = 0,           // 总价
    val totalpaymentamt: Int = 0,      // 总支付金额
    val taxamt: Int = 0,               // 税额
    val ptotalamount: Int = 0,
    val cartModels: List<CartModel> = emptyList(), // 购物车模型列表
    val additionalBuyCacheKey: String = "" // 额外购买缓存键
)

data class CartSellerInfo(
    val id: String = "",
    val title: String = "",
    val icon: String = "",
    val action: String = "",
    val sellername: String = "",
    val cityname: String = ""
)

data class CartModel(
    val data: Any? = null,
    val modelType: Int = 0,
    val backgroundColor: String = "",
    val bottomMargin: Int = 0,
    val topPadding: Int = 0,
    val bottomPadding: Int = 0
)