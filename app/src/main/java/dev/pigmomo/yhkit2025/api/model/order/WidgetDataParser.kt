package dev.pigmomo.yhkit2025.api.model.order

import com.google.gson.Gson
import com.google.gson.JsonArray
import com.google.gson.JsonElement
import com.google.gson.JsonObject

/**
 * 小部件数据解析器
 * 根据widgettype和name解析原始数据到对应的数据类
 */
object WidgetDataParser {
    private val gson = Gson()

    /**
     * 解析小部件数据
     * @param orderPlaceWidget 原始小部件数据
     * @return 解析后的小部件数据，类型根据widgettype和name确定
     */
    fun parseWidgetData(orderPlaceWidget: OrderPlaceWidget): Any? {
        val widgetType = orderPlaceWidget.widgettype
        val name = orderPlaceWidget.name
        val rawData = orderPlaceWidget.data

        // 如果原始数据为null，直接返回null
        rawData ?: return null

        return try {
            when (widgetType) {
                "row" -> parseRowWidgetData(name, rawData)
                "group" -> parseGroupWidgetData(rawData)
                else -> rawData
            }
        } catch (e: Exception) {
            rawData
        }
    }

    /**
     * 解析行类型小部件数据
     * @param name 小部件名称
     * @param rawData 原始数据
     * @return 解析后的具体类型数据
     */
    private fun parseRowWidgetData(name: String, rawData: Any): BaseWidgetData {
        val jsonElement = convertToJsonElement(rawData)

        return when (name) {
            "address" -> gson.fromJson(jsonElement, AddressWidgetData::class.java)
            "packages" -> gson.fromJson(jsonElement, PackagesWidgetData::class.java)
            "deliveryamountinfo" -> gson.fromJson(
                jsonElement,
                DeliveryAmountWidgetData::class.java
            )

            "coupons" -> gson.fromJson(jsonElement, CouponsWidgetData::class.java)
            "redpacket" -> gson.fromJson(jsonElement, RedPacketWidgetData::class.java)
            "combinpay" -> gson.fromJson(jsonElement, CombinPayWidgetData::class.java)
            "stockLackRemarkNew" -> gson.fromJson(
                jsonElement,
                StockLackRemarkWidgetData::class.java
            )

            "putDoorWayRemarkNew" -> gson.fromJson(
                jsonElement,
                PutDoorWayRemarkWidgetData::class.java
            )

            "remarkNew" -> gson.fromJson(jsonElement, RemarkWidgetData::class.java)
            "invoicenoterspNew" -> gson.fromJson(jsonElement, InvoiceWidgetData::class.java)
            "productstotalamount" -> gson.fromJson(
                jsonElement,
                ProductsTotalAmountWidgetData::class.java
            )

            "points" -> gson.fromJson(jsonElement, PointsWidgetData::class.java)
            "subtotalamount" -> gson.fromJson(jsonElement, SubtotalAmountWidgetData::class.java)
            "discount" -> gson.fromJson(jsonElement, DiscountWidgetData::class.java)
            "shoppingbags_delivery" -> gson.fromJson(
                jsonElement,
                ShoppingBagsDeliveryWidgetData::class.java
            )
            "shoppingbags_pickself" -> gson.fromJson(
                jsonElement,
                ShoppingBagsPickSelfWidgetData::class.java
            )

            "freedelivery" -> gson.fromJson(jsonElement, FreeDeliveryWidgetData::class.java)

            else -> gson.fromJson(jsonElement, CommonRowWidgetData::class.java)
        }
    }

    /**
     * 解析组类型小部件数据
     * @param rawData 原始数据
     * @return 解析后的组类型数据
     */
    private fun parseGroupWidgetData(rawData: Any): GroupWidgetData {
        val jsonElement = convertToJsonElement(rawData)

        try {
            val dataArray = when {
                // JsonArray
                jsonElement.isJsonArray ->
                    jsonElement.asJsonArray

                // JsonObject
                jsonElement is JsonObject && jsonElement.has("data") -> {
                    val dataElement = jsonElement.get("data")
                    if (dataElement.isJsonArray) {
                        dataElement.asJsonArray
                    } else {
                        JsonArray()
                    }
                }

                else -> {
                    JsonArray()
                }
            }

            val orderPlaceWidgets = dataArray.mapNotNull {
                try {
                    gson.fromJson(it, OrderPlaceWidget::class.java)
                } catch (e: Exception) {
                    null
                }
            }

            return GroupWidgetData(orderPlaceWidgets)
        } catch (e: Exception) {
            return GroupWidgetData(emptyList())
        }
    }

    /**
     * 将任意对象转换为JsonElement
     * @param obj 需要转换的对象
     * @return 转换后的JsonElement
     */
    private fun convertToJsonElement(obj: Any): JsonElement {
        // 如果已经是JsonElement，直接返回
        if (obj is JsonElement) return obj

        // 否则，先将对象转为JSON字符串，再解析为JsonElement
        val json = gson.toJson(obj)
        return gson.fromJson(json, JsonElement::class.java)
    }
} 