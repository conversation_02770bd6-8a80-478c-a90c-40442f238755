package dev.pigmomo.yhkit2025.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.model.LogEntity
import dev.pigmomo.yhkit2025.data.repository.LogRepositoryImpl
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import java.util.Date

/**
 * 日志视图模型
 * 用于管理日志数据和界面状态
 * @param application 应用上下文
 */
class LogViewModel(application: Application) : AndroidViewModel(application) {

    private val repository: LogRepositoryImpl

    // 每页加载的日志数量
    private val PAGE_SIZE = 20

    // 当前页码
    private val _currentPage = MutableStateFlow(1)

    // 是否有更多数据可加载
    private val _hasMoreData = MutableStateFlow(true)
    val hasMoreData: StateFlow<Boolean> = _hasMoreData

    // 当前查询模式
    private var currentQueryMode: QueryMode = QueryMode.ALL
    private var currentQueryParam: String = ""

    // 界面显示的日志列表
    private val _logs = MutableStateFlow<List<LogEntity>>(emptyList())
    val logs: StateFlow<List<LogEntity>> = _logs

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading

    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage

    init {
        val logDao = AppDatabase.getDatabase(application).logDao()
        repository = LogRepositoryImpl(logDao)
    }

    // 查询模式枚举
    private enum class QueryMode {
        ALL,
        BY_LEVEL,
        BY_TOKEN,
        BY_TAG,
        BY_PHONE,
        SEARCH,
        BY_LEVEL_AND_SEARCH
    }

    /**
     * 重置状态并加载第一页
     */
    private fun resetAndLoad(mode: QueryMode, param: String = "") {
        _currentPage.value = 1
        _hasMoreData.value = true
        currentQueryMode = mode
        currentQueryParam = param
        loadNextPage()
    }

    /**
     * 加载下一页数据
     */
    fun loadNextPage() {
        if (!_hasMoreData.value || _isLoading.value) return

        _isLoading.value = true
        val page = _currentPage.value
        val offset = (page - 1) * PAGE_SIZE
        val limit = PAGE_SIZE

        viewModelScope.launch(Dispatchers.IO) {
            try {
                val newLogs: List<LogEntity> = when (currentQueryMode) {
                    QueryMode.ALL -> repository.getLogsWithPagination(offset, limit)
                    QueryMode.BY_LEVEL -> repository.getLogsByLevelWithPagination(
                        currentQueryParam,
                        offset,
                        limit
                    )

                    QueryMode.BY_TOKEN -> repository.getLogsByTokenWithPagination(
                        currentQueryParam,
                        null,
                        offset,
                        limit
                    )

                    QueryMode.BY_TAG -> repository.getLogsByTagWithPagination(
                        currentQueryParam,
                        offset,
                        limit
                    )

                    QueryMode.BY_PHONE -> repository.getLogsByPhoneNumberWithPagination(
                        currentQueryParam,
                        offset,
                        limit
                    )

                    QueryMode.SEARCH -> repository.searchLogsWithPagination(
                        currentQueryParam,
                        offset,
                        limit
                    )

                    QueryMode.BY_LEVEL_AND_SEARCH -> {
                        // 从currentQueryParam中提取日志级别和搜索关键词
                        val params = currentQueryParam.split("|||")
                        if (params.size == 2) {
                            val level = params[0]
                            val keyword = params[1]
                            repository.searchLogsByLevelWithPagination(
                                level,
                                keyword,
                                offset,
                                limit
                            )
                        } else {
                            emptyList()
                        }
                    }
                }

                // 更新数据
                if (newLogs.isNotEmpty()) {
                    if (page == 1) {
                        _logs.value = newLogs
                    } else {
                        _logs.value = _logs.value + newLogs
                    }
                    _currentPage.value = page + 1
                } else {
                    if (page == 1) {
                        _logs.value = emptyList()
                    }
                }

                // 判断是否还有更多数据
                _hasMoreData.value = newLogs.size >= PAGE_SIZE

            } catch (e: Exception) {
                _errorMessage.value = e.message
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 加载所有日志
     */
    fun loadAllLogs() {
        resetAndLoad(QueryMode.ALL)
    }

    /**
     * 按令牌ID加载日志
     * @param tokenUid 令牌ID
     */
    fun loadLogsByToken(tokenUid: String) {
        resetAndLoad(QueryMode.BY_TOKEN, tokenUid)
    }

    /**
     * 按手机号加载日志
     * @param phoneNumber 手机号
     */
    fun loadLogsByPhoneNumber(phoneNumber: String) {
        resetAndLoad(QueryMode.BY_PHONE, phoneNumber)
    }

    /**
     * 按标签加载日志
     * @param tag 日志标签
     */
    fun loadLogsByTag(tag: String) {
        resetAndLoad(QueryMode.BY_TAG, tag)
    }

    /**
     * 按日志级别加载日志
     * @param logLevel 日志级别
     */
    fun loadLogsByLevel(logLevel: String) {
        resetAndLoad(QueryMode.BY_LEVEL, logLevel)
    }

    /**
     * 搜索日志
     * @param keyword 搜索关键字
     */
    fun searchLogs(keyword: String) {
        resetAndLoad(QueryMode.SEARCH, keyword)
    }

    /**
     * 同时按日志级别和关键词搜索日志
     * @param logLevel 日志级别
     * @param keyword 搜索关键词
     */
    fun loadLogsByLevelAndSearch(logLevel: String, keyword: String) {
        // 使用分隔符组合参数，在loadNextPage中解析
        val combinedParam = "$logLevel|||$keyword"
        resetAndLoad(QueryMode.BY_LEVEL_AND_SEARCH, combinedParam)
    }

    /**
     * 清除所有日志
     */
    fun clearAllLogs() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                repository.clearAllLogs()
                _logs.value = emptyList()
                _currentPage.value = 1
                _hasMoreData.value = false
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }

    /**
     * 删除特定令牌的日志
     * @param tokenUid 令牌ID
     */
    fun deleteLogsByToken(tokenUid: String) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                repository.deleteLogsByToken(tokenUid)
                loadAllLogs()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }

    /**
     * 删除特定手机号的日志
     * @param phoneNumber 手机号
     */
    fun deleteLogsByPhoneNumber(phoneNumber: String) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                repository.deleteLogsByPhoneNumber(phoneNumber)
                loadAllLogs()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }

    /**
     * 删除指定时间之前的日志
     * @param days 天数
     */
    fun deleteOldLogs(days: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val calendar = java.util.Calendar.getInstance()
                calendar.add(java.util.Calendar.DAY_OF_MONTH, -days)
                repository.deleteLogsBefore(calendar.time)
                loadAllLogs()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
} 