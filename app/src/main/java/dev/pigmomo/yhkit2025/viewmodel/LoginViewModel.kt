package dev.pigmomo.yhkit2025.viewmodel

import android.app.Application
import android.content.ClipboardManager
import android.content.Context
import android.net.Uri
import android.util.Log
import android.widget.Toast
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.api.model.coupon.CouponListCoupon
import dev.pigmomo.yhkit2025.api.utils.AddressUtils.matchAndAssignParamsByXYHBizParamsCommon
import dev.pigmomo.yhkit2025.api.utils.ResponseParserUtils
import dev.pigmomo.yhkit2025.api.utils.login.PasswordLoginUtils
import dev.pigmomo.yhkit2025.api.utils.SignUtils
import dev.pigmomo.yhkit2025.api.utils.login.DeviceInfoProvider
import dev.pigmomo.yhkit2025.api.utils.login.SmsLoginUtils
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.model.LoginTokenEntity
import dev.pigmomo.yhkit2025.data.model.PhoneWithPasswordEntity
import dev.pigmomo.yhkit2025.data.repository.LogRepository
import dev.pigmomo.yhkit2025.data.repository.PhoneWithPasswordRepository
import dev.pigmomo.yhkit2025.data.repository.TokenRepository
import dev.pigmomo.yhkit2025.service.BackgroundService
import dev.pigmomo.yhkit2025.ui.dialog.ExportFilterOptionForLoginToken
import dev.pigmomo.yhkit2025.utils.ClipboardUtils
import dev.pigmomo.yhkit2025.utils.HttpProxyUtils
import dev.pigmomo.yhkit2025.utils.TokenParserUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 登录视图模型
 * 用于管理登录相关的业务逻辑和UI状态
 */
class LoginViewModel(
    private val tokenRepository: TokenRepository,
    private val phoneWithPasswordRepository: PhoneWithPasswordRepository,
    application: Application
) : AndroidViewModel(application) {

    // 日志仓库，用于记录操作日志
    private var logRepository: LogRepository? = null

    // 应用上下文
    private val appContext = getApplication<Application>().applicationContext

    // 登录令牌列表
    private val _loginTokens = MutableStateFlow<List<LoginTokenEntity>>(emptyList())
    val loginTokens: StateFlow<List<LoginTokenEntity>> = _loginTokens

    // 手机号和密码列表
    private val _phoneWithPasswords = MutableStateFlow<List<PhoneWithPasswordEntity>>(emptyList())
    val phoneWithPasswords: StateFlow<List<PhoneWithPasswordEntity>> = _phoneWithPasswords

    // 当前选中的令牌UID
    private val _clickedTokenUid = mutableStateOf<String?>(null)
    val clickedTokenUid: State<String?> = _clickedTokenUid

    // 长按选中的令牌UID
    private val _longClickedTokenUid = mutableStateOf<String?>(null)
    val longClickedTokenUid: State<String?> = _longClickedTokenUid

    // 令牌详情对话框可见性
    private val _isTokenDetailDialogVisible = mutableStateOf(false)
    val isTokenDetailDialogVisible: State<Boolean> = _isTokenDetailDialogVisible

    // 账号导出状态
    private val _accountExporting = mutableStateOf(false)
    val accountExporting: State<Boolean> = _accountExporting

    // 导出账号对话框可见性
    private val _isExportAccountDialogVisible = mutableStateOf(false)
    val isExportAccountDialogVisible: State<Boolean> = _isExportAccountDialogVisible

    // 导出筛选选项
    private val _exportFilterOption = mutableStateOf(ExportFilterOptionForLoginToken.ALL)

    // 账号导入状态
    private val _accountImporting = mutableStateOf(false)
    val accountImporting: State<Boolean> = _accountImporting

    // 正在登录的手机号列表 密码登录
    private val _loggingInPhoneNumbersForPassword = MutableStateFlow<Set<String>>(emptySet())
    val loggingInPhoneNumbersForPassword: StateFlow<Set<String>> =
        _loggingInPhoneNumbersForPassword.asStateFlow()

    // 正在登录的手机号列表 短信登录
    private val _loggingInPhoneNumbersForSms = MutableStateFlow<Set<String>>(emptySet())
    val loggingInPhoneNumbersForSms: StateFlow<Set<String>> =
        _loggingInPhoneNumbersForSms.asStateFlow()

    // 手机号、验证码输入框
    private val _inputStr = mutableStateOf("")
    val inputStr: State<String> = _inputStr

    // 多线程登录
    private val _isMultiThreadLogin = mutableStateOf(false)
    val isMultiThreadLogin: State<Boolean> = _isMultiThreadLogin

    // 多线程登录对话框
    private val _isMultiThreadLoginDialogVisible = mutableStateOf(false)
    val isMultiThreadLoginDialogVisible: State<Boolean> = _isMultiThreadLoginDialogVisible

    // 剩余短信数量
    private val _remainingMessages = mutableStateOf(0)
    val remainingMessages: State<Int> = _remainingMessages

    // 自动注册状态
    private val _isAutoRegistering = mutableStateOf(false)
    val isAutoRegistering: State<Boolean> = _isAutoRegistering

    // 查询状态
    private val _isQuerying = mutableStateOf(false)
    val isQuerying: State<Boolean> = _isQuerying

    // 查询确认对话框
    private val _isQueryConfirmDialogVisible = mutableStateOf(false)
    val isQueryConfirmDialogVisible: State<Boolean> = _isQueryConfirmDialogVisible

    // 悬浮窗可见性
    private val _isFloatingWindowShown = mutableStateOf(false)
    val isFloatingWindowShown: State<Boolean> = _isFloatingWindowShown

    // 初始化，加载登录令牌和手机号密码
    init {
        loadLoginTokens()
        loadPhoneWithPasswords()
    }

    /**
     * 设置日志仓库
     * @param repository 日志仓库实例
     */
    fun setLogRepository(repository: LogRepository) {
        logRepository = repository
    }

    /**
     * 加载所有登录令牌
     */
    private fun loadLoginTokens() {
        viewModelScope.launch {
            tokenRepository.getAllLoginTokens().collect { tokens ->
                _loginTokens.value = tokens
            }
        }
    }

    /**
     * 加载所有手机号和密码
     */
    private fun loadPhoneWithPasswords() {
        viewModelScope.launch {
            phoneWithPasswordRepository.getAllPhoneWithPasswords().collect { entities ->
                _phoneWithPasswords.value = entities
            }
        }
    }

    /**
     * 设置当前选中的令牌UID
     * @param uid 令牌UID
     */
    fun setClickedTokenUid(uid: String?) {
        _clickedTokenUid.value = uid
    }

    /**
     * 设置长按选中的令牌UID
     * @param uid 令牌UID
     */
    fun setLongClickedTokenUid(uid: String?) {
        _longClickedTokenUid.value = uid
    }

    /**
     * 设置令牌详情对话框可见性
     * @param visible 是否可见
     */
    fun setTokenDetailDialogVisible(visible: Boolean) {
        _isTokenDetailDialogVisible.value = visible
    }

    /**
     * 设置导出账号对话框可见性
     * @param visible 是否可见
     */
    fun setExportAccountDialogVisible(visible: Boolean) {
        _isExportAccountDialogVisible.value = visible
    }

    /**
     * 设置手机号、验证码输入框
     * @param inputStr 输入框内容
     */
    fun setInputStr(inputStr: String) {
        _inputStr.value = inputStr
    }

    /**
     * 设置批量登录
     * @param isMultiThreadLogin 是否批量登录
     */
    fun setIsMultiThreadLogin(isMultiThreadLogin: Boolean) {
        _isMultiThreadLogin.value = isMultiThreadLogin
    }

    /**
     * 设置多线程登录对话框可见性
     * @param visible 是否可见
     */
    fun setMultiThreadLoginDialogVisible(visible: Boolean) {
        _isMultiThreadLoginDialogVisible.value = visible
    }

    /**
     * 设置剩余短信数量
     * @param remainingMessages 剩余短信数量
     */
    fun setRemainingMessages(remainingMessages: Int) {
        _remainingMessages.value = remainingMessages
    }

    /**
     * 设置自动注册状态
     * @param isAutoRegistering 是否自动注册
     */
    fun setAutoRegistering(isAutoRegistering: Boolean) {
        _isAutoRegistering.value = isAutoRegistering
    }

    /**
     * 设置查询状态
     * @param isQuerying 是否查询
     */
    fun setIsQuerying(isQuerying: Boolean) {
        _isQuerying.value = isQuerying
    }

    /**
     * 设置查询确认对话框可见性
     * @param visible 是否可见
     */
    fun setQueryConfirmDialogVisible(visible: Boolean) {
        _isQueryConfirmDialogVisible.value = visible
    }

    /**
     * 确认导出账号
     * @param filterOption 筛选选项
     */
    fun onExportAccountsConfirm(filterOption: ExportFilterOptionForLoginToken) {
        _exportFilterOption.value = filterOption
        setExportAccountDialogVisible(false)
    }

    /**
     * 设置悬浮窗可见性
     * @param visible 是否可见
     */
    fun setFloatingWindowShown(visible: Boolean) {
        _isFloatingWindowShown.value = visible
    }

    /**
     * 添加登录令牌
     * @param loginToken 登录令牌实体
     */
    fun addLoginToken(loginToken: LoginTokenEntity) {
        viewModelScope.launch {
            try {
                tokenRepository.insertToken(loginToken)
                // 刷新令牌列表
                loadLoginTokens()
            } catch (e: Exception) {
                Log.e("LoginViewModel", "Failed to add login token", e)
            }
        }
    }

    /**
     * 删除登录令牌
     * @param loginToken 登录令牌实体
     */
    fun deleteLoginToken(loginToken: LoginTokenEntity) {
        viewModelScope.launch {
            try {
                tokenRepository.deleteLoginToken(loginToken)
                // 刷新令牌列表
                loadLoginTokens()
            } catch (e: Exception) {
                Log.e("LoginViewModel", "Failed to delete login token", e)
            }
        }
    }

    /**
     * 删除手机号和密码
     * @param phoneWithPassword 手机号和密码实体
     */
    fun deletePhoneWithPassword(phoneWithPassword: PhoneWithPasswordEntity) {
        viewModelScope.launch {
            try {
                phoneWithPasswordRepository.deletePhoneWithPassword(phoneWithPassword)
                // 刷新列表
                loadPhoneWithPasswords()
                showToast("已删除: ${phoneWithPassword.phoneNumber}")
            } catch (e: Exception) {
                Log.e("LoginViewModel", "Failed to delete phone with password", e)
                showToast("删除错误: ${e.message}")
            }
        }
    }

    /**
     * 清空所有登录令牌
     */
    fun clearAllLoginTokens() {
        viewModelScope.launch {
            try {
                tokenRepository.deleteAllLoginTokens()
                // 刷新令牌列表
                loadLoginTokens()
            } catch (e: Exception) {
                Log.e("LoginViewModel", "Failed to clear all login tokens", e)
            }
        }
    }

    /**
     * 复制Token信息到剪贴板
     */
    fun copyTokenToClipboard(loginToken: LoginTokenEntity) {
        val clipboardManager =
            getApplication<Application>().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        if (!loginToken.isLogin || loginToken.isTimeOut) {
            val content =
                "${loginToken.phoneNumber}----${loginToken.smsCode}----${loginToken.appParam}"
            ClipboardUtils.copyTextToClipboard(clipboardManager, content)
            showToast("已复制到剪贴板")
            return
        }
        ClipboardUtils.copyToken(clipboardManager, loginToken)
        showToast("已复制到剪贴板")
    }

    /**
     * 清空所有手机号和密码
     */
    fun clearAllPhoneWithPasswords() {
        viewModelScope.launch {
            try {
                phoneWithPasswordRepository.deleteAllPhoneWithPasswords()
                // 刷新列表
                loadPhoneWithPasswords()
                showToast("已清空所有手机号密码")
            } catch (e: Exception) {
                e.printStackTrace()
                showToast("清空错误: ${e.message}")
            }
        }
    }

    /**
     * 显示Toast消息
     * @param message 消息内容
     */
    private fun showToast(message: String) {
        viewModelScope.launch(Dispatchers.Main) {
            Toast.makeText(getApplication(), message, Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 从URI导入账号
     * @param uri 导入文件的URI
     */
    fun importAccountsFromUri(uri: Uri) {
        _accountImporting.value = true
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    // 读取文件内容
                    val content = readContentFromUri(uri) ?: return@withContext

                    // 检查是否为手机号密码格式
                    if (isPhonePasswordFormat(content)) {
                        importPhoneWithPasswords(content)
                        return@withContext
                    }

                    // 使用TokenParserUtil解析账号
                    importLoginTokens(content)
                }
            } catch (e: Exception) {
                Log.e("LoginViewModel", "Import accounts failed", e)
                showToast("导入错误: ${e.message}")
            } finally {
                _accountImporting.value = false
            }
        }
    }

    /**
     * 从URI读取内容
     * @param uri 文件URI
     * @return 文件内容字符串，错误返回null
     */
    private fun readContentFromUri(uri: Uri): String? {
        return try {
            appContext.contentResolver.openInputStream(uri)?.use { inputStream ->
                inputStream.bufferedReader().use { it.readText() }
            }
        } catch (e: Exception) {
            Log.e("LoginViewModel", "Failed to read content from URI", e)
            showToast("读取文件错误: ${e.message}")
            null
        }
    }

    /**
     * 检查内容是否为手机号密码格式
     * @param content 文件内容
     * @return 是否为手机号密码格式
     */
    private fun isPhonePasswordFormat(content: String): Boolean {
        val firstLine = content.split("\n").firstOrNull() ?: return false
        return firstLine.split("----").size == 2
    }

    /**
     * 导入手机号密码
     * @param content 文件内容
     */
    private suspend fun importPhoneWithPasswords(content: String) {
        val lines = content.split("\n").filter { it.isNotBlank() }
        val results = lines.map { line ->
            try {
                val parts = line.split("----")
                if (parts.size < 2) return@map ImportResult.Failure

                val phoneNumber = parts[0].trim()
                val password = parts[1].trim()

                if (phoneNumber.isBlank() || password.isBlank()) {
                    return@map ImportResult.Failure
                }

                val result = phoneWithPasswordRepository.insertOrUpdatePhoneWithPassword(
                    phoneNumber = phoneNumber,
                    password = password
                )

                if (result.first) ImportResult.Success else ImportResult.Updated
            } catch (e: Exception) {
                Log.e("LoginViewModel", "Error importing phone with password", e)
                ImportResult.Failure
            }
        }

        showImportResultToast(results)
    }

    /**
     * 导入登录令牌
     * @param content 文件内容
     */
    private suspend fun importLoginTokens(content: String) {
        val parseResult = TokenParserUtils.parseTokensFromContent(content, "login")

        if (parseResult.tokens.isEmpty()) {
            showToast("非合法数据")
            return
        }

        val results = parseResult.tokens.map { token ->
            if (token is LoginTokenEntity) {
                try {
                    val result = tokenRepository.insertToken(token)
                    if (result.first) ImportResult.Success else ImportResult.Updated
                } catch (e: Exception) {
                    Log.e("LoginViewModel", "Error importing token", e)
                    ImportResult.Failure
                }
            } else {
                ImportResult.Failure
            }
        }

        showImportResultToast(results)
    }

    /**
     * 导入结果枚举
     */
    private enum class ImportResult {
        Success, Updated, Failure
    }

    /**
     * 显示导入结果的Toast
     * @param results 导入结果列表
     */
    private fun showImportResultToast(results: List<ImportResult>) {
        val successCount = results.count { it == ImportResult.Success }
        val updateCount = results.count { it == ImportResult.Updated }
        val failCount = results.count { it == ImportResult.Failure }

        if (successCount > 0 || updateCount > 0) {
            showToast(
                "成功导入${successCount}个账号，更新${updateCount}个" +
                        (if (failCount > 0) "，失败${failCount}个" else "")
            )
        } else {
            showToast("导入失败，请检查文件格式")
        }
    }

    /**
     * 导出账号到指定URI，账号密码没有不能导出，只能导出账号
     * @param uri 导出文件的URI
     */
    fun exportAccountsToUri(uri: Uri) {
        _accountExporting.value = true
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    val tokenDao = AppDatabase.getDatabase(appContext).loginTokenDao()
                    val allTokens = tokenDao.getAllLoginTokens().first()

                    if (allTokens.isEmpty()) {
                        showToast("数据库数据为空")
                        return@withContext
                    }

                    // 筛选已登录的账号
                    val loggedInTokens = allTokens.filter { it.isLogin }

                    if (loggedInTokens.isEmpty()) {
                        showToast("已登录账号列表为空")
                        return@withContext
                    }

                    val tokensToExport = when (_exportFilterOption.value) {
                        ExportFilterOptionForLoginToken.ALL -> loggedInTokens
                        ExportFilterOptionForLoginToken.NON_NEWCOMER -> loggedInTokens.filter { "新人特权" != it.extraNote }
                        ExportFilterOptionForLoginToken.NEWCOMER_PRIVILEGE -> loggedInTokens.filter { "新人特权" == it.extraNote }
                    }

                    val exportContent = tokensToExport.joinToString("\n") { token ->
                        "${token.phoneNumber}----${token.uid}----${token.userKey}-601933-${token.accessToken}----${token.refreshToken}----${token.appParam},${token.expiresIn}----${token.updateDate} ${token.extraNote}"
                    }

                    // 写入到用户选择的URI
                    appContext.contentResolver.openOutputStream(uri)?.use { outputStream ->
                        outputStream.write(exportContent.toByteArray())
                    }

                    showToast("成功导出${tokensToExport.size}个账号")
                }
            } catch (e: Exception) {
                e.printStackTrace()
                showToast("导出失败: ${e.message}")
            } finally {
                _accountExporting.value = false
            }
        }
    }

    /**
     * ViewModel工厂类，用于创建LoginViewModel实例
     */
    class Factory(
        private val tokenRepository: TokenRepository,
        private val phoneWithPasswordRepository: PhoneWithPasswordRepository,
        private val application: Application
    ) : ViewModelProvider.Factory {
        @Suppress("UNCHECKED_CAST")
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(LoginViewModel::class.java)) {
                return LoginViewModel(
                    tokenRepository,
                    phoneWithPasswordRepository,
                    application
                ) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }

    fun loginWithPassword(phone: String, password: String, loginIndex: Int) {
        viewModelScope.launch {
            try {
                // 检查服务
                if (!SignUtils.checkServer()) {
                    showToast("服务未初始化")
                    return@launch
                }
                // 添加到正在登录的手机号集合
                _loggingInPhoneNumbersForPassword.value =
                    _loggingInPhoneNumbersForPassword.value + phone
                val result = PasswordLoginUtils.loginWithPassword(phone, password, loginIndex, true)
                when (result) {
                    is RequestResult.Success -> {
                        addLoginToken(result.data)
                        showToast("同步登录成功")
                        phoneWithPasswordRepository.updateIsLogin(phone, true)
                        Log.d("LoginViewModel", "Login successful: ${result.data}")
                    }

                    is RequestResult.Error -> {
                        showToast("同步登录错误: ${result.error.message}")
                        Log.e("LoginViewModel", "Login failed: ${result.error}")
                    }
                }
            } finally {
                // 从正在登录的手机号集合中移除
                _loggingInPhoneNumbersForPassword.value =
                    _loggingInPhoneNumbersForPassword.value - phone
            }
        }
    }

    /**
     * 进行并行的批量登录。
     * 通过为每个登录任务创建独立的子协程来实现并行处理，从而提高登录效率。
     */
    fun doMultiThreadLogin() {
        viewModelScope.launch {
            // 使用 .value 获取 StateFlow 的当前列表值，仅处理当前账号，避免重复执行
            phoneWithPasswords.value.forEachIndexed { index, phoneWithPassword ->
                // 如果账号已经登录或者批量登录停止，则跳过本次循环
                if (phoneWithPassword.isLogin || !_isMultiThreadLogin.value) return@forEachIndexed

                // 为每个登录操作启动一个新的、独立的子协程
                // 这使得多个登录请求可以并行执行，而不会相互等待。
                launch {
                    loginWithPassword(
                        phoneWithPassword.phoneNumber,
                        phoneWithPassword.password,
                        index
                    )
                }
            }
            _isMultiThreadLogin.value = false
        }
    }

    /**
     * 从服务器获取短信验证码
     */
    fun getSmsCodeFromServer() {
        viewModelScope.launch {
            val smsCode = SmsLoginUtils.getSmsCodeFromServer()
            val parts = smsCode.split("----")
            Log.d("LoginViewModel", "getSmsCodeFromServer: $smsCode")
            if (parts.size == 4) {
                _inputStr.value = parts[0] + "----" + parts[1] + "----" + parts[3]
                _remainingMessages.value = parts[2].toInt()
                showToast("验证码获取成功")
            } else {
                showToast("未获取到验证码")
            }
        }
    }

    fun loginWithSmsCode(smsCode: String = "") {
        viewModelScope.launch {
            var phone = ""
            try {
                // 检查服务
                if (!SignUtils.checkServer()) {
                    showToast("服务未初始化")
                    return@launch
                }
                val smsCode = smsCode.ifEmpty { _inputStr.value }
                if (smsCode.isEmpty()) {
                    showToast("输入参数为空")
                    return@launch
                }
                if (smsCode.split("----").size < 2 || smsCode.split("----").size > 3) {
                    showToast("输入参数格式错误")
                    return@launch
                }
                val parts = smsCode.split("----")
                if (parts.size >= 2) {
                    phone = parts[0]
                    val code = parts[1]
                    val param = if (parts.size > 2) parts[2] else ""
                    // 格式检查
                    if (phone.length != 11 || code.length != 6) {
                        showToast("手机号或验证码格式错误")
                        return@launch
                    }
                    val tempLoginToken =
                        SmsLoginUtils.createEmptyLoginTokenEntity(phone, code, param)
                    // 添加到数据库，以便显示登陆动画
                    addLoginToken(tempLoginToken.first)
                    _loggingInPhoneNumbersForSms.value = _loggingInPhoneNumbersForSms.value + phone
                    val result = SmsLoginUtils.loginWithSmsCode(
                        phone,
                        code,
                        param,
                        tempLoginToken.second,
                        useProxy = true
                    )
                    when (result) {
                        is RequestResult.Success -> {
                            // 先删除临时记录
                            deleteLoginToken(tempLoginToken.first)
                            // 再添加新的登录记录
                            addLoginToken(result.data)
                            Log.d("LoginViewModel", "Login successful: ${result.data}")
                        }

                        is RequestResult.Error -> {
                            result.error.message?.let {
                                if (it.contains("验证码错误或已失效")) {
                                    tokenRepository.updateLoginToken(
                                        tempLoginToken.first.copy(
                                            isTimeOut = true
                                        )
                                    )
                                }
                            }
                            showToast("验证码登录错误: ${result.error.message}")
                            Log.e("LoginViewModel", "Login failed: ${result.error}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("LoginViewModel", "Login failed: ${e.message}")
            } finally {
                if (phone.isNotEmpty()) {
                    _loggingInPhoneNumbersForSms.value = _loggingInPhoneNumbersForSms.value - phone
                }
            }
        }
    }

    fun loginWithSmsCode(loginTokenWithSmsCode: LoginTokenEntity) {
        viewModelScope.launch {
            try {
                // 检查服务
                if (!SignUtils.checkServer()) {
                    showToast("服务未初始化")
                    return@launch
                }
                _loggingInPhoneNumbersForSms.value =
                    _loggingInPhoneNumbersForSms.value + loginTokenWithSmsCode.phoneNumber
                val (jysessionid, distinctId, deviceInfo) = DeviceInfoProvider.praseAppParam(
                    loginTokenWithSmsCode.appParam
                )
                val result = SmsLoginUtils.loginWithSmsCode(
                    loginTokenWithSmsCode.phoneNumber,
                    loginTokenWithSmsCode.smsCode,
                    "$jysessionid,$distinctId,${deviceInfo.deviceId}",
                    deviceInfo,
                    useProxy = true
                )
                when (result) {
                    is RequestResult.Success -> {
                        // 先删除临时记录
                        deleteLoginToken(loginTokenWithSmsCode)
                        // 再添加新的登录记录
                        addLoginToken(result.data)
                        Log.d("LoginViewModel", "Login successful: ${result.data}")
                    }

                    is RequestResult.Error -> {
                        result.error.message?.let {
                            if (it.contains("验证码错误或已失效")) {
                                tokenRepository.updateLoginToken(
                                    loginTokenWithSmsCode.copy(
                                        isTimeOut = true
                                    )
                                )
                            }
                        }
                        showToast("验证码登录错误: ${result.error.message}")
                        Log.e("LoginViewModel", "Login failed: ${result.error}")
                    }
                }
            } catch (e: Exception) {
                Log.e("LoginViewModel", "Login failed: ${e.message}")
            } finally {
                _loggingInPhoneNumbersForSms.value =
                    _loggingInPhoneNumbersForSms.value - loginTokenWithSmsCode.phoneNumber
            }
        }
    }

    /**
     * 开始自动注册流程
     * 每5秒从服务器获取一次验证码并尝试注册
     */
    fun startAutoRegistration() {
        if (_isAutoRegistering.value) return

        viewModelScope.launch {
            try {
                if (!SignUtils.checkServer()) {
                    showToast("服务未初始化")
                    return@launch
                }

                _isAutoRegistering.value = true

                while (_isAutoRegistering.value) {
                    val delayTime = if (remainingMessages.value > 5) 5000L else 10000L
                    try {
                        // 检查是否有不在登录中、isTimeOut = false、isLogin = false的账号
                        val notLoggingInTokens = _loginTokens.value.filter {
                            !it.isLogin && !it.isTimeOut && !loggingInPhoneNumbersForSms.value.contains(
                                it.phoneNumber
                            )
                        }
                        if (notLoggingInTokens.isNotEmpty()) {
                            notLoggingInTokens.forEach {
                                loginWithSmsCode(it)
                            }
                        }

                        Log.d("LoginViewModel", "Attempting to get SMS code for auto registration")
                        val smsCode = SmsLoginUtils.getSmsCodeFromServer()
                        if (smsCode.isEmpty()) {
                            //showToast("自动注册后台运行中")
                            Log.d(
                                "LoginViewModel",
                                "Remaining messages: ${remainingMessages.value}"
                            )
                            // 根据剩余短信数量决定等待时间
                            delay(delayTime)
                            continue
                        }

                        showToast("验证码获取成功")

                        val parts = smsCode.split("----")

                        if (parts.size == 4) {
                            val phone = parts[0]
                            val code = parts[1]
                            val remainingCount = parts[2].toInt()
                            val param = parts[3]
                            val smsCodeData = "$phone----$code----$param"

                            _remainingMessages.value = remainingCount
                            _inputStr.value = smsCodeData

                            Log.d("LoginViewModel", "Auto registration: Got SMS code for $phone")
                            // 检查是否在loggingInPhoneNumbersForSms.value/loginTokens中
                            if (loggingInPhoneNumbersForSms.value.contains(phone) || loginTokens.value.any { it.phoneNumber == phone }) {
                                Log.d("LoginViewModel", "Auto registration: $phone is already logging in")
                                continue
                            }
                            // 尝试使用获取到的验证码进行登录
                            loginWithSmsCode(smsCodeData)
                        } else {
                            Log.e("LoginViewModel", "Invalid SMS code format: $smsCode")
                            showToast("验证码格式错误")
                        }
                    } catch (e: Exception) {
                        Log.e("LoginViewModel", "Error during auto registration", e)
                        showToast("自动注册出错: ${e.message}")
                    }

                    delay(delayTime)
                }
            } finally {
                _isAutoRegistering.value = false
            }
        }
    }

    /**
     * 停止自动注册流程
     */
    fun stopAutoRegistration() {
        _isAutoRegistering.value = false
        showToast("已停止自动注册")
    }

    /**
     * 生成当前登录令牌列表信息
     * @return 格式为"总数,已登录数量,正在登录数量,超时数量"的字符串
     */
    fun getLoginTokensInfo(): String {
        val tokens = _loginTokens.value
        val total = tokens.size
        val loggingInPhoneNumbersForSmsCount = _loggingInPhoneNumbersForSms.value.size
        val isLogin = tokens.count { it.isLogin }
        val timedOut = tokens.count { it.isTimeOut }

        val info = "$total,$isLogin,$loggingInPhoneNumbersForSmsCount,$timedOut"

        // 更新后台服务中的注册信息
        if (!isQuerying.value) {
            BackgroundService.updateRegistrationInfo(info, "login")
        }

        return info
    }

    /**
     * 切换悬浮窗
     */
    fun toggleFloatingWindow(show: Boolean) {
        BackgroundService.toggleFloatingWindow(appContext, show)
    }

    /**
     * 查询账户信息
     * 使用多线程和代理查询所有已登录账号的信息
     */
    fun queryAllAccountInfo() {
        viewModelScope.launch {
            try {
                if (!SignUtils.checkServer()) {
                    showToast("服务未初始化")
                    return@launch
                }

                // 设置查询状态为true
                _isQuerying.value = true

                val loggedInTokens = _loginTokens.value.filter { it.isLogin && !it.isTimeOut }

                if (loggedInTokens.isEmpty()) {
                    showToast("可查询账号为空")
                    return@launch
                }

                Log.d("LoginViewModel", "Querying ${loggedInTokens.size} accounts")

                var successCount = 0
                var failCount = 0
                val totalCount = loggedInTokens.size

                loggedInTokens.forEach { token ->
                    // 停止查询
                    if (!isQuerying.value) {
                        Log.d("LoginViewModel", "Query stopped")
                        return@launch
                    }

                    launch(Dispatchers.IO) {
                        val result = queryAccountInfo(token)
                        if (result) {
                            Log.d("LoginViewModel", "Query successful for ${token.phoneNumber}")
                            successCount++
                        } else {
                            Log.e("LoginViewModel", "Query failed for ${token.phoneNumber}")
                            failCount++
                        }

                        BackgroundService.updateRegistrationInfo(
                            "$totalCount,$successCount,$failCount,",
                            "query"
                        )
                    }

                    delay(10000)
                }

                Log.d("LoginViewModel", "Query completed. Success: $successCount, Fail: $failCount")
            } catch (e: Exception) {
                Log.e("LoginViewModel", "Error during query", e)
                showToast("查询过程中出错: ${e.message}")
            } finally {
                // 查询结束后，如果还在查询状态，则重置为false
                if (_isQuerying.value) {
                    _isQuerying.value = false
                }
            }
        }
    }

    private suspend fun queryAccountInfo(token: LoginTokenEntity): Boolean {
        var requestService: RequestService? = null
        try {
            // 创建RequestService实例
            requestService = RequestService.create(token, "app")

            // 配置代理
            try {
                val proxyConfig = HttpProxyUtils.getProxyIp("pinzan")
                if (proxyConfig.proxyInfo.first != null && proxyConfig.proxyInfo.second != null) {
                    requestService.setupProxy(proxyConfig)
                    Log.d(
                        "LoginViewModel",
                        "Proxy setup successful for ${token.phoneNumber}: ${proxyConfig.proxyInfo.first}:${proxyConfig.proxyInfo.second}"
                    )
                } else {
                    Log.e(
                        "LoginViewModel",
                        "Invalid proxy configuration for ${token.phoneNumber}"
                    )
                }
            } catch (e: Exception) {
                Log.e(
                    "LoginViewModel",
                    "Failed to setup proxy for ${token.phoneNumber}: ${e.message}"
                )
                return false
            }

            // 用于存储额外信息的变量
            val extraNoteBuilder = StringBuilder()
            val originalNote = token.extraNote

            // 查询用户类型
            if (!originalNote.contains("新人特权") && !originalNote.contains("非新人") && !originalNote.contains(
                    "领居都在买"
                )
            ) {
                try {
                    val accountTypeResult = requestService.user.checkAccountType()
                    when (accountTypeResult) {
                        is RequestResult.Success -> {
                            val homepageResponse =
                                ResponseParserUtils.parseHomepageResponse(
                                    accountTypeResult.data
                                )
                            if (homepageResponse != null && homepageResponse.code == 0) {
                                // 检查是否需要登录
                                val needLogin =
                                    homepageResponse.data?.loginPopVO?.newPersonLogin == "200"

                                if (!needLogin) {
                                    // 确定账户类型
                                    val accountType =
                                        determineAccountType(homepageResponse)

                                    // 添加账户类型标记
                                    extraNoteBuilder.append(accountType)

                                    Log.d(
                                        "LoginViewModel",
                                        "Account type determined for ${token.phoneNumber}: $accountType"
                                    )
                                } else {
                                    Log.e(
                                        "LoginViewModel",
                                        "Account needs login for ${token.phoneNumber}"
                                    )
                                }
                            }
                        }

                        is RequestResult.Error -> {
                            Log.e(
                                "LoginViewModel",
                                "Failed to check account type for ${token.phoneNumber}: ${accountTypeResult.error.message}"
                            )
                        }
                    }
                } catch (e: Exception) {
                    Log.e(
                        "LoginViewModel",
                        "Error checking account type for ${token.phoneNumber}",
                        e
                    )
                    return false
                }
            }

            // 查询地址信息
            if (!originalNote.contains("地址")) {
                try {
                    val addressResult = requestService.address.getAllAddress()
                    when (addressResult) {
                        is RequestResult.Success -> {
                            val addressData = addressResult.data
                            val addressResponse =
                                ResponseParserUtils.parseAddressResponse(addressData)
                            if (addressResponse != null && addressResponse.code == 0) {
                                val addressList = addressResponse.data?.list
                                if (!addressList.isNullOrEmpty()) {
                                    // 地址不为空，添加标记
                                    if (extraNoteBuilder.isNotEmpty()) {
                                        extraNoteBuilder.append("/地址")
                                    } else {
                                        extraNoteBuilder.append("地址")
                                    }
                                    Log.d(
                                        "LoginViewModel",
                                        "Address found for ${token.phoneNumber}: ${addressList.size} addresses"
                                    )
                                }
                            }
                        }

                        is RequestResult.Error -> {
                            Log.e(
                                "LoginViewModel",
                                "Failed to get address for ${token.phoneNumber}: ${addressResult.error.message}"
                            )
                        }
                    }
                } catch (e: Exception) {
                    Log.e(
                        "LoginViewModel",
                        "Error getting address for ${token.phoneNumber}",
                        e
                    )
                    return false
                }
            }

            // 设置必要参数
            val xyhBizParams = RequestConfig.XYH_BIZ_PARAMS_ORIGIN_APP
            requestService.setXyhBizParams(xyhBizParams)
            val webXyhBizParams =
                matchAndAssignParamsByXYHBizParamsCommon(
                    xyhBizParams
                )
            requestService.setWebXyhBizParams(
                webXyhBizParams
            )
            requestService.setCityId(RequestConfig.CheckAccountType.CITYID)
            requestService.setShopId(RequestConfig.CheckAccountType.SHOPID)
            requestService.setSellerId(RequestConfig.CheckAccountType.SELLERID)
            requestService.setCityId(RequestConfig.CheckAccountType.CITYID)

            // 查询积分信息
            if (!originalNote.contains("积分")) {
                try {
                    val creditResult = requestService.credit.creditDetail(0)
                    when (creditResult) {
                        is RequestResult.Success -> {
                            val creditResponse =
                                ResponseParserUtils.parseCreditResponse(creditResult.data)
                            if (creditResponse != null && creditResponse.code == 0 && creditResponse.data != null) {
                                val creditData = creditResponse.data
                                // 积分存在，添加标记
                                if (extraNoteBuilder.isNotEmpty()) {
                                    extraNoteBuilder.append("/积分:${creditData.credit}")
                                } else {
                                    extraNoteBuilder.append("积分:${creditData.credit}")
                                }
                                Log.d(
                                    "LoginViewModel",
                                    "Credit found for ${token.phoneNumber}: ${creditData.credit}"
                                )
                            }
                        }

                        is RequestResult.Error -> {
                            Log.e(
                                "LoginViewModel",
                                "Failed to get credit for ${token.phoneNumber}: ${creditResult.error.message}"
                            )
                        }
                    }
                } catch (e: Exception) {
                    Log.e(
                        "LoginViewModel",
                        "Error getting credit for ${token.phoneNumber}",
                        e
                    )
                    return false
                }
            }

            // 查询优惠券列表
            if (!originalNote.contains("优惠券")) {
                try {
                    // 存储所有优惠券数据
                    val allCoupons =
                        mutableListOf<CouponListCoupon>()

                    // 1. 首先获取红包券
                    val redEnvelopeResult =
                        requestService.coupon.getCouponList("redEnvelope.coupon")
                    when (redEnvelopeResult) {
                        is RequestResult.Success -> {
                            val redEnvelopeCouponResponse =
                                ResponseParserUtils.parseCouponResponse(
                                    redEnvelopeResult.data
                                )
                            if (redEnvelopeCouponResponse != null && redEnvelopeCouponResponse.code == 0) {
                                // 提取红包券数据
                                val availableCoupons =
                                    redEnvelopeCouponResponse.data?.availablecoupons?.coupons
                                        ?: emptyList()
                                val unavailableCoupons =
                                    redEnvelopeCouponResponse.data?.unavailablecoupons?.coupons
                                        ?: emptyList()
                                allCoupons.addAll(availableCoupons)
                                allCoupons.addAll(unavailableCoupons)
                                Log.d(
                                    "LoginViewModel",
                                    "Red envelope coupons found for ${token.phoneNumber}: ${availableCoupons.size}"
                                )
                            }
                        }

                        is RequestResult.Error -> {
                            Log.e(
                                "LoginViewModel",
                                "Failed to get red envelope coupons for ${token.phoneNumber}: ${redEnvelopeResult.error.message}"
                            )
                        }
                    }

                    // 2. 然后获取通用优惠券
                    val generalResult =
                        requestService.coupon.getCouponList("coupon.general")
                    when (generalResult) {
                        is RequestResult.Success -> {
                            val generalCouponResponse =
                                ResponseParserUtils.parseCouponResponse(
                                    generalResult.data
                                )
                            if (generalCouponResponse != null && generalCouponResponse.code == 0) {
                                // 提取通用券数据
                                val availableCoupons =
                                    generalCouponResponse.data?.availablecoupons?.coupons
                                        ?: emptyList()
                                val unavailableCoupons =
                                    generalCouponResponse.data?.unavailablecoupons?.coupons
                                        ?: emptyList()
                                allCoupons.addAll(availableCoupons)
                                allCoupons.addAll(unavailableCoupons)
                                Log.d(
                                    "LoginViewModel",
                                    "General coupons found for ${token.phoneNumber}: ${availableCoupons.size}"
                                )
                            }
                        }

                        is RequestResult.Error -> {
                            Log.e(
                                "LoginViewModel",
                                "Failed to get general coupons for ${token.phoneNumber}: ${generalResult.error.message}"
                            )
                        }
                    }

                    // 如果有可用优惠券，添加标记
                    if (allCoupons.isNotEmpty()) {
                        if (extraNoteBuilder.isNotEmpty()) {
                            extraNoteBuilder.append("/优惠券")
                        } else {
                            extraNoteBuilder.append("优惠券")
                        }
                        Log.d(
                            "LoginViewModel",
                            "Total coupons found for ${token.phoneNumber}: ${allCoupons.size}"
                        )
                    }
                } catch (e: Exception) {
                    Log.e(
                        "LoginViewModel",
                        "Error getting coupons for ${token.phoneNumber}",
                        e
                    )
                    return false
                }
            }

            // 更新账号备注
            if (extraNoteBuilder.isNotEmpty()) {
                val newNote = if (originalNote.isNotEmpty()) {
                    "$originalNote/${extraNoteBuilder}"
                } else {
                    extraNoteBuilder.toString()
                }

                tokenRepository.updateLoginToken(token.copy(extraNote = newNote))
                return true
            }

            return false
        } catch (e: Exception) {
            // 处理异常
            Log.e(
                "LoginViewModel",
                "Error querying account info for ${token.phoneNumber}",
                e
            )
            return false
        } finally {
            // 释放资源
            requestService?.stop()
        }
    }
}


