package dev.pigmomo.yhkit2025.viewmodel

import android.annotation.SuppressLint
import android.app.Application
import android.content.ClipboardManager
import android.content.Context
import android.util.Log
import android.widget.Toast
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import dev.pigmomo.yhkit2025.hooks.config.XPrefsWriter
import dev.pigmomo.yhkit2025.data.model.ConfigTokenEntity
import dev.pigmomo.yhkit2025.data.repository.TokenRepository
import dev.pigmomo.yhkit2025.utils.ClipboardUtils
import dev.pigmomo.yhkit2025.utils.DeviceInfoUtils
import dev.pigmomo.yhkit2025.utils.TokenParserUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class MainViewModel(
    private val tokenRepository: TokenRepository,
    application: Application
) : AndroidViewModel(application) {

    // 启动OrderActivity的回调函数
    var startOrderActivityCallback: (() -> Unit)? = null

    // 启动LogViewActivity的回调函数
    var startLogViewActivityCallback: (() -> Unit)? = null

    // 启动LoginActivity的回调函数
    var startLoginActivityCallback: (() -> Unit)? = null

    @SuppressLint("StaticFieldLeak")
    val context: Context = getApplication()

    // 输入参数状态
    private val _inputParams = mutableStateOf("")
    val inputParams: State<String> = _inputParams

    // 配置记录列表状态
    private val _configTokens = MutableStateFlow<List<ConfigTokenEntity>>(emptyList())
    val configTokens: StateFlow<List<ConfigTokenEntity>> = _configTokens.asStateFlow()

    // 设备信息状态
    private val _deviceInfo = mutableStateOf<String>("")
    val deviceInfo: State<String> = _deviceInfo

    // 被点击的配置项UID
    private val _clickedTokenUid = mutableStateOf<String?>(null)
    val clickedTokenUid: State<String?> = _clickedTokenUid

    // 被长按的配置项UID
    private val _longClickedTokenUid = mutableStateOf<String?>(null)
    val longClickedTokenUid: State<String?> = _longClickedTokenUid

    // 控制TokenDetailDialog显示状态
    private val _isTokenDetailDialogVisible = mutableStateOf(false)
    val isTokenDetailDialogVisible: State<Boolean> = _isTokenDetailDialogVisible

    // SharedPreferences常量
    companion object {
        const val PREF_NAME = "yhkit_config"

        const val KEY_UID = "uid"
        const val KEY_PHONE_NUMBER = "phone_number"
        const val KEY_USER_KEY = "user_key"
        const val KEY_ACCESS_TOKEN = "access_token"
        const val KEY_REFRESH_TOKEN = "refresh_token"
        const val KEY_EXPIRES_IN = "expires_in"
        const val KEY_APP_PARAM = "app_param"
    }

    init {
        loadConfigTokens()
        fetchDeviceInfo()
    }

    /**
     * 加载所有配置记录
     */
    private fun loadConfigTokens() {
        viewModelScope.launch {
            tokenRepository.getAllConfigTokensOrderByUpdateDate().collect { tokens ->
                _configTokens.value = tokens
            }
        }
    }

    /**
     * 更新输入参数
     */
    fun updateInputParams(params: String) {
        _inputParams.value = params
    }

    /**
     * 设置被长按的Token UID并显示详情对话框
     */
    fun setLongClickedTokenUid(uid: String?) {
        _longClickedTokenUid.value = uid
    }

    /**
     * 显示或隐藏Token详情对话框
     */
    fun setTokenDetailDialogVisible(visible: Boolean) {
        _isTokenDetailDialogVisible.value = visible
    }

    /**
     * 清除长按标记但保持对话框状态
     */
    fun clearLongClickedToken() {
        _longClickedTokenUid.value = null
    }

    /**
     * 将ConfigTokenEntity保存到SharedPreferences
     */
    @SuppressLint("WorldReadableFiles")
    internal fun saveConfigTokenToPrefs(configToken: ConfigTokenEntity) {
        try {
            _clickedTokenUid.value = configToken.uid

            // 使用XPrefsWriter写入配置
            XPrefsWriter.writeConfig(
                context,
                configToken
            )
        } catch (e: Exception) {
            Log.e("MainViewModel", "error saving to XSharedPreferences", e)
        }
    }

    /**
     * 删除配置记录
     */
    fun deleteConfigRecord(configToken: ConfigTokenEntity) {
        viewModelScope.launch {
            tokenRepository.deleteConfigToken(configToken)
            loadConfigTokens()

            // 同时从SharedPreferences中删除
            deleteConfigTokenFromPrefs(configToken.uid)

            // 清除长按状态
            _longClickedTokenUid.value = null
            _isTokenDetailDialogVisible.value = false

            Toast.makeText(context, "已删除配置", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 从SharedPreferences中删除指定uid的配置
     */
    private fun deleteConfigTokenFromPrefs(uid: String) {
        try {
            // 使用XPrefsWriter删除配置
            val success = XPrefsWriter.deleteConfig(
                context,
                uid
            )

            if (success) {
                _clickedTokenUid.value = null
                Log.d("MainViewModel", "ConfigToken removed from XSharedPreferences: $uid")
            }
        } catch (e: Exception) {
            Log.e("MainViewModel", "Error deleting from XSharedPreferences", e)
        }
    }

    /**
     * 从剪贴板粘贴
     */
    fun pasteFromClipboard() {
        val clipboardText = ClipboardUtils.getTextFromClipboard(getApplication())
        clipboardText?.let {
            if (clipboardText.contains("----") && clipboardText.split("----").size in 5..6) {
                _inputParams.value = it
            } else {
                Toast.makeText(context, "粘贴错误，请检查账号格式", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 复制Token信息到剪贴板
     */
    fun copyTokenToClipboard(configToken: ConfigTokenEntity) {
        val clipboardManager =
            getApplication<Application>().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        ClipboardUtils.copyToken(clipboardManager, configToken)
        Toast.makeText(context, "已复制到剪贴板", Toast.LENGTH_SHORT).show()
    }

    /**
     * 获取设备信息（IP和设备ID）
     */
    fun fetchDeviceInfo() {
        viewModelScope.launch {
            val info = DeviceInfoUtils.getDeviceInfo(getApplication())
            _deviceInfo.value = info
        }
    }

    /**
     * 执行主要操作（前进箭头）
     */
    fun saveAndSetConfigToken() {
        val currentInput = _inputParams.value.trim()
        if (currentInput.isNotEmpty()) {
            viewModelScope.launch {
                val token = TokenParserUtils.parseTokenFromLine(currentInput, "config")
                if (token != null) {
                    tokenRepository.insertToken(token)

                    // 同时保存到SharedPreferences
                    if (token is ConfigTokenEntity) {
                        _clickedTokenUid.value = token.uid
                        saveConfigTokenToPrefs(token)
                    }
                } else {
                    Log.e("MainViewModel", "parseTokenFromLine failed")
                    Toast.makeText(context, "未适配格式", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    /**
     * 清除点击状态
     */
    fun clearClickStates() {
        _clickedTokenUid.value = null
        _longClickedTokenUid.value = null
        _isTokenDetailDialogVisible.value = false
    }

    /**
     * 打开订单管理页面
     */
    fun openOrderActivity() {
        startOrderActivityCallback?.invoke()
    }

    /**
     * 打开日志查看页面
     */
    fun openLogViewActivity() {
        startLogViewActivityCallback?.invoke()
    }

    /**
     * 打开登录账号管理页面
     */
    fun openLoginActivity() {
        startLoginActivityCallback?.invoke()
    }

    /**
     * ViewModel工厂
     */
    class Factory(
        private val tokenRepository: TokenRepository,
        private val application: Application
    ) : ViewModelProvider.Factory {
        @Suppress("UNCHECKED_CAST")
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(MainViewModel::class.java)) {
                return MainViewModel(tokenRepository, application) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
}