package dev.pigmomo.yhkit2025

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.lifecycle.ViewModelProvider
import dev.pigmomo.yhkit2025.ui.screens.LogScreen
import dev.pigmomo.yhkit2025.ui.theme.Yhkit2025Theme
import dev.pigmomo.yhkit2025.viewmodel.LogViewModel

/**
 * 日志查看界面的Activity
 */
class LogViewActivity : ComponentActivity() {
    
    private lateinit var viewModel: LogViewModel
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化ViewModel
        viewModel = ViewModelProvider(this)[LogViewModel::class.java]
        
        setContent {
            Yhkit2025Theme(dynamicColor = false) {
                LogScreen(
                    viewModel = viewModel,
                    onNavigateBack = { finish() }
                )
            }
        }
    }
} 