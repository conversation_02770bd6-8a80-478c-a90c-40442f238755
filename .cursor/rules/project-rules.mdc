---
description: 永辉生活套件项目开发规范
globs: 
alwaysApply: true
---
# 永辉生活套件项目开发规范

## 项目概述

这是一个基于Xposed框架的Android应用，名为"永辉生活套件"，主要功能包括：
- 永辉生活APP的功能增强和自动化
- 用户账户管理和令牌处理
- 订单管理和购物车操作
- 优惠券和积分管理
- 地址管理和店铺选择
- 自动下单和抢购功能
- 数据分析和报表功能

## 技术栈

- **语言**: Kotlin 2.0.21+
- **UI框架**: Jetpack Compose (BOM: 2024.09.00+)
- **数据库**: Room 2.5.1+
- **网络请求**: OkHttp 4.12.0+ + Retrofit 2.9.0+
- **依赖注入**: Koin 3.5.0+
- **架构模式**: MVVM + Repository
- **Hook框架**: LSPosed/Xposed
- **服务器**: Ktor 2.3.7+ (用于Hook通信)
- **协程**: Kotlin Coroutines + Flow
- **图表**: MPAndroidChart
- **图片加载**: Coil 2.4.0+
- **JSON解析**: Kotlinx Serialization 1.6.0+

## 项目结构规范

### 包结构
```
dev.pigmomo.yhkit2025/
├── api/                    # API相关
│   ├── model/             # API数据模型
│   ├── service/           # API服务类
│   ├── encryption/        # 加密工具
│   └── utils/            # API工具类
├── data/                  # 数据层
│   ├── dao/              # 数据访问对象
│   ├── database/         # 数据库配置
│   ├── model/            # 数据实体
│   └── repository/       # 仓库模式实现
├── di/                    # 依赖注入
│   ├── modules/          # Koin模块
├── hooks/                 # Xposed Hook相关
│   ├── config/           # Hook配置
│   ├── handlers/         # Hook处理器
│   └── utils/            # Hook工具类
├── ui/                    # UI层
│   ├── components/       # 可复用组件
│   ├── dialog/           # 对话框组件
│   ├── screens/          # 屏幕组件
│   └── theme/            # 主题配置
├── utils/                 # 通用工具类
├── viewmodel/            # ViewModel层
└── worker/               # 后台工作任务
```

## 编码规范

### 1. 命名规范

#### 类命名
- **Activity**: 以`Activity`结尾，如`MainActivity`、`OrderActivity`
- **ViewModel**: 以`ViewModel`结尾，如`MainViewModel`、`OrderViewModel`
- **Service**: 以`Service`结尾，如`UserService`、`OrderService`
- **Repository**: 以`Repository`结尾，如`TokenRepository`
- **Entity**: 以`Entity`结尾，如`TokenEntity`、`LoginTokenEntity`
- **Dao**: 以`Dao`结尾，如`ConfigTokenDao`
- **Utils**: 以`Utils`结尾，如`ClipboardUtils`、`DeviceInfoUtils`
- **Dialog**: 以`Dialog`结尾，如`AccountDialog`、`OrderPlaceDialog`
- **Hook**: 以`Hook`结尾，如`OrderHook`、`LoginHook`
- **Worker**: 以`Worker`结尾，如`SyncWorker`、`NotificationWorker`

#### 文件命名
- Compose组件文件使用PascalCase
- 工具类文件使用PascalCase + Utils后缀
- 常量文件使用PascalCase + Config后缀
- 扩展函数文件使用PascalCase + Extensions后缀

#### 变量命名
- 使用camelCase
- 私有变量以下划线开头（StateFlow/MutableStateFlow）
- 常量使用UPPER_SNAKE_CASE
- 布尔变量使用is/has/should前缀

### 2. 架构规范

#### MVVM架构
- **View**: Compose UI组件，只负责UI展示和用户交互
- **ViewModel**: 处理业务逻辑，管理UI状态
- **Model**: 数据层，包括Repository、Entity、API Service

#### Repository模式
- 所有数据访问通过Repository进行
- Repository负责协调本地数据库和远程API
- 使用接口定义Repository契约
- 使用Result类型封装操作结果

#### 状态管理
- 使用StateFlow/MutableStateFlow管理状态
- UI状态变量以下划线开头的私有变量存储
- 对外暴露只读的StateFlow
- 使用sealed class定义UI状态

#### 依赖注入
- 使用Koin进行依赖注入
- 按功能模块组织Module
- 单例使用single，非单例使用factory
- ViewModel使用viewModel函数创建

### 3. API设计规范

#### Service类设计
- 继承`BaseService`基类
- 每个业务领域一个Service类
- 方法返回`RequestResult<T>`封装结果
- 使用suspend函数支持协程
- 使用Retrofit注解定义API接口

#### 请求封装
- 统一使用`RequestHelper`处理HTTP请求
- 请求参数使用数据类封装
- 响应数据使用数据类解析
- 使用拦截器添加通用请求头和参数

#### 错误处理
- 使用`RequestResult`封装成功和失败状态
- 统一的错误日志记录
- 用户友好的错误提示
- 网络错误重试机制

### 4. UI组件规范

#### Compose组件
- 组件函数使用`@Composable`注解
- 参数按照：状态、事件回调、修饰符的顺序排列
- 使用`remember`缓存计算结果
- 合理使用`LaunchedEffect`处理副作用
- 使用`derivedStateOf`计算派生状态
- 使用`rememberCoroutineScope`处理事件

#### 对话框组件
- 统一使用`AlertDialog`基础组件
- 对话框状态由父组件管理
- 提供`onDismiss`回调
- 使用`rememberDialogState`管理对话框状态

#### 主题和样式
- 使用Material3设计规范
- 统一的颜色和字体定义
- 支持深色模式
- 使用动态主题
- 定义可重用的组件样式

#### 列表性能优化
- 使用`LazyColumn`/`LazyRow`替代`Column`/`Row`
- 实现`key`参数避免不必要的重组
- 使用`rememberSaveable`保存滚动状态
- 使用`derivedStateOf`减少重组次数
- 实现分页加载

### 5. 数据库规范

#### Room数据库
- 实体类使用`@Entity`注解
- DAO接口使用`@Dao`注解
- 数据库类继承`RoomDatabase`
- 使用单例模式管理数据库实例
- 使用数据库迁移策略

#### 实体设计
- 主键使用`@PrimaryKey`注解
- 外键关系使用`@ForeignKey`定义
- 字段使用`@ColumnInfo`指定列名
- 使用`@Embedded`和`@Relation`处理复杂关系
- 使用`@TypeConverter`处理复杂类型

### 6. Hook开发规范

#### Xposed Hook
- Hook类实现`IXposedHookLoadPackage`接口
- 使用`XposedHelpers`进行方法Hook
- Hook方法要有详细的日志记录
- 异常处理要完善，避免目标应用崩溃
- 使用反射工具类简化Hook操作

#### 配置管理
- 使用`XSharedPreferences`读取配置
- 配置变更要及时同步
- 提供配置重载机制
- 使用加密存储敏感配置
- 配置分组管理

#### Hook安全
- 检查应用签名确保目标合法
- 防止Hook循环和递归调用
- 使用try-catch包装所有Hook操作
- 提供紧急关闭开关
- 实现Hook性能监控

### 7. 工具类规范

#### 工具类设计
- 使用`object`关键字创建单例工具类
- 方法要有清晰的文档注释
- 参数验证和异常处理
- 线程安全考虑
- 使用扩展函数优化API设计

#### 常用工具类
- `ClipboardUtils`: 剪贴板操作
- `DeviceInfoUtils`: 设备信息获取
- `TokenParserUtils`: 令牌解析
- `ResponseParserUtils`: 响应数据解析
- `TimeUtils`: 时间日期处理
- `EncryptionUtils`: 加密解密
- `NetworkUtils`: 网络状态检测
- `PermissionUtils`: 权限处理
- `NotificationUtils`: 通知管理
- `FileUtils`: 文件操作

### 8. 测试规范

#### 单元测试
- 使用JUnit 5编写测试
- 使用Mockk模拟依赖
- 测试覆盖率目标>80%
- 使用测试规则简化测试
- 分离测试数据和测试逻辑

#### UI测试
- 使用Compose UI测试框架
- 编写关键流程的UI测试
- 使用测试标签标记可测试元素
- 模拟用户交互测试功能

#### 权限管理
- 最小权限原则
- 运行时权限检查
- 权限说明要清晰
- 提供权限请求工具类

### 9. 性能优化

#### 内存管理
- 及时释放不需要的资源
- 避免内存泄漏
- 合理使用缓存
- 使用弱引用存储临时对象
- 大对象使用延迟加载

#### 网络优化
- 请求去重
- 合理的超时设置
- 网络状态检查
- 实现请求优先级
- 使用缓存减少请求次数
- 压缩请求和响应数据

#### UI性能
- 避免过度重组
- 合理使用LazyColumn
- 图片加载优化
- 使用Window.setDecorFitsSystemWindows优化全屏体验
- 减少布局层级

#### 启动优化
- 使用启动器模式优化启动时间
- 延迟初始化非关键组件
- 使用WorkManager处理后台任务
- 监控并优化启动性能

## 代码审查清单

### 提交前检查
- [ ] 代码符合命名规范
- [ ] 有适当的注释和文档
- [ ] 异常处理完善
- [ ] 没有硬编码的敏感信息
- [ ] 测试用例通过
- [ ] 性能影响评估
- [ ] 代码格式化符合规范
- [ ] 移除调试代码和日志

### 代码审查要点
- [ ] 架构设计合理
- [ ] 代码可读性良好
- [ ] 错误处理完善
- [ ] 安全性考虑
- [ ] 性能优化
- [ ] 测试覆盖率
- [ ] 兼容性考虑
- [ ] 可维护性评估

## 依赖管理

### 版本管理
- 使用`libs.versions.toml`统一管理依赖版本
- 定期更新依赖到稳定版本
- 避免使用快照版本
- 主要依赖版本要求：
  - Kotlin: 2.0.21+
  - Compose BOM: 2024.09.00+
  - Room: 2.5.1+
  - OkHttp: 4.12.0+
  - Ktor: 2.3.7+
  - Koin: 3.5.0+
  - Retrofit: 2.9.0+
  - Kotlinx Serialization: 1.6.0+
  - Coil: 2.4.0+

### 依赖原则
- 最小依赖原则
- 避免依赖冲突
- 及时移除不使用的依赖
- 使用KSP替代kapt提升编译性能
- 使用版本目录管理依赖版本
- 定期检查依赖安全漏洞

### 安全依赖
- Xposed API使用compileOnly引入
- 敏感依赖要进行安全审查
- 避免引入有安全漏洞的依赖版本
- 使用依赖扫描工具检查安全问题
- 使用可信源的依赖

## 安全规范

### 权限安全
- 申请最小必要权限
- 动态权限申请要有用户友好的说明
- 权限使用要有明确的业务场景
- 定期审查使用的权限

### 数据安全
- 敏感数据加密存储
- 使用安全的加密算法
- 网络传输使用HTTPS
- 避免明文存储密码和令牌
- 实现数据脱敏机制

### 代码安全
- 避免SQL注入
- 防止XSS攻击
- 使用安全的随机数生成
- 实现输入验证
- 避免使用不安全的API

## 日志规范

### 日志级别
- **DEBUG**: 调试信息，仅在开发环境输出
- **INFO**: 一般信息，如用户操作、业务流程
- **WARN**: 警告信息，如网络异常、数据异常
- **ERROR**: 错误信息，如崩溃、严重异常

### 日志内容
- 包含足够的上下文信息
- 不记录敏感信息（密码、令牌等）
- 使用结构化日志格式
- Hook日志要包含目标应用信息
- 包含时间戳和线程信息

### 日志工具
- 统一使用`HookLogUtils`进行Hook相关日志
- 普通日志使用Android Log类
- 生产环境要控制日志输出级别
- 实现日志文件轮转
- 提供日志上传功能

## 国际化规范

### 字符串资源
- 所有用户可见文本放在`strings.xml`中
- 使用有意义的key命名
- 支持中文和英文两种语言
- 错误提示信息要本地化
- 使用参数化字符串处理变量

### 资源命名
- 字符串资源使用snake_case命名
- 图标资源使用baseline_前缀
- 颜色资源使用语义化命名
- 尺寸资源使用功能命名

### 本地化流程
- 使用翻译工具辅助翻译
- 定期同步翻译资源
- 测试不同语言环境下的UI布局
- 处理不同语言的文本长度问题

## 版本控制规范

### Git提交规范
- 使用语义化提交信息
- 格式：`type(scope): description`
- 类型：feat、fix、docs、style、refactor、test、chore
- 每次提交要有明确的功能点
- 提交前运行代码格式化

### 分支管理
- main分支：稳定版本
- develop分支：开发版本
- feature分支：新功能开发
- hotfix分支：紧急修复
- release分支：版本准备

### 版本发布
- 使用语义化版本号：MAJOR.MINOR.PATCH
- 每个版本要有详细的更新日志
- 重要版本要有向后兼容性说明
- 使用标签标记发布版本
- 自动化构建和发布流程

## 文档规范

### 代码注释
- 类和方法要有KDoc注释
- 复杂业务逻辑要有行内注释
- Hook相关代码要详细说明Hook的目标和作用
- 注释要及时更新，保持与代码同步
- 使用TODO和FIXME标记待办事项

### API文档
- Service类的方法要有详细的参数和返回值说明
- 错误码要有明确的含义说明
- 请求示例和响应示例
- 维护API变更历史
- 提供API使用示例

### 用户文档
- 提供功能使用说明
- 常见问题解答
- 故障排除指南
- 版本更新说明
- 视频教程链接

## 持续集成与部署

### CI流程
- 使用GitHub Actions自动化构建
- 每次提交运行单元测试
- 代码质量检查
- 自动化UI测试
- 构建产物归档

### 发布流程
- 版本号递增
- 生成更新日志
- 构建发布包
- 发布到应用商店
- 通知用户更新

